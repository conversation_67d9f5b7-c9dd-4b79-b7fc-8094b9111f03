﻿using Lrb.Application.AutoDialer.Web.Mappings;
using Lrb.Application.Common.IVR;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.UserDetails.Web;

namespace Lrb.Application.AutoDialer.Web.Requests
{
    public class UpdateUserCallingStatusRequest : IRequest<Response<bool>>
    {
        public bool? ShouldToggleUser {  get; set; }
    }
    public class UpdateUserCallingStatusRequestHandler : IRequestHandler<UpdateUserCallingStatusRequest, Response<bool>>
    {
        public readonly ICurrentUser _currentUser;
        public readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        private readonly IRepositoryWithEvents<AutoDialerAudit> _autoDialerRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.IVRCommonCallLog> _ivrCommonCallRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> _integrationAccRepo;
        private readonly IIVRService _iVRService;

        public UpdateUserCallingStatusRequestHandler(IRepositoryWithEvents<AutoDialerAudit> autoDialerRepo, ICurrentUser currentUser,
            IIVRService iVRService, IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> integrationAccRepo,
            IRepositoryWithEvents<Domain.Entities.IVRCommonCallLog> ivrCommonCallRepo, 
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo)
        {
            _autoDialerRepo = autoDialerRepo;
            _iVRService = iVRService;
            _integrationAccRepo = integrationAccRepo;
            _ivrCommonCallRepo = ivrCommonCallRepo;
            _currentUser = currentUser;
            _userDetailsRepo = userDetailsRepo;
        }
        public async Task<Response<bool>> Handle(UpdateUserCallingStatusRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var userId = _currentUser.GetUserId();
                var userDetails = await _userDetailsRepo.FirstOrDefaultAsync(new GetUsersSpec(new List<Guid> { userId }));
                if (userDetails != null)
                {
                    if (request.ShouldToggleUser ?? false)
                    {
                        userDetails.IsAvilableForCall = !userDetails.IsAvilableForCall ?? false;

                        await _userDetailsRepo.UpdateAsync(userDetails);
                    }
                    if (userDetails.IsAvilableForCall == true)
                    {
                        var data = await AutoDailerHelper.InitiateAutoCall(userId, _autoDialerRepo, _integrationAccRepo, _ivrCommonCallRepo, _iVRService);
                    }
                    return new(true);
                }
                else
                {
                    return new(false,"Invalid user details");
                }
            }
            catch (Exception ex)
            {
                return new(false);
            }
        }
    }
}
