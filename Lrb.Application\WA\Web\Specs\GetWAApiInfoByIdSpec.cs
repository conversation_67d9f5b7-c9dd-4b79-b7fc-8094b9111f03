﻿namespace Lrb.Application.WA.Web
{
    public class GetWAApiInfoByIdSpec : Specification<WAApiInfo>
    {
        public GetWAApiInfoByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }

    public class GetWAApiInfoSpec : Specification<WAApiInfo>
    {
        public GetWAApiInfoSpec(bool shouldUseForTextMessaging)
        {
            Query.Where(i => !i.IsDeleted && i.ShouldUseForTextMessaging == shouldUseForTextMessaging);
        }
        public GetWAApiInfoSpec()
        {
            Query.Where(i => !i.IsDeleted)
                .OrderByDescending(i => i.CreatedOn);
        }
    }

    public class GetWAApiInfoByWaApiActionSpec : Specification<WAApiInfo> 
    {
        public GetWAApiInfoByWaApiActionSpec(WAApiAction wAApiAction)
        {
            Query.Where(i => !i.IsDeleted && i.WAApiAction == wAApiAction);
        }
    }
}
