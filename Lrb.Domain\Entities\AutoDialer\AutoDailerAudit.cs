﻿namespace Lrb.Domain.Entities
{
    public class AutoDialerAudit : UserLevelAuditableEntity, IAggregateRoot
    {
        public Lead? Lead { get; set; }
        public Guid? LeadId { get; set; }
        public string? CallTo { get; set; }
        public string? CallFrom { get; set; }
        public Guid? AssignTo { get; set; }
        public IVRCallStatus? CallStatus { get; set; } = IVRCallStatus.InQueue;
        public Guid? IVRCommonCallLogId { get; set; }
        public IVRCommonCallLog? IVRCommonCallLog { get; set; }
        public int? OrderRank { get; set; }
        public int? Priority { get; set; } = 1;
        public DateTime? CallStarted { get; set; }
    }
}