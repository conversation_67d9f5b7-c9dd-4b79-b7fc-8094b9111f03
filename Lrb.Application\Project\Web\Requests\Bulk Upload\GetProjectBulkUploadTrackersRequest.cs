﻿namespace Lrb.Application.Project.Web.Requests.Bulk_Upload
{
    public class GetProjectBulkUploadTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkProjectUploadTracker, string>>
    {
    }
    public class GetProjectBulkUploadTrackersRequestHandler : IRequestHandler<GetProjectBulkUploadTrackersRequest, PagedResponse<BulkProjectUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkProjectUploadTracker> _trackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetProjectBulkUploadTrackersRequestHandler(IRepositoryWithEvents<BulkProjectUploadTracker> trackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _trackerRepo = trackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<BulkProjectUploadTracker, string>> Handle(GetProjectBulkUploadTrackersRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _trackerRepo.ListAsync(new GetAllBulkProjectTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new GetBulkProjectTrackerCountSpec(subIds), cancellationToken);
            return new(trackers, totalCount);
        }
    }
}
