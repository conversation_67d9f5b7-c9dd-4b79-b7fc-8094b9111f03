﻿using Lrb.Application.ExportTemplate;
using Lrb.Application.Identity.Users;
using Newtonsoft.Json;

namespace Lrb.Application.Lead.Web
{
    public class GetAllExportLeadTracker : PaginationFilter, IRequest<PagedResponse<ExportLeadTrackerDto, string>>
    {
    }
    public class GetAllExportLeadTrackerHandler : IRequestHandler<GetAllExportLeadTracker, PagedResponse<ExportLeadTrackerDto, string>>
    {
        private readonly IReadRepository<ExportLeadTracker> _exportLeadTrackerRepo;
        public readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllExportLeadTrackerHandler(IReadRepository<ExportLeadTracker> exportLeadTrackerRepo,
            IUserService userService, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _exportLeadTrackerRepo = exportLeadTrackerRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<ExportLeadTrackerDto, string>> Handle(GetAllExportLeadTracker request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var leadTrackers = await _exportLeadTrackerRepo.ListAsync(new GetExportLeadTrackersSpec(request, subIds), cancellationToken);
            var totalCount = await _exportLeadTrackerRepo.CountAsync(new GetExportLeadTrackersCountSpec(subIds), cancellationToken);
            var leadTrackerDto = leadTrackers.Adapt<List<ExportLeadTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(leadTrackerDto.Select(i => i.CreatedBy.ToString()).Distinct().ToList(), cancellationToken);
            foreach (var leadTracker in leadTrackerDto)
            {
                leadTracker.ExportTemplate = (JsonConvert.DeserializeObject<ViewExportTemplateDto>(leadTracker?.Template ?? string.Empty) ?? null);
                leadTracker.ExportedUser = users?.FirstOrDefault(i => leadTracker.CreatedBy == i.Id)?.Adapt<Team.Web.UserDto>();
            }
            return new(leadTrackerDto, totalCount);
        }
    }
}
