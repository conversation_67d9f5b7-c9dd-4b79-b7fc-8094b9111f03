﻿using Lrb.Application.Agency.Web.Requests;
using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.Agency.Web
{
    public class AgencyTrackerSpec : EntitiesByPaginationFilterSpec<BulkMarketingAgencyUploadTracker>
    {
        public AgencyTrackerSpec(GetAllBulkAgencyTrackersRequest filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.BulkUploadType == MarketingBulkUploadType.Agency && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class AgencyTrackerCountSpec : Specification<BulkMarketingAgencyUploadTracker>
    {
        public AgencyTrackerCountSpec( List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && i.BulkUploadType == MarketingBulkUploadType.Agency && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetExportAgencyTrackersSpec : EntitiesByPaginationFilterSpec<ExportMarketingTracker>
    {
        public GetExportAgencyTrackersSpec(GetAllExportAgencyTrackers filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.ExportType == MarketingExportType.ExportAgency && subIds.Contains(i.CreatedBy)).OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportAgencyTrackersCountSpec : Specification<ExportMarketingTracker>
    {
        public GetExportAgencyTrackersCountSpec( List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && i.ExportType == MarketingExportType.ExportAgency && subIds.Contains(i.CreatedBy));
        }
    }
}
