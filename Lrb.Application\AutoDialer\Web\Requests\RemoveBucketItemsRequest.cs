﻿using Lrb.Application.AutoDialer.Web.Specs;

namespace Lrb.Application.AutoDialer.Web.Requests
{
    public class RemoveBucketItemsRequest : IRequest<Response<bool>>
    {
        public List<Guid?>? LeadIds { get; set; }
    }
    public class RemoveBucketItemsRequestHandler : IRequestHandler<RemoveBucketItemsRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<AutoDialerAudit> _autoDialerRepo;
        public RemoveBucketItemsRequestHandler(IRepositoryWithEvents<AutoDialerAudit> autoDialerRepo)
        {
            _autoDialerRepo = autoDialerRepo;
        }

        public async Task<Response<bool>> Handle(RemoveBucketItemsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request.LeadIds?.Any() ?? false)
                {
                    var audits = await _autoDialerRepo.ListAsync(new GetAutoDialerAuditsByLeadIdsSpec(request.LeadIds));
                    await _autoDialerRepo.SoftDeleteRangeAsync(audits);
                    return new(true);
                }
                return new(false, "Invalid request");
            }
            catch (Exception ex) 
            {
                return new(false, ex.Message);
            }
        }
    }
}
