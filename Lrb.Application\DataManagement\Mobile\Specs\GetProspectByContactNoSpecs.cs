﻿namespace Lrb.Application.DataManagement.Mobile.Specs
{
    public class GetProspectByContactNoSpecs : Specification<Lrb.Domain.Entities.Prospect>
    {
        public GetProspectByContactNoSpecs(string contactNo)
        {
            Query.Where(i => i.ContactNo.Contains(contactNo) && !i.IsDeleted);
        }
        public GetProspectByContactNoSpecs(string contactNo, string countryCode)
        {
            if(contactNo != null && countryCode != null)
            {
                string formattedContactNo = contactNo.StartsWith(countryCode) ? contactNo.Substring(countryCode.Length) : countryCode + contactNo;

                Query.Where(i => !i.IsDeleted &&
                            !i.IsArchived &&
                            (i.ContactNo != null && (i.ContactNo == formattedContactNo))
                            ||
                            (i.AlternateContactNo != null &&  (i.AlternateContactNo == formattedContactNo)));
            }
        }
    }
}
