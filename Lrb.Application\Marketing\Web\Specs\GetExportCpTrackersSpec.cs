﻿using Lrb.Application.ChannelPartner.Web.Request;
using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.Marketing.Web.Specs
{
    public class GetExportCpTrackersSpec : EntitiesByPaginationFilterSpec<ExportMarketingTracker>
    {
        public GetExportCpTrackersSpec(GetAllExportCpTracker filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.ExportType == MarketingExportType.ExportChannelPartner && subIds.Contains(i.CreatedBy)).OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportCpTrackersCountSpec : Specification<ExportMarketingTracker>
    {
        public GetExportCpTrackersCountSpec(List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && i.ExportType==MarketingExportType.ExportChannelPartner && subIds.Contains(i.CreatedBy));
        }
    }
}
