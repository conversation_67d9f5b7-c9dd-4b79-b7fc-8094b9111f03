﻿using Lrb.Application.AutoDialer.Web.Dtos;
using Lrb.Application.AutoDialer.Web.Specs;

namespace Lrb.Application.AutoDialer.Web.Requests
{
    public class GetAllBucketLeadsRequest : PaginationFilter, IRequest<PagedResponse<AutoDialerLeadDto, string>>
    {
        public string? SearchByNameAndNumber { get; set; }
        public Guid? CurrentUserId { get; set; }
        public IVRCallStatus? CallStatus { get; set; }
        public Guid? FilterStatusId { get; set; }
        public List<Guid>? UserIds { get; set; }
    }
    public class GetAllBucketLeadsRequestHandler : IRequestHandler<GetAllBucketLeadsRequest, PagedResponse<AutoDialerLeadDto, string>>
    {
        private readonly IRepositoryWithEvents<AutoDialerAudit> _autoDialerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllBucketLeadsRequestHandler(IRepositoryWithEvents<AutoDialerAudit> autoDialerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _autoDialerRepo = autoDialerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<AutoDialerLeadDto, string>> Handle(GetAllBucketLeadsRequest request, CancellationToken cancellationToken)
        {
            try
            {
                request.CurrentUserId = request.CurrentUserId ?? _currentUser.GetUserId();
                var tenant = _currentUser.GetTenant();
                List<Guid> UserIds = new();
                var isAdmin = _dapperRepository.IsAdminAsync(request.CurrentUserId ?? _currentUser.GetUserId(), tenant ?? _currentUser.GetTenant()).Result;
                if (!isAdmin)
                {
                    UserIds = (await _dapperRepository.GetSubordinateIdsAsync(request.CurrentUserId ?? Guid.Empty, tenant, false)).ToList();
                }
                var leads = await _autoDialerRepo.ListAsync(new GetAutoDialerAuditSpec(request, isAdmin, UserIds));
                var leadsCount = await _autoDialerRepo.CountAsync(new GetAutoDialerAuditSpec(request, isAdmin, UserIds));
                if (leadsCount != 0)
                {
                    List<AutoDialerLeadDto> leadDto = leads.Adapt<List<AutoDialerLeadDto>>();
                    return new(leadDto, leadsCount);
                }
                return new();
                //var results = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<AutoDialerAuditWithCountDto>(
                //    "LeadratBlack",
                //    "get_auto_dialer_audits",
                //    new
                //    {
                //        p_tenant_id = tenant,
                //        p_is_admin = isAdmin,
                //        p_user_ids = UserIds ?? new List<Guid> { request.CurrentUserId ?? _currentUser.GetUserId() },
                //        p_current_user_id = request.CurrentUserId,
                //        p_search = request.SearchByNameAndNumber,
                //        p_call_status = request.CallStatus,
                //        p_status_filter_name = request.StatusFilterName,
                //        p_lead_user_ids = request.UserIds ?? new List<Guid>(),
                //        p_page_number = request.PageNumber,
                //        p_page_size = request.PageSize
                //    })).ToList();

                //var totalCount = results.FirstOrDefault()?.TotalCount ?? 0;
                //var autoDialerAudits = results.Cast<AutoDialerAuditDto>().ToList();
            }
            catch (Exception ex)
            {
                return new(ex.Message);
            }
        }

    }
    public class AutoDialerAuditWithCountDto : AutoDialerAuditDto
    {
        public long TotalCount { get; set; }
    }
}