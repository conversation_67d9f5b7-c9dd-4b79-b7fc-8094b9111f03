﻿using Lrb.Application.Automation.Dtos;
using Lrb.Application.Integration.Web.Requests;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Integration.Web
{
    public class IntegrationAccInfoSpec : EntitiesByPaginationFilterSpec<IntegrationAccountInfo>
    {
        public IntegrationAccInfoSpec(GetAllIntegrationAccountRequest filter) : base(filter)
        {
            Query.Where(i => !i.IsDeleted)
                .Include(i => i.IVRApiConfiguration);
            if (filter.LeadSource != null && filter.LeadSource != default)
            {
                Query.Where(i => i.LeadSource == filter.LeadSource);

                if (filter.LeadSource == LeadSource.Facebook)
                {
                    Query.Where(i => i.FacebookAccountId == Guid.Empty);
                }

                if (filter.LeadSource == LeadSource.GoogleAds)
                {
                    Query.Where(i => i.GoogleadLeadFormId == Guid.Empty);
                }
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchByName))
            {
                Query.Where(i => i.AccountName.ToLower().Trim().Contains(filter.SearchByName.ToLower().Trim()));
            }
            Query.OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class IntegrationAccountInfoBySourceSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccountInfoBySourceSpec(IntegrationAccountInfo filter)
        {
            Query.Where(i => !i.IsDeleted);
            Query.Where(i => i.LeadSource == filter.LeadSource);
        }
        public IntegrationAccountInfoBySourceSpec(LeadSource source)
        {
            Query.Where(i => !i.IsDeleted);
            Query.Where(i => i.LeadSource == source)
                .Include(i => i.Assignment)
                .ThenInclude(i => i.Project)
                .Include(i => i.Assignment)
                .ThenInclude(i => i.Location)
                .Include(i => i.IVRApiConfiguration);
        }
    }
    public class IntegrationAccountInfoByIdSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccountInfoByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id).Include(i => i.WebhookPayloadMapping);
        }
    }
    public class IntegrationAccountInfoByPrimaryStatusSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccountInfoByPrimaryStatusSpec(LeadSource source)
        {
            Query.Where(i => !i.IsDeleted && i.LeadSource == source && i.IsPrimary);
        }
    }
    public class IntegrationAccountInfoByVirtualNumberSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccountInfoByVirtualNumberSpec(LeadSource source)
        {
            Query.Include(i => i.IVRApiConfiguration)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                    .Include(i => i.IVROutboundConfiguration)
                    .Include(i => i.Agency);
            Query.Where(i => !i.IsDeleted && i.LeadSource == source && i.IVROutboundConfiguration != null);
        }
        public IntegrationAccountInfoByVirtualNumberSpec(LeadSource source, IVRServiceProvider serviceProvider)
        {
            Query.Include(i => i.IVRApiConfiguration)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                .Include(i => i.Agency);
            Query.Where(i => !i.IsDeleted && i.LeadSource == source && i.IVRApiConfiguration != null && i.IVRApiConfiguration.IsPrimary);
            Query.Where(i => i.IVRApiConfiguration.IVRServiceProvider == serviceProvider);
        }
    }
    public class IntegrationAccCountSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccCountSpec(GetAllIntegrationAccountRequest filter)
        {
            Query.Where(i => !i.IsDeleted);
            if (filter.LeadSource != null && filter.LeadSource != default)
            {
                Query.Where(i => i.LeadSource == filter.LeadSource);

                if (filter.LeadSource == LeadSource.GoogleAds)
                {
                    Query.Where(i => i.GoogleadLeadFormId == Guid.Empty);
                }
                if (filter.LeadSource == LeadSource.Facebook)
                {
                    Query.Where(i => i.FacebookAccountId == Guid.Empty);
                }
            }
            if (!string.IsNullOrWhiteSpace(filter.SearchByName))
            {
                Query.Where(i => i.AccountName.ToLower().Trim().Contains(filter.SearchByName.ToLower().Trim()));
            }
        }
    }
    public class GoogleAdIntegrationSpec : Specification<GoogleAdLeadFormIntegrationInfo>
    {
        public GoogleAdIntegrationSpec(GetGoogleAdIntegrationAccountRequest request)
        {
            Query.Where(i => !i.IsDeleted);
            if (!string.IsNullOrWhiteSpace(request.SearchByName))
            {
                Query.Where(i => i.AccountName.ToLower().Trim().Contains(request.SearchByName.ToLower().Trim()));
            }
            Query.OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GoogleAdIntegrationCountSpec : Specification<GoogleAdLeadFormIntegrationInfo>
    {
        public GoogleAdIntegrationCountSpec(GetGoogleAdIntegrationAccountRequest request)
        {
            Query.Where(i => !i.IsDeleted);
            if (!string.IsNullOrWhiteSpace(request.SearchByName))
            {
                Query.Where(i => i.AccountName.ToLower().Trim().Contains(request.SearchByName.ToLower().Trim()));
            }
        }
    }
    public class GoogleAdIntegrationByIdsSpec : Specification<GoogleAdLeadFormIntegrationInfo>
    {
        public GoogleAdIntegrationByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
    }
    public class IntegrationAccInfoByGoogleAdIdOrId : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccInfoByGoogleAdIdOrId(Guid googleAdId)
        {
            Query
                .Include(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Agency)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Agency)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.ChannelPartner)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Property)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Campaign)
                .Where(i => !i.IsDeleted && (i.GoogleadLeadFormId == googleAdId || i.Id == googleAdId));

        }
        public IntegrationAccInfoByGoogleAdIdOrId(List<Guid> googleAdIds)
        {
            Query
                .Include(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                        .Include(i => i.Agency)
                .Where(i => !i.IsDeleted && (googleAdIds.Contains(i.GoogleadLeadFormId) || googleAdIds.Contains(i.Id)));
        }
    }

    public class IntegrationAccInfoByFacebookIdSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccInfoByFacebookIdSpec(Guid fbId)
        {
            Query
                .Include(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Where(i => !i.IsDeleted && (i.FacebookAccountId == fbId));
        }
        public IntegrationAccInfoByFacebookIdSpec(List<Guid> fbIds)
        {
            Query
                .Include(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Where(i => !i.IsDeleted && fbIds.Contains(i.FacebookAccountId));
        }
    }
    public class IntegrationAccountInfoByIdsSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccountInfoByIdsSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id)).Include(i => i.Agency);
        }
    }
    public class AssignedIntegrationAccountsByIdSpec : Specification<IntegrationAccountInfo, BaseAssignedEntityDto>
    {
        public AssignedIntegrationAccountsByIdSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id));
        }
        public AssignedIntegrationAccountsByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && id == i.Id);
        }
    }
    public class AssignedFacebookIntegrationAccountsByIdSpec : Specification<IntegrationAccountInfo, AssignedFacebookIntegrationAccDto>
    {
        public AssignedFacebookIntegrationAccountsByIdSpec(List<Guid> ids)
        {
            Query.Where(i => !i.IsDeleted && ids.Contains(i.Id) && i.FacebookAccountId != default);
        }
        public AssignedFacebookIntegrationAccountsByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && id == i.Id);
        }
    }
    public class IntegrationAccountInfoByAutomationIdsSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccountInfoByAutomationIdsSpec(List<Guid> ids)
        {
            Query.Where(i => ids.Contains(i.AutomationId ?? Guid.Empty) && !i.IsDeleted && i.FacebookAccountId != default);
        }
    }

    public class IntegrationAccountByIdSpec : Specification<IntegrationAccountInfo>
    {
        public IntegrationAccountByIdSpec(Guid id)
        {
            Query
                .Include(i => i.UserAssignment)
                    .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                        .ThenInclude(i => i.Project)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                        .ThenInclude(i => i.Location)
                .Include(i => i.IVRApiConfiguration)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Agency)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.ChannelPartner)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Property)
                .Include(i => i.Assignment)
                        .ThenInclude(i => i.Campaign)
                .Where(i => i.Id == id && !i.IsDeleted);
        }
        public IntegrationAccountByIdSpec(List<Guid> ids)
        {
            Query
                .Include(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                        .ThenInclude(i => i.Project)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                        .ThenInclude(i => i.Location)
                .Include(i => i.IVRApiConfiguration)
                .Where(i => ids.Contains(i.Id) && !i.IsDeleted);
        }
    }

    public class GetIntegrationAccountOfMicrosite : Specification<IntegrationAccountInfo>
    {
        public GetIntegrationAccountOfMicrosite(LeadSource source)
        {
            Query.Where(i => i.LeadSource == source);
        }
    }

    public class IVRPrimaryAccountSpecs : Specification<IntegrationAccountInfo>
    {
        public IVRPrimaryAccountSpecs()
        {
            Query.Where(i => i.LeadSource == LeadSource.IVR && i.IsPrimary && !i.IsDeleted && i.Credentials != null);
        }
    }

    public class GetIntegrationAccountInfoWithFilterSpecs : Specification<IntegrationAccountInfo>
    {
        public GetIntegrationAccountInfoWithFilterSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.IntegrationFilterInfos);
        }
    }

    public class GetIntegrationAccountOfProjectMicrosite : Specification<IntegrationAccountInfo>
    {
        public GetIntegrationAccountOfProjectMicrosite(LeadSource source)
        {
            Query.Where(i => i.LeadSource == source);
        }
    }

    public class GetIntegrationAccountInfoWithIVRConfigSpec : Specification<IntegrationAccountInfo>
    {
        public GetIntegrationAccountInfoWithIVRConfigSpec(Guid id)
        {
            Query
                .Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                        .ThenInclude(i => i.Project)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                        .ThenInclude(i => i.Location)
                .Include(i => i.IVRApiConfiguration)
                .Include(i => i.IVROutboundConfiguration)
                .Include(i => i.IVRPayloadMapping)
                /*.Include(i => i.WebhookPayloadMapping)*/;
        }
        public GetIntegrationAccountInfoWithIVRConfigSpec()
        {
            Query
                .Where(i => !i.IsDeleted && i.LeadSource == LeadSource.IVR)
                .Include(i => i.UserAssignment)
                  .ThenInclude(i => i.UserAssignmentConfigurations)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                        .ThenInclude(i => i.Project)
                .Include(i => i.IVRAssignments)
                    .ThenInclude(i => i.Assignment)
                        .ThenInclude(i => i.Location)
                .Include(i => i.IVRApiConfiguration)
                .Include(i => i.IVROutboundConfiguration)
                .Include(i => i.IVRPayloadMapping);
        }
    }

    public class GetIntegrationAccInfoWithAgencySpec : Specification<IntegrationAccountInfo>
    {
        public GetIntegrationAccInfoWithAgencySpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.Agency);
        }
    }
    public class GetWebhookIntegrationAccInfoWithAgencySpec : Specification<IntegrationAccountInfo>
    {
        public GetWebhookIntegrationAccInfoWithAgencySpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.Agency)
                .Include(i => i.WebhookPayloadMapping);
        }
    }


    public class GetWebhookPayloadmappingSpec : Specification<WebhookPayloadMapping>
    {
        public GetWebhookPayloadmappingSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }

    public class GetIntegrationAccountInfoWithWASpec : Specification<IntegrationAccountInfo>
    {
        public GetIntegrationAccountInfoWithWASpec()
        {
            Query.Where(i => !i.IsDeleted && i.IsPrimary && i.LeadSource == LeadSource.WhatsApp)
                .Include(i => i.WAApiInfo.Where(i => (i.WAApiAction == WAApiAction.SendTextWithMediaMessage || i.WAApiAction == WAApiAction.SendTextMessage)))
                .Include(i => i.WAPayloadMapping);
        }
        public GetIntegrationAccountInfoWithWASpec(Guid id)
        {
            Query.Where(i => i.Id == id && !i.IsDeleted && i.LeadSource == LeadSource.WhatsApp)
                .Include(i => i.WAApiInfo)
                .Include(i => i.WAPayloadMapping);
        }
    }
    public class GetAllIntegrationAccountInfoWithWASpec : Specification<IntegrationAccountInfo>
    {
        public GetAllIntegrationAccountInfoWithWASpec()
        {
            Query.Where(i => !i.IsDeleted && i.LeadSource == LeadSource.WhatsApp)
                .Include(i => i.WAApiInfo)
                .Include(i => i.WAPayloadMapping);
        }
        public GetAllIntegrationAccountInfoWithWASpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.LeadSource == LeadSource.WhatsApp && i.Id == id)
                .Include(i => i.WAApiInfo)
                .Include(i => i.WAPayloadMapping);
        }
    }
    public class GetIntegrationAccountInfoByIdSpec : Specification<IntegrationAccountInfo>
    {
        public GetIntegrationAccountInfoByIdSpec(Guid id)
        {
            Query
                .Where(i => !i.IsDeleted && i.Id == id)
                .Include(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Location)
                        .ThenInclude(i => i.UserAssignment)
                .Include(i => i.Assignment)
                    .ThenInclude(i => i.Project)
                    .ThenInclude(i => i.UserAssignment)
                .Include(i => i.IVRApiConfiguration)
                .Include(i => i.IVROutboundConfiguration)
                .Include(i => i.IVRPayloadMapping)
              /*  .Include(i => i.WebhookPayloadMapping)*/;
        }
    }
    public class GoogleAdLeadFormDataSpec : Specification<GoogleAdLeadFormData>
    {
        public GoogleAdLeadFormDataSpec(List<string>? apiKeys)
        {
            Query
                .Where(i => !i.IsDeleted)
                .Where(i => apiKeys.Contains(i.Googlekey ?? string.Empty));
        }
    }

    public class GoogleAdsAccountsSpec : Specification<IntegrationAccountInfo>
    {
        public GoogleAdsAccountsSpec(LeadSource source)
        {
            Query.Where(i => !i.IsDeleted && i.LeadSource == source);

        }
        public GoogleAdsAccountsSpec(Guid? Id)
        {
            Query.Where(i => !i.IsDeleted && i.GoogleadLeadFormId == Id);

        }
    }

    public class GetIntegrationAccountInfoWithOutBoundSpec : Specification<IntegrationAccountInfo>
    {
        public GetIntegrationAccountInfoWithOutBoundSpec()
        {
            Query
                .Where(i => !i.IsDeleted && i.IsPrimary && i.IVRCallType == IVRType.Outbound)
                .Include(i => i.IVROutboundConfiguration);
        }
    }
}
