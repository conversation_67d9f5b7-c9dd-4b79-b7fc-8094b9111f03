﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.UserDetails.Web.Dtos
{
    public class FullUserViewDto : IDto
    {
        private List<UserRolePermission>? rolePermission;
        public Guid Id { get; set; }
        public string UserName { get; set; } = default!;
        public string FirstName { get; set; } = default!;
        public string LastName { get; set; } = default!;
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public bool IsActive { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? AltEmail { get; set; }
        public string? UserRoles { get; set; }
        public string? RolePermission { get; set; }
        public bool IsDeleted { get; set; }
        public Guid CreatedBy { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid LastModifiedBy { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? DeletedOn { get; set; }
        public Guid? DeletedBy { get; set; }
        public bool IsAdmin { get; set; }
        public string? Subordinates { get; set; }

        [NotMapped]
        public List<UserRolePermission>? RolePermissionConverted =>
            !string.IsNullOrWhiteSpace(RolePermission)
                ? JsonConvert.DeserializeObject<List<UserRolePermission>>(RolePermission)
                : null;
        [NotMapped]
        public List<Guid>? SubordinateIds =>
            !string.IsNullOrWhiteSpace(Subordinates)
                ? JsonConvert.DeserializeObject<List<Guid>>(Subordinates)
                : new List<Guid>();
    }
}
