﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Application.QRFormTemplate.Web.Specs;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Lead.Web
{
    public class CreateLeadRequest : CreateLeadDto, IRequest<Response<Guid>>
    {
        public virtual Guid CreatedBy { get; set; }
        public virtual Guid LastModifiedBy { get; set; }
        public DateTime CreatedOn { get; private set; }
        public DateTime? LastModifiedOn { get; set; }
        public Guid? TemplateId { get; set; }
        public Guid? LocationId { get; set; }

    }
    public class CreateLeadRequestHandler : LeadCommonRequestHandler, IRequestHandler<CreateLeadRequest, Response<Guid>>
    {
        public CreateLeadRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(CreateLeadRequestHandler).Name, "Handle")
        {
        }

        public async Task<Response<Guid>> Handle(CreateLeadRequest request, CancellationToken cancellationToken)
        {
            try
            {
               
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var contactNos = await ValidateContactNoAsync(request.ContactNo, request.AlternateContactNo,globalSettings, cancellationToken);
                request.ContactNo = contactNos.ContactNo;
                request.AlternateContactNo = contactNos.AltContactNo;
                
                //MasterPropertyType? propertyType = await ValidatePropertyTypeAsync(request.Enquiry?.PropertyTypeId, cancellationToken);
                var propertyTypes = await ValidatePropertyTypesAsync(request?.Enquiry?.PropertyTypeIds, cancellationToken);


                Address? address = await CreateAddressAsync(request.Enquiry?.Address, cancellationToken);

                List<Address>? addresses = await CreateAddressesAsync(request.Enquiry?.Addresses, cancellationToken);

                List<Domain.Entities.Lead> duplicateLeads = await SearchForDuplicateLeadsAsync(request.Adapt<DuplicateLeadSpecDto>(), cancellationToken);

                Address? customerAddress = await CreateAddressAsync(request.Address, cancellationToken);

                Domain.Entities.Lead lead = request.Adapt<Domain.Entities.Lead>();

                await InitializeLeadAsync(lead, await CreateLeadTagsAsync(request.LeadTags), customerAddress, cancellationToken);


                await SetLeadEnquiryAsync(lead, request.Enquiry, address, propertyTypes?.FirstOrDefault(), addresses, request.TemplateId,cancellationToken, propertyTypes, globalSettings);
                //await UpdateLeadSourceInfoAsync(lead.Enquiries[0], cancellationToken);

                await SetLeadProjectsAsync(lead, request.ProjectsList, globalSettings, cancellationToken);

                await SetLeadAssignedToAsync(request.TemplateId, request.ProjectId, request.LocationId, lead, globalSettings, default);

                await SetLeadPropertiesAsync(lead, request.PropertiesList, globalSettings, cancellationToken);

                await UpdateDuplicateVersionAsync(lead, cancellationToken: cancellationToken);

                await SetChannelPartnersAsync(lead, request.ChannelPartnerList, request.TemplateId, cancellationToken);

                await SetQRSubSourceAsync(lead, request.TemplateId, cancellationToken);

                await SetLeadAgencyAsync(lead, request.Agencies?.Where(i => !string.IsNullOrWhiteSpace(i.Name)).Select(i => i.Name).ToList() ?? default, request.AgencyName?.Trim() ?? string.Empty, request.TemplateId, cancellationToken);
                await SetLeadCampaignAsync(lead, request.Campaigns?.Where(i => !string.IsNullOrWhiteSpace(i.Name)).Select(i => i.Name).ToList() ?? default,  request.TemplateId, cancellationToken);
                
                await UpdateLeadCustomFlagsByIdAsync(lead, request.TemplateId ,cancellationToken);
                //await SetQRAgencyName(lead, request.TemplateId, cancellationToken);

                lead = await _leadRepo.AddAsync(lead);
                await UpdateLeadHistoryAsync(lead, cancellationToken: cancellationToken);

                //await SendLeadForAutoAssignment(lead, cancellationToken);

                await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithHistory);

                await SendLeadAssignmentNotificationsAsync(lead,globalSettings, cancellationToken);

                return new Response<Guid>(lead.Id);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(CreateLeadRequestHandler).Name} - Handle()");
                return new(Guid.Empty, ex.Message);
            }
        }
    }
}
