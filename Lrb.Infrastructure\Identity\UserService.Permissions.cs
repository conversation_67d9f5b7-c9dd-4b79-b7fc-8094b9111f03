﻿using Lrb.Application.Common.Caching;
using Lrb.Application.Common.Exceptions;
using Lrb.Application.Identity.Roles;
using Lrb.Shared.Authorization;
using Mapster;
using Microsoft.EntityFrameworkCore;

namespace Lrb.Infrastructure.Identity;

internal partial class UserService
{
    public async Task<List<string>> GetPermissionsAsync(string userId, string TenantId, CancellationToken cancellationToken)
    {
        //var user = await _userManager.FindByIdAsync(userId);
        if (!Guid.TryParse(userId, out var guidUserId))
            throw new UnauthorizedException("Invalid user ID format.");
        var user = await _dapperRepository.GetFullUserByTenantAsync(guidUserId, TenantId, cancellationToken);
        _ = user ?? throw new UnauthorizedException("Authentication Failed.");

        //var userRoles = await _userManager.GetRolesAsync(user);
        //var permissionss = new List<string>();
        //foreach (var role in await _roleManager.Roles
        //    .Where(r => userRoles.Contains(r.Name))
        //    .ToListAsync(cancellationToken))
        //{
        //    permissionss.AddRange(await _db.RoleClaims
        //        .Where(rc => rc.RoleId == role.Id && rc.ClaimType == LrbClaims.Permission)
        //        .Select(rc => rc.ClaimValue)
        //        .ToListAsync(cancellationToken));
        //}
        //var permissionsss = permissionss.Distinct().ToList();

        var permissions = user.RolePermissionConverted?
                .SelectMany(i => i.Permissions ?? new List<string>())
                .Where(name => !string.IsNullOrWhiteSpace(name)).Distinct()
                .ToList() ?? new();
        return permissions;
    }
    public async Task<List<RolePermissionDto>> GetRolesWithPermissionsAsync(string userId, CancellationToken cancellationToken)
    {
        List<RolePermissionDto> rolePermissions = new();
        var user = await _userManager.FindByIdAsync(userId);
        _ = user ?? throw new UnauthorizedException("Authentication Failed.");
        var userRoles = await _userManager.GetRolesAsync(user);

        foreach (var role in await _roleManager.Roles
            .Where(r => userRoles.Contains(r.Name))
            .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
            .ToListAsync(cancellationToken))
        {
            var rolePermissionDto = role.Adapt<RolePermissionDto>();
            rolePermissionDto.Enabled = true; // await _userManager.IsInRoleAsync(user, role.Name);
            rolePermissionDto.Permissions = (await _db.RoleClaims
                .Where(rc => rc.RoleId == role.Id && rc.ClaimType == LrbClaims.Permission)
                .Select(rc => rc.ClaimValue)
                .ToListAsync(cancellationToken));
            rolePermissions.Add(rolePermissionDto);

        }
        return rolePermissions;
    }
    public List<RolePermissionDto> GetRolesWithPermissions(string userId)
    {
        List<RolePermissionDto> rolePermissions = new();
        var user = _userManager.FindByIdAsync(userId).Result;
        _ = user ?? throw new UnauthorizedException("Authentication Failed.");
        var userRoles = _userManager.GetRolesAsync(user).Result;

        foreach (var role in _roleManager.Roles.OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
            .Where(r => userRoles.Contains(r.Name))
            .ToList())
        {
            var rolePermissionDto = role.Adapt<RolePermissionDto>();
            rolePermissionDto.Enabled = true;
            rolePermissionDto.Permissions = _db.RoleClaims
                .Where(rc => rc.RoleId == role.Id && rc.ClaimType == LrbClaims.Permission)
                .Select(rc => rc.ClaimValue)
                .ToList();
            rolePermissions.Add(rolePermissionDto);

        }
        return rolePermissions;
    }

    public async Task<bool> HasPermissionAsync(string userId,string tenantId, string permission, CancellationToken cancellationToken)
    {
        var permissions = await _cache.GetOrSetAsync(
            _cacheKeys.GetCacheKey(LrbClaims.Permission, userId),
            () => GetPermissionsAsync(userId, tenantId, cancellationToken), _logger,
            cancellationToken: cancellationToken);
        return permissions?.Contains(permission) ?? false;
    }

    public Task InvalidatePermissionCacheAsync(string userId, CancellationToken cancellationToken) =>
        _cache.RemoveAsync(_cacheKeys.GetCacheKey(LrbClaims.Permission, userId), cancellationToken);
}
//internal partial class MobileUserService
//{
//    public async Task<List<string>> GetPermissionsAsync(string userId, CancellationToken cancellationToken)
//    {
//        var user = await _userManager.FindByIdAsync(userId);

//        _ = user ?? throw new UnauthorizedException("Authentication Failed.");

//        var userRoles = await _userManager.GetRolesAsync(user);
//        var permissions = new List<string>();
//        foreach (var role in await _roleManager.Roles
//            .Where(r => userRoles.Contains(r.Name))
//            .OrderByDescending(i => i.LastModifiedOn ?? DateTime.MinValue)
//            .ToListAsync(cancellationToken))
//        {
//            permissions.AddRange(await _db.RoleClaims
//                .Where(rc => rc.RoleId == role.Id && rc.ClaimType == LrbClaims.Permission)
//                .Select(rc => rc.ClaimValue)
//                .ToListAsync(cancellationToken));
//        }

//        return permissions.Distinct().ToList();
//    }

//    public async Task<bool> HasPermissionAsync(string userId, string permission, CancellationToken cancellationToken)
//    {
//        var permissions = await _cache.GetOrSetAsync(
//            _cacheKeys.GetCacheKey(LrbClaims.Permission, userId),
//            () => GetPermissionsAsync(userId, cancellationToken),
//            cancellationToken: cancellationToken);

//        return permissions?.Contains(permission) ?? false;
//    }

//    public Task InvalidatePermissionCacheAsync(string userId, CancellationToken cancellationToken) =>
//        _cache.RemoveAsync(_cacheKeys.GetCacheKey(LrbClaims.Permission, userId), cancellationToken);
//}