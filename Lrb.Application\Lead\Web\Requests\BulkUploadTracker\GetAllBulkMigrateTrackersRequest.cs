﻿using Lrb.Application.Common.Persistence;

namespace Lrb.Application.Lead.Web.Requests.BulkUploadTracker
{
    public class GetAllBulkMigrateTrackersRequest : PaginationFilter, IRequest<PagedResponse<LeadMigrateTracker, string>>
    {
    }
    public class GetAllBulkMigrateTrackersRequestHandler : IRequestHandler<GetAllBulkMigrateTrackersRequest, PagedResponse<LeadMigrateTracker, string>>
    {
        private readonly IRepositoryWithEvents<LeadMigrateTracker> _trackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAllBulkMigrateTrackersRequestHandler(IRepositoryWithEvents<LeadMigrateTracker> trackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _trackerRepo = trackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<LeadMigrateTracker, string>> Handle(GetAllBulkMigrateTrackersRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _trackerRepo.ListAsync(new LeadMigrateTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new LeadMigrateTrackerCountSpec(subIds), cancellationToken);
            return new(trackers, totalCount);
        }
    }

    public class LeadMigrateTrackerSpec : EntitiesByPaginationFilterSpec<LeadMigrateTracker>
    {
        public LeadMigrateTrackerSpec(GetAllBulkMigrateTrackersRequest filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class LeadMigrateTrackerCountSpec : Specification<LeadMigrateTracker>
    {
        public LeadMigrateTrackerCountSpec(List<Guid> subIds) 
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
