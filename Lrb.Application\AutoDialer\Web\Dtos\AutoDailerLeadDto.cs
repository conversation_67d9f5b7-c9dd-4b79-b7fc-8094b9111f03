﻿using Lrb.Application.Lead.Mobile;

namespace Lrb.Application.AutoDialer.Web.Dtos
{
    public class AutoDialerLeadDto
    {
        public IVRCallStatus CallStatus { get; set; }
        public ViewLeadDto? Lead { get; set; }
    }
    public class StatusDisplayCountDto
    {
        public Guid? Id { get; set; }
        public string DisplayName { get; set; } = default!;
        public int Count { get; set; }
    }
    public class AutoDialerLead1Dto
    {

        public IVRCallStatus CallStatus { get; set; }
    }

    public class AutoDialerLeadDetailsDto
    {
        public Guid? Id { get; set; }
        public string? Name { get; set; }
        public Guid? AssignTo { get; set; }
        public string? ContactNo { get; set; }
        public DateTime? CallStartTime { get; set; }
        public Guid? SecondaryUserId { get; set; }
    }
    public class AutoDialerCallEndedDetailsDto
    {
        public Guid? LeadId { get; set; }
        public string? Name { get; set; }
        public Guid? AssignTo { get; set; }
        public string? ContactNo { get; set; }
        public DateTime? CallStartTime { get; set; }
        public Guid? SecondaryUserId { get; set; }
    }


    public class AutoDialerViewLeadDto
    {
        public Guid Id { get; set; }
        public string Name { get; set; }
        public string ContactNo { get; set; }
        public string? AlternateContactNo { get; set; }
        public string? Email { get; set; }
        public Guid? AssignTo { get; set; }
        public Guid? SecondaryUserId { get; set; }
        public Gender? Gender { get; set; }
        public DateTime? DateOfBirth { get; set; }
        public MaritalStatusType? MaritalStatus { get; set; }
        public LeadTagDto? LeadTags { get; set; }
        public IVRCallStatus CallStatus { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public UserDetails.Mobile.UserDto? LastModifiedByUser { get; set; }
        public UserDetails.Mobile.UserDto? AssignedUser { get; set; }
        public UserDetails.Mobile.UserDto? AssignedFromUser { get; set; }
        public LeadStatusDto? Status { get; set; }
        public ViewLeadEnquiryDto? Enquiry { get; set; }
        public LeadFilterTypeMobile LeadFilterKey { get; set; }
        public List<ProjectDto>? Projects { get; set; }
        public List<PropertyDto>? Properties { get; set; }
        public UserDetails.Mobile.UserDto? SourcingManagerUser { get; set; }
        public UserDetails.Mobile.UserDto? ClosingManagerUser { get; set; }
        public UserDetails.Mobile.UserDto? BookedByUser { get; set; }
        public UserDetails.Mobile.UserDto? SecondaryUser { get; set; }
        public List<CustomFlagDto>? CustomFlags { get; set; }
        public LeadAssignmentType? AssignmentType { get; set; }
        public UserDetails.Mobile.UserDto? SecondaryFromUser { get; set; }
    }

}
