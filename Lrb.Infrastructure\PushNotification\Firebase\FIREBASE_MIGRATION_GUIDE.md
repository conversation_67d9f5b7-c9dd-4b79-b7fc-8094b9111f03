# Firebase Notification Service Migration Guide

## Overview

This guide explains how to migrate from the existing `NotificationSenderService` (which uses Hangfire and AWS Pinpoint) to the new `FirebaseNotificationSenderService` that uses Firebase Cloud Functions, Cloud Scheduler, and Firebase Cloud Messaging (FCM).

## Key Changes

### 1. Replaced Dependencies
- **Hangfire** → **Firebase Cloud Scheduler + Cloud Functions**
- **AWS Pinpoint** → **Firebase Cloud Messaging (FCM)**
- **Background job processing** → **Firebase Cloud Functions**

### 2. New Services
- `FirebaseNotificationSenderService` - Main notification service
- `FirebaseJobService` - Job scheduling service
- `IFirebaseJobService` - Job service interface

## Configuration Requirements

### 1. Firebase Project Setup

1. **Create Firebase Project**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create a new project or use existing one
   - Enable the following services:
     - Cloud Messaging (FCM)
     - Cloud Functions
     - Cloud Scheduler
     - Firestore Database

2. **Generate Service Account Key**
   - Go to Project Settings → Service Accounts
   - Generate new private key (JSON format)
   - Download the JSON file

3. **Enable Required APIs**
   - Cloud Functions API
   - Cloud Scheduler API
   - Firebase Cloud Messaging API
   - Cloud Firestore API

### 2. Application Configuration

Update your `appsettings.json` files with Firebase configuration:

```json
{
  "FirebaseSetting": *********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************,
  "MobileFirebaseSetting": ***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
}
```

### 3. Dependency Injection Registration

Replace the existing notification service registration in your `Startup.cs` or `Program.cs`:

**Before (Hangfire + AWS Pinpoint):**
```csharp
services.AddNotification(configuration);
```

**After (Firebase):**
```csharp
services.AddFirebaseNotification(configuration);
```

**For gradual migration:**
```csharp
services.AddNotificationWithFirebase(configuration, useFirebase: true);
```

## Required NuGet Packages

Add the following NuGet packages to your project:

```xml
<PackageReference Include="FirebaseAdmin" Version="2.4.0" />
<PackageReference Include="Google.Cloud.Firestore" Version="3.4.0" />
<PackageReference Include="Google.Cloud.Scheduler.V1" Version="3.5.0" />
<PackageReference Include="Google.Cloud.Functions.V1" Version="2.5.0" />
```

## Firebase Cloud Functions Setup

You'll need to deploy Cloud Functions to handle job processing. Create the following functions:

### 1. Process Immediate Jobs (`processJob`)
```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.processJob = functions.https.onRequest(async (req, res) => {
  try {
    const { jobId, jobData } = req.body;
    
    // Process the job based on jobData
    // This would contain the serialized method call information
    
    // Update job status in Firestore
    await admin.firestore().collection('scheduled_jobs').doc(jobId).update({
      status: 'completed',
      completedAt: admin.firestore.FieldValue.serverTimestamp()
    });
    
    res.status(200).send('Job processed successfully');
  } catch (error) {
    console.error('Error processing job:', error);
    res.status(500).send('Error processing job');
  }
});
```

### 2. Process Scheduled Jobs (`processScheduledJob`)
```javascript
exports.processScheduledJob = functions.https.onRequest(async (req, res) => {
  try {
    const { jobId, jobData } = req.body;
    
    // Process the scheduled job
    // Similar to processJob but for scheduled execution
    
    res.status(200).send('Scheduled job processed successfully');
  } catch (error) {
    console.error('Error processing scheduled job:', error);
    res.status(500).send('Error processing scheduled job');
  }
});
```

### 3. Process Recurring Jobs (`processRecurringJob`)
```javascript
exports.processRecurringJob = functions.https.onRequest(async (req, res) => {
  try {
    const { jobId, jobData } = req.body;
    
    // Process the recurring job
    // This will be called based on the cron schedule
    
    res.status(200).send('Recurring job processed successfully');
  } catch (error) {
    console.error('Error processing recurring job:', error);
    res.status(500).send('Error processing recurring job');
  }
});
```

## Migration Steps

### Step 1: Prepare Firebase Environment
1. Set up Firebase project with required services
2. Generate service account credentials
3. Deploy Cloud Functions for job processing

### Step 2: Update Configuration
1. Add Firebase settings to configuration files
2. Install required NuGet packages
3. Update dependency injection registration

### Step 3: Test Migration
1. Deploy application with Firebase services
2. Test notification functionality
3. Verify scheduled jobs are working
4. Monitor Cloud Functions logs

### Step 4: Remove Old Dependencies
1. Remove Hangfire dependencies
2. Remove AWS Pinpoint dependencies
3. Clean up old configuration settings

## Benefits of Migration

### 1. Performance Improvements
- **Serverless architecture** - No need to maintain background job servers
- **Auto-scaling** - Firebase automatically scales based on demand
- **Reduced infrastructure costs** - Pay only for what you use

### 2. Reliability
- **Managed service** - Google manages the infrastructure
- **Built-in retry mechanisms** - Automatic retry for failed jobs
- **Monitoring and logging** - Built-in monitoring through Firebase Console

### 3. Simplified Deployment
- **No background job server setup** - Everything runs in the cloud
- **Easier maintenance** - Fewer moving parts to manage
- **Better error handling** - Centralized error tracking

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify service account credentials are correct
   - Ensure required APIs are enabled in Google Cloud Console

2. **Cloud Function Deployment Issues**
   - Check Firebase CLI is installed and authenticated
   - Verify project permissions

3. **Job Scheduling Problems**
   - Ensure Cloud Scheduler API is enabled
   - Check time zone settings in scheduled jobs

### Monitoring

- Use Firebase Console to monitor function executions
- Check Firestore for job status tracking
- Monitor Cloud Scheduler for scheduled job execution

## Support

For issues related to this migration:
1. Check Firebase Console logs
2. Review Cloud Function execution logs
3. Verify Firestore job status documents
4. Contact development team for assistance
