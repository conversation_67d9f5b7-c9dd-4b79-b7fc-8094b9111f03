﻿using Lrb.Application.ExportTemplate;
using Lrb.Application.Identity.Users;
using Lrb.Application.Project.Web.Dtos;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Specs;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Project.Web.Requests
{
    public class GetProjectExportTracker : PaginationFilter, IRequest<PagedResponse<ExportProjectTrackerDto, string>>
    {
    }
    public class GetProjectExportTrackerHandler : IRequestHandler<GetProjectExportTracker, PagedResponse<ExportProjectTrackerDto, string>>
    {
        private readonly IReadRepository<ExportProjectTracker> _exportProjectTrackerRepo;
        public readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetProjectExportTrackerHandler(IReadRepository<ExportProjectTracker> exportProjectTrackerRepo,
            IUserService userService, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _exportProjectTrackerRepo = exportProjectTrackerRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<ExportProjectTrackerDto, string>> Handle(GetProjectExportTracker request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var leadTrackers = await _exportProjectTrackerRepo.ListAsync(new ExportProjectTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _exportProjectTrackerRepo.CountAsync(new GetExportProjectTrackersCountSpec(subIds), cancellationToken);
            var projectTrackerDto = leadTrackers.Adapt<List<ExportProjectTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(projectTrackerDto.Select(i => i.CreatedBy.ToString()).Distinct().ToList(), cancellationToken);
            foreach (var projectTracker in projectTrackerDto)
            {
                projectTracker.ExportTemplate = (JsonConvert.DeserializeObject<ViewProjectExportTemplateDto>(projectTracker?.Template ?? string.Empty) ?? null);
                projectTracker.ExportedUser = users?.FirstOrDefault(i => projectTracker.CreatedBy == i.Id)?.Adapt<Team.Web.UserDto>();
            }
            return new(projectTrackerDto, totalCount);
        }
    }
}
