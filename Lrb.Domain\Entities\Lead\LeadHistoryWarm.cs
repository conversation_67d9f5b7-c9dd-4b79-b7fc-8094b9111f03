﻿namespace Lrb.Domain.Entities
{
    public class LeadHistoryWarm
    {
        public Guid LeadId { get; set; }
        public string? FieldName { get; set; }
        public string? FieldType { get; set; }
        public string? OldValue { get; set; }
        public string? NewValue { get; set; }
        public string? ModifiedBy { get; set; }
        public DateTime? ModifiedOn { get; set; }
        public Guid? LastModifiedById { get; set; }
        public Guid GroupKey { get; set; }
        public int Version { get; set; }
        public Guid? UserId { get; set; }
    }
}
