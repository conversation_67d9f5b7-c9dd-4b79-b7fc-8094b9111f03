﻿using Lrb.Application.ChannelPartner.Web.Request;
using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.ChannelPartner.Web
{
    public class ChannelPartnersTrackerSpec : EntitiesByPaginationFilterSpec<BulkMarketingAgencyUploadTracker>
    {
        public ChannelPartnersTrackerSpec(GetAllBulkCpTrackersRequest filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.BulkUploadType == MarketingBulkUploadType.ChannelPartner && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class ChannelPartnersTrackerCountSpec : Specification<BulkMarketingAgencyUploadTracker>
    {
        public ChannelPartnersTrackerCountSpec(List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && i.BulkUploadType == MarketingBulkUploadType.ChannelPartner && subIds.Contains(i.CreatedBy));
        }
    }
}
