﻿using Lrb.Application.AutoDialer.Web.Dtos;
using Lrb.Application.AutoDialer.Web.Specs;
using Lrb.Application.UserDetails.Web;

namespace Lrb.Application.AutoDialer.Web.Requests
{
    public class GetAutoDialerConfigurationRequest : IRequest<Response<AutoDialerConfigDto>>
    {
    }
    public class GetAutoDialerConfigurationRequestHandler : IRequestHandler<GetAutoDialerConfigurationRequest, Response<AutoDialerConfigDto>>
    {
        private readonly IRepositoryWithEvents<AutoDialerConfiguration> _autoDialerConfigRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;


        public GetAutoDialerConfigurationRequestHandler(IRepositoryWithEvents<AutoDialerConfiguration> autoDialerConfigRepo, 
            IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo)
        {
            _autoDialerConfigRepo = autoDialerConfigRepo;
            _userDetailsRepo = userDetailsRepo;
        }
        public async Task<Response<AutoDialerConfigDto>> Handle(GetAutoDialerConfigurationRequest request,CancellationToken cancellationToken)
        {
            var config = await _autoDialerConfigRepo.FirstOrDefaultAsync(new GetAutoDialerConfigSpec());
            var result = config?.Adapt<AutoDialerConfigDto>();
            var userIds = (await _userDetailsRepo.ListAsync(new GetAllUserDetailsSpec()))?.Where(i => i.IsAutoDialerEnabled == true)?.Select(j => j.UserId)?.ToList() ?? new();
            result.UserIds = userIds;
            return new(result);
        }
    }
}
