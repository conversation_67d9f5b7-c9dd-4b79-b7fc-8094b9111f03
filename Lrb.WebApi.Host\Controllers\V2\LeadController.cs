﻿using Lrb.Application.Lead.Web;

namespace Lrb.WebApi.Host.Controllers.V2
{
    [Authorize]
    [Route("api/v2/[controller]")]
    [ApiVersionNeutral]
    public class LeadController : BaseApiController
    {
        private readonly Serilog.ILogger _logger;

        public LeadController( Serilog.ILogger logger)
        {
            _logger = logger;
        }

        [AllowAnonymous]
        [ApiKey]
        [HttpGet("anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get all leads using available filters.", "")]
        public async Task<PagedResponse<PullViewLeadDto, string>> SearchAsync([FromQuery] GetAllLeadsAnonymousRequest request)
        {
            var response = await Mediator.Send(request);
            if (HttpContext.Request.Headers.TryGetValue(Lrb.Shared.Multitenancy.MultitenancyConstants.TenantIdName, out var tenantIdValues))
            {
                string? tenantId = tenantIdValues.FirstOrDefault();
                _logger.Information("GetAllLeadsAnonymousRequest info: PageNumber={PageNumber}, PageSize={PageSize}, TenantId={TenantId}", request.PageNumber, request.PageSize, tenantId);

            }
            return response;
        }
    }
}
