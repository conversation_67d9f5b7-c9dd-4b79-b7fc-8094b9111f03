﻿using Lrb.Application.PushNotification.Mobile.Dtos;
using Lrb.Application.PushNotification.Mobile.Specs;

namespace Lrb.Application.PushNotification.Mobile.Requests
{
    public class GetAllPushNotificationsByUserWithoutCountRequest : PaginationFilter, IRequest<Response<PushNotificationWrapperDto>>
    {

    }
    public class GetAllPushNotificationsByUserWithoutCountRequestHandler : IRequestHandler<GetAllPushNotificationsByUserWithoutCountRequest, Response<PushNotificationWrapperDto>>
    {
        private readonly IRepositoryWithEvents<Notification> _notificationRepo;
        private readonly IRepositoryWithEvents<PushNotificationRecords> _pushNotificationRecordsRepo;
        private readonly ICurrentUser _currentUserRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<LocalNotification> _localNotificationRepo;
        public GetAllPushNotificationsByUserWithoutCountRequestHandler(
                                                     IRepositoryWithEvents<Notification> notificationRepo,
                                                     IRepositoryWithEvents<PushNotificationRecords> pushNotificationRecordsRepo,
                                                     ICurrentUser currentUserRepo,
                                                     IDapperRepository dapperRepository,
                                                     IRepositoryWithEvents<LocalNotification> localNotificationRepo)
        {
            _notificationRepo = notificationRepo;
            _pushNotificationRecordsRepo = pushNotificationRecordsRepo;
            _currentUserRepo = currentUserRepo;
            _dapperRepository = dapperRepository;
            _localNotificationRepo = localNotificationRepo;
        }
        public async Task<Response<PushNotificationWrapperDto>> Handle(GetAllPushNotificationsByUserWithoutCountRequest request, CancellationToken cancellationToken)
        {
            Guid currentUserId = _currentUserRepo.GetUserId();
            List<PushNotificationRecordsDto> records = new();
            List<NotificationDto> notifications = await _notificationRepo.ListAsync(new GetNotificationsByUserIdSpecV1(request, currentUserId), cancellationToken);
            records = await _pushNotificationRecordsRepo.ListAsync(new GetPushNotificationRecordsWithoutPaginationSpecV1(notifications.Select(i => i.UniqueId ?? Guid.Empty).ToList(), currentUserId), cancellationToken);
            records = records.DistinctBy(i => i.NotificationUniqueId).ToList();
            List<PushNotificationDto> dtos = new();
            List<PushNotificationDto> notificationDtos = new();
            if ((notifications?.Any() ?? false) && (records?.Any() ?? false))
            {
                PushNotificationDto dto = new();
                foreach (var record in records)
                {
                    var notification = notifications.Where(i => i.UniqueId == record.NotificationUniqueId).FirstOrDefault();
                    var deepLinkUrl = notification?.FCMDeepLinkUrl ?? string.Empty;
                    Guid guidLeadId = GetGuidFromDeepLinkUrl(deepLinkUrl);
                    dtos.Add(new PushNotificationDto()
                    {
                        DeliveredTime = record.DeliveredAt,
                        IsOpened = record.IsOpened,
                        Description = notification?.MessageBody ?? string.Empty,
                        FCMDeepLinkUrl = notification?.FCMDeepLinkUrl ?? string.Empty,
                        Title = notification?.Title ?? string.Empty,
                        LeadId = guidLeadId,
                        NotificationUniqueid = notification?.UniqueId ?? Guid.Empty,
                        IsDelivered = record.IsDelivered,
                        CreatedOn = record.CreatedOn,
                        IsLocalNotification = false
                    });
                }
            }
            if (dtos.Any())
            {
                return new()
                {
                    Data = new()
                    {
                        Items = dtos
                    },
                    Succeeded = true,
                };
            }
            else
            {
                return new()
                {
                    Succeeded = false,
                    Message = "No Notifications Found!",
                };
            }
        }
        public static Guid GetGuidFromDeepLinkUrl(string? deepLinkUrl)
        {
            Guid guidLeadId = default;
            if (!string.IsNullOrWhiteSpace(deepLinkUrl) && deepLinkUrl.Contains("id"))
            {
                var leadId = deepLinkUrl.Split('&').Where(i => i.Contains("id=")).FirstOrDefault();
                leadId = leadId != null ? leadId.Substring(3) : leadId;
                guidLeadId = !string.IsNullOrWhiteSpace(leadId) ? Guid.TryParse(leadId, out Guid result) ? result : Guid.Empty : Guid.Empty;
            }
            return guidLeadId;
        }
    }
}
