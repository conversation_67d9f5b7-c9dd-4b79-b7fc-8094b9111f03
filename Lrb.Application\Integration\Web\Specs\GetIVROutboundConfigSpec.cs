﻿namespace Lrb.Application.Integration.Web.Specs
{
    public class GetIVROutboundConfigSpec : Specification<IVROutboundConfiguration>
    {
        public GetIVROutboundConfigSpec()
        {
            Query.Where(i => !i.IsDeleted && i.IsPrimary);
        }
    }

    public class GetIVROutboundConfigByIdSpec : Specification<IVROutboundConfiguration>
    {
        public GetIVROutboundConfigByIdSpec(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }
}
