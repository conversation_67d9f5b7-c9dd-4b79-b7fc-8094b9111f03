﻿namespace Lrb.Domain.Enums
{
    public enum IVRResponseType
    {
        None = 0,

        // Identifiers
        CallId = 1,          // Unique identifier for the call
        SessionId = 2,       // Session or transaction ID
        ExternalId = 3,      // Optional external reference ID (e.g., CRM-lead ID)

        // Status & Message
        Status = 4,          // Status of the call (e.g., "initiated", "connected")
        Message = 5,         // Human-readable status message or remarks
        StatusCode = 6,      // Optional numeric status code (e.g., 200, 400)

        // Errors
        Error = 7,           // General error message
        ErrorCode = 8,       // Specific error code (e.g., "IVR_001")
        IsSuccess = 9,       // Boolean indicator of success or failure

        // Call metadata
        RecordingUrl = 10,   // URL to call recording, if available
        Duration = 11,       // Call duration in seconds
        Timestamp = 12,      // Time when the call event occurred

        // Routing / Flow
        AgentId = 13,        // ID of the agent the call was routed to
        CustomerNumber = 14, // Customer's phone number
        AgentNumber = 15,    // Agent's phone number

        // Optional Fields
        CustomField1 = 16,   // Placeholder for additional provider-specific data
        CustomField2 = 17
    }

}
