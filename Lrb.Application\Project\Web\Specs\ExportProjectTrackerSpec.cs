﻿using Lrb.Application.Project.Web.Requests;
using Lrb.Application.Property.Web.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Project.Web.Specs
{
    public class ExportProjectTrackerSpec : EntitiesByPaginationFilterSpec<ExportProjectTracker>
    {
        public ExportProjectTrackerSpec(GetProjectExportTracker filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy)).OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetExportProjectTrackersCountSpec : Specification<ExportProjectTracker>
    {
        public GetExportProjectTrackersCountSpec(List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
