﻿using Lrb.Application.UserDetails.Web;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Integration.Web.Requests.Common
{
    public class GetUsersBasicInfoRequest : IRequest<Response<List<ViewUserBasicDetailsDto>>>
    {
        public string? TenantId { get; set; }
        public int? PageSize { get; set; } = 10;
        public int? PageNumber { get; set; } = 1;
    }
    public class GetUsersBasicInfoRequestHandler : IRequestHandler<GetUsersBasicInfoRequest, Response<List<ViewUserBasicDetailsDto>>>
    {
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        public GetUsersBasicInfoRequestHandler(IDapperRepository dapperRepository, IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo)
        {
            _dapperRepository = dapperRepository;
            _integrationAccRepo = integrationAccRepo;
        }

        public async Task<Response<List<ViewUserBasicDetailsDto>>> Handle(GetUsersBasicInfoRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request.PageNumber <= 0 && request.PageSize <= 0)
                {
                    throw new ArgumentException("Pagination is required: PageNumber and PageSize must be greater than 0.");
                }
                var result = await _dapperRepository.GetPaginatedUserBasicInfoAsync(tenantId: request.TenantId, pageNumber: request.PageNumber, pageSize: request.PageSize);
                return new(result);
            }
            catch (Exception ex)
            {
                return new()
                {
                    Message = ex.Message,
                    Succeeded = false
                };
            }
        }
    }
}
