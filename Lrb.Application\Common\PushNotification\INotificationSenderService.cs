﻿using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Common.PushNotification
{
    public interface INotificationSenderService
    {
        public Task<List<string>> ScheduleNotificationsAsync<T>(Event @event, T entity, Guid? userId = default, string? userName = default, List<string>? topics = null, int? noOfEntities = null, Guid? currentUserIdPm = null, List<Guid>? userIds = null, LeadSource? leadSourceParam = null, Guid? currentUserId = null, int? rotationTime = null,Domain.Entities.GlobalSettings? globalSettings = null, List<UserDetailsDto>? allUserDetails = null, List<NotificationContent>? contents = null, string? status = null,double? bufferTime = null);
        public Task<List<string>> SchedulePushNotificationsAsync<T>(Event @event, T entity, Domain.Entities.GlobalSettings? globalSettings, List<Guid>? userIds = default, string? userName = default, List<string>? topics = null, Guid? currentUserId = null);
        public Task<bool> DeleteScheduledNotificationsAsync<T>(T entity);
        public Task<bool> DeleteScheduledNotificationsBulkAsync<T>(List<T> entities);
        public Task SendWhatsAppNotificationAsync<T>(T entity, Domain.Enums.Event @event, NotificationSettings? notificationSettings, Domain.Entities.GlobalSettings globalSettings, bool? isFeedbackNotification = false, Domain.Entities.Lead? lead = null, string? userName = null);
        public Task SendWhatsAppNotificationAsync<T>(T entity, List<Guid> waIds, NotificationSettings? notificationSettings, Domain.Entities.GlobalSettings? globalSettings, bool? IsFeedbackNotification = null, object? leadDto = null);
        public Task<string> ScheduleWANotificationsAsync<T>(T entity, Guid userId, string mediaType, string message);
        Task SendTemplateNotificationAsync<T>(T entity, List<Guid>? statusIds, Lrb.Domain.Enums.Event? @event, int? NoOfEntities = null);
    }
}
