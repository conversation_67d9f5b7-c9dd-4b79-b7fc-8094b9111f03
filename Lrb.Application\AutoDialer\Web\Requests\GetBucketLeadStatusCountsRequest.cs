﻿using Lrb.Application.AutoDialer.Web.Dtos;
using Lrb.Application.Common.Persistence;

namespace Lrb.Application.AutoDialer.Web.Requests
{
    public class GetBucketLeadStatusCountsRequest : IRequest<Response<List<StatusDisplayCountDto>>>
    {
        public List<Guid>? UserIds { get; set; }
        public IVRCallStatus CallStatus { get; set; }
    }
    public class GetBucketLeadStatusCountsRequestHandler : IRequestHandler<GetBucketLeadStatusCountsRequest, Response<List<StatusDisplayCountDto>>>
    {
        public readonly IRepositoryWithEvents<Domain.Entities.AutoDialerAudit> _autoDialerAuditRepo;
        public readonly IRepositoryWithEvents<Domain.Entities.MasterData.CustomMasterLeadStatus> _statusRepo;
        public readonly ICurrentUser _currentUser;
        IDapperRepository _dapperRepository;

        public GetBucketLeadStatusCountsRequestHandler(IRepositoryWithEvents<AutoDialerAudit> autoDialerAuditRepo,
            ICurrentUser currentUser, IRepositoryWithEvents<Domain.Entities.MasterData.CustomMasterLeadStatus> statusRepo, IDapperRepository dapperRepository)
        {
            _autoDialerAuditRepo = autoDialerAuditRepo;
            _currentUser = currentUser;
            _statusRepo = statusRepo;
            _dapperRepository = dapperRepository;
        }
        public async Task<Response<List<StatusDisplayCountDto>>> Handle(GetBucketLeadStatusCountsRequest request, CancellationToken cancellationToken)
        {
            var tenant = _currentUser.GetTenant();
            var userId = _currentUser.GetUserId();
            var isAdmin = await _dapperRepository.IsAdminAsync(userId, tenant);
            List<Guid> UserIds = request.UserIds ?? new();
            if (!isAdmin && UserIds.Count() == 0)
            {
                UserIds = (await _dapperRepository.GetSubordinateIdsAsync(userId, tenant, false)).ToList();
            }
            var auditLeadStatusIds = (await _dapperRepository.QueryStoredProcedureFromReadReplicaAsync<StatusDisplayCountDto>(
                               "LeadratBlack",
                               "get_status_display_counts",
                               new
                               {
                                   p_tenant_id = tenant,
                                   p_user_ids = UserIds.Any() ? UserIds : new List<Guid> { userId },
                                   p_is_admin = isAdmin,
                                   p_call_status = request.CallStatus
                               })).ToList();
            return new(auditLeadStatusIds);
        }
    }
}
