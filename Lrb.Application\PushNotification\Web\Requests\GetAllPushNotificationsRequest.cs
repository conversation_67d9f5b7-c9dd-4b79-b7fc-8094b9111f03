﻿using Lrb.Application.PushNotification.Web.Dtos;
using Lrb.Application.PushNotification.Web.Specs;

namespace Lrb.Application.PushNotification.Web.Requests
{
    public class GetAllPushNotificationsRequest : PaginationFilter, IRequest<Response<PushNotificationWrapperDto>>
    {
        public string DeviceUDID { get; set; }
    }

    public class GetAllPushNotificationsRequestHandler : IRequestHandler<GetAllPushNotificationsRequest, Response<PushNotificationWrapperDto>>
    {
        private readonly IRepositoryWithEvents<Notification> _notificationRepo;
        private readonly IRepositoryWithEvents<PushNotificationRecords> _pushNotificationRecordsRepo;
        private readonly ICurrentUser _currentUserRepo;
        private readonly IRepositoryWithEvents<LocalNotification> _localNotificationRepo;
        public GetAllPushNotificationsRequestHandler(IRepositoryWithEvents<Notification> notificationRepo,
                                                     IRepositoryWithEvents<PushNotificationRecords> pushNotificationRecordsRepo,
                                                     ICurrentUser currentUserRepo,
                                                     IRepositoryWithEvents<LocalNotification> localNotificationRepo)
        {
            _notificationRepo = notificationRepo;
            _pushNotificationRecordsRepo = pushNotificationRecordsRepo;
            _currentUserRepo = currentUserRepo;
            _localNotificationRepo = localNotificationRepo;
        }

        public async Task<Response<PushNotificationWrapperDto>> Handle(GetAllPushNotificationsRequest request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(request.DeviceUDID))
            {
                return new()
                {
                    Succeeded = false,
                    Data = new(),
                    Message = "DeviceUDID cannot be empty!"
                };
            }
            Guid currentUserId = _currentUserRepo.GetUserId();
            List<PushNotificationRecordsDto> records = new();
            int totalRecordsCount = default;
            int notOpenedRecordsCount = default;
            records = await _pushNotificationRecordsRepo.ListAsync(new GetPushNotificationRecordsSpecV1(request, currentUserId, request.DeviceUDID), cancellationToken);
            totalRecordsCount = await _pushNotificationRecordsRepo.CountAsync(new GetPushNotificationRecordsCountSpec(request, currentUserId, request.DeviceUDID), cancellationToken);
            notOpenedRecordsCount = await _pushNotificationRecordsRepo.CountAsync(new GetNotOpenedRecordsCountSpec(request, currentUserId, request.DeviceUDID), cancellationToken);
            //List<LocalNotification>? localNotifications = await _localNotificationRepo.ListAsync(new GetLocalNotificationsByUserAndDeviceSpec(currentUserId, request.DeviceUDID), cancellationToken);
            if ((!records?.Any() ?? true))
            {
                return new()
                {
                    Message = "No Notification Records Found!",
                    Succeeded = true,
                };
            }
            List<Guid>? notificationUniqueIds = records?.Select(r => r.NotificationUniqueId ?? Guid.Empty).ToList();
            notificationUniqueIds = notificationUniqueIds?.Any() ?? false ? notificationUniqueIds.Where(id => id != Guid.Empty).ToList() : notificationUniqueIds;
            List<NotificationDto>? notifications = null;
            if (notificationUniqueIds?.Any() ?? false)
            {
                notifications = await _notificationRepo.ListAsync(new GetNotificationsByUniqueIdSpecV1(notificationUniqueIds), cancellationToken);
            }
            List<PushNotificationDto> dtos = new();
            List<PushNotificationDto> notificationDtos = new();
            if ((notifications?.Any() ?? false) && (records?.Any() ?? false))
            {
                PushNotificationDto dto = new();
                foreach (var record in records)
                {
                    var notification = notifications.Where(i => i.UniqueId == record.NotificationUniqueId).FirstOrDefault();
                    var deepLinkUrl = notification?.FCMDeepLinkUrl ?? string.Empty;
                    Guid guidLeadId = default;
                    if (!string.IsNullOrWhiteSpace(deepLinkUrl) && deepLinkUrl.Contains("id"))
                    {
                        var leadId = deepLinkUrl.Split('&').Where(i => i.Contains("id=")).FirstOrDefault();
                        leadId = leadId != null ? leadId.Substring(3) : leadId;
                        guidLeadId = !string.IsNullOrWhiteSpace(leadId) ? Guid.TryParse(leadId, out Guid result) ? result : Guid.Empty : Guid.Empty;
                    }
                    dtos.Add(new PushNotificationDto()
                    {
                        DeliveredTime = record.DeliveredAt,
                        IsOpened = record.IsOpened,
                        Description = notification?.MessageBody ?? string.Empty,
                        FCMDeepLinkUrl = notification?.FCMDeepLinkUrl ?? string.Empty,
                        Title = notification?.Title ?? string.Empty,
                        LeadId = guidLeadId,
                        NotificationUniqueid = notification?.UniqueId ?? Guid.Empty,
                        IsDelivered = record.IsDelivered,
                        CreatedOn = record.CreatedOn
                    });
                }
            }
            //if (localNotifications?.Any() ?? false)
            //{
            //    List<PushNotificationDto>? localPushNotificationDtos = localNotifications.Adapt<List<PushNotificationDto>>();
            //    dtos.AddRange(localPushNotificationDtos);
            //    dtos = dtos.OrderByDescending(i => i.CreatedOn).ToList();
            //    totalRecordsCount = localPushNotificationDtos.Any() ? (totalRecordsCount + localPushNotificationDtos.Count) : totalRecordsCount;
            //}
            if (dtos.Any())
            {
                return new()
                {
                    Data = new()
                    {
                        Items = dtos,
                        TotalCount = totalRecordsCount,
                        NotOpenedCount = notOpenedRecordsCount
                    },
                    Succeeded = true,
                };
            }
            else
            {
                return new()
                {
                    Succeeded = false,
                    Message = "No Notifications Found!",
                };
            }
        }
    }
}
