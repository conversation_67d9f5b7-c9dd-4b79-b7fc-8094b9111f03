﻿using Lrb.Application.Lead.Web;
using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.Property.Web.Dtos;



namespace Lrb.Application.Property.Web
{
    public class UpdatePropertyDto : CreatePropertyDto
    {
    }
    public class CreatePropertyDto : BasePropertyDto
    {
        public string? PlaceId { get; set; }
        public Guid? PropertyTypeId { get; set; }
        public List<string>? ProjectsList { get; set; }
        public List<CreateListingAddressDto>? ListingAddresses { get; set; }


    }
    public class ViewPropertyDto : BasePropertyDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public string? MicrositeURL { get; set; }
        public int? MicrositeUniqueNo { get; set; }
        //public string? SerialNo { get; set; }
        // public List<ProjectDto>? Projects { get; set; }
        public List<PropertyVideoGallaryDto>? Videos { get; set; }
        public ListingStatus? ListingStatus { get; set; }
        public DateTime? ListingExpireDate { get; set; }
        public List<CustomListingSourceDto>? ListingSources { get; set; }
        public ListingLevel? ListingLevel { get; set; }
        public List<ViewListingSourceAddressDto>? ListingSourceAddresses { get; set; }
    }
    public class BasePropertyDto : IDto
    {
        public Guid Id { get; set; }
        public IEnumerable<PropertyAttributeDto>? Attributes { get; set; }
        public string? Title { get; set; }
        public SaleType SaleType { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public FurnishStatus FurnishStatus { get; set; }
        public PropertyStatus Status { get; set; }
        public string? Rating { get; set; }
        public int ShareCount { get; set; }
        public DateTime? PossessionDate { get; set; }
        public PossesionType? PossesionType { get; set; }

        public bool IsGOListingEnabled { get; set; }
        public Facing Facing { get; set; }
        //table references
        public Guid? GOPropertyId { get; set; }
        public double NoOfBHK { get; set; }
        public BHKType BHKType { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
        public PropertyOwnerDetailsDto? OwnerDetails { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public PropertyTagDto? TagInfo { get; set; }
        public Dictionary<string, List<PropertyGalleryDto>>? ImageUrls { get; set; }
        public IEnumerable<Guid>? Amenities { get; set; }
        public bool IsArchived { get; set; }
        public List<BrochureDto>? Brochures { get; set; }
        public int WhatsAppShareCount { get; set; }
        public int CallShareCount { get; set; }
        public int EmailShareCount { get; set; }
        public int SMSShareCount { get; set; }
        public string? AboutProperty { get; set; }
        public AddressDto? Address { get; set; }
        public string? Landmark { get; set; }
        public double? MaintenanceCost { get; set; }
        public int MyProperty { get; set; }
        public PropertySource PropertySource { get; set; }
        public int UnitNo { get; set; }
        public List<string>? Links { get; set; }
        public List<string>? Projects { get; set; }
        public string? Project { get; set; }
        public string? SerialNo { get; set; }
        public List<Guid>? AssignedTo { get; set; }
        public bool? IsWaterMarkEnabled { get; set; }
        public Dictionary<string, string>? AdditionalProperties { get; set; }
        public bool? ShouldVisisbleOnListing { get; set; }
        public SecurityDeposit? SecurityDeposit { get; set; }
        public LockInPeriod? LockInPeriod { get; set; }
        public NoticePeriod? NoticePeriod { get; set; }
        public List<int>? NoOfFloorsOccupied { get; set; }
        public string? CoWorkingOperator { get; set; }
        public string? CoWorkingOperatorName { get; set; }
        public string? CoWorkingOperatorPhone { get; set; }
        public TenantContactInfoDto? TenantContactInfo { get; set; }
        public string? DLDPermitNumber { get; set; }
        public string? RefrenceNo { get; set; }
        public string? DTCMPermit { get; set; }
        public OfferingType? OfferingType { get; set; }
        public CompletionStatus? CompletionStatus { get; set; }
        public string? Language { get; set; }
        public string? TitleWithLanguage { get; set; }
        public string? AboutPropertyWithLanguage { get; set; }
        public List<string>? View360Url { get; set; }
        public TaxationMode TaxationMode { get; set; }
        public List<Guid>? ListingOnBehalf { get; set; }
        public double? SecurityDepositAmount { get; set; }
        public string? SecurityDepositUnit { get; set; }

        public List<PropertyOwnerDetailsDto>? PropertyOwnerDetails { get; set; }
        public Dictionary<Guid, SourceReferenceInfo>? SourceReferenceIds { get; set; }
    }
    public class BasicPropertyInfoDto
    {
        public Guid? Id { get; set; }
        public string? Title { get; set; }
        public EnquiryType EnquiredFor { get; set; }
        public Guid? PropertyTypeId { get; set; }
        public double NoOfBHK { get; set; }
        public double NoOfBHKs { get; set; }
        public BHKType BHKType { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public string? AboutProperty { get; set; }
        public string? Project { get; set; }
        public PropertyOwnerDetailsDto? OwnerDetails { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
        public List<PropertyOwnerDetailsDto>? PropertyOwnerDetails { get; set; }

    }



    public class PropertiesInfoDto
    {
        public SaleType SaleType { get; set; }
        public DateTime? PossessionDate { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
        public PropertyOwnerDetailsDto? OwnerDetails { get; set; }
        public string? PlaceId { get; set; }
        public AddressDto? Address { get; set; }
        public Guid? GOPropertyId { get; set; }
        public PropertyTagDto? TagInfo { get; set; }
        public List<PropertyOwnerDetailsDto>? PropertyOwnerDetails { get; set; }

        public PossesionType? PossesionType { get; set; }

    }
    //public class PropertyGalleryDto
    //{
    //    public Dictionary<string, List<string>>? ImageUrls { get; set; }
    //}
    public class PropertyAttributesDto
    {
        public IEnumerable<PropertyAttributeDto>? Attributes { get; set; }
    }
    public class PropertyAmenitiesDto
    {
        public IEnumerable<Guid>? Amenities { get; set; }
    }



    public class ViewFormattedPropertyDto
    {
        public string? Title { get; set; }
        public string? PropertyType { get; set; }
        public long? Budget { get; set; }
        public string SaleType { get; set; }
        public string? AreaWithUnit { get; set; }
        public string EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public string? FurnishStatus { get; set; }
        public string? Status { get; set; }
        public string? Rating { get; set; }
        public int ShareCount { get; set; }
        public DateTime? PossessionDate { get; set; }
        public string Facing { get; set; }
        public double NoOfBHK { get; set; }
        public string BHKType { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerPhoneNo { get; set; }
        public string? OwnerEmail { get; set; }
        public string? Project { get; set; }
        public string? AboutProperty { get; set; }
        public string? Location { get; set; }
        /*   public double? MaintenanceCost { get; set; }
           public int MyProperty { get; set; }
           public string? PropertySource { get; set; }
           public int UnitNo { get; set; }*/
        public string? ThirdPartyURL { get; set; }
        public string? Attributes { get; set; }
        public string? OwnerAlternateNo { get; set; }
        public string? ImageUrls { get; set; }
        public string? Videos { get; set; }
        public string? Brochures { get; set; }
        public PossesionType? PossesionType { get; set; }
        public double? SecurityDepositAmount { get; set; }
        public string? SecurityDepositUnit { get; set; }


    }


    public class PullViewPropertyDto : PullBasePropertyDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public string? MicrositeURL { get; set; }
        public int? MicrositeUniqueNo { get; set; }
        public List<PropertyVideoGallaryDto>? Videos { get; set; }
    }
    public class PullBasePropertyDto : IDto
    {
        public Guid Id { get; set; }
        public IEnumerable<PropertyAttributeDto>? Attributes { get; set; }
        public string? Title { get; set; }
        public string SaleType { get; set; }
        public string EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public string FurnishStatus { get; set; }
        public string Status { get; set; }
        public string? Rating { get; set; }
        public int ShareCount { get; set; }
        public DateTime? PossessionDate { get; set; }
        public bool IsGOListingEnabled { get; set; }
        public string Facing { get; set; }
        public double NoOfBHK { get; set; }
        public string BHKType { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
        public PropertyOwnerDetailsDto? OwnerDetails { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public PropertyTagDto? TagInfo { get; set; }
        public Dictionary<string, List<PropertyGalleryDto>>? ImageUrls { get; set; }
        public IEnumerable<string>? Amenities { get; set; }
        public bool IsArchived { get; set; }
        public List<BrochureDto>? Brochures { get; set; }
        public int WhatsAppShareCount { get; set; }
        public int CallShareCount { get; set; }
        public int EmailShareCount { get; set; }
        public int SMSShareCount { get; set; }
        public string? AboutProperty { get; set; }
        public AddressDto? Address { get; set; }
        public string? Landmark { get; set; }
        public double? MaintenanceCost { get; set; }
        public int MyProperty { get; set; }
        public string PropertySource { get; set; }
        public int UnitNo { get; set; }
        public List<string>? Links { get; set; }
        public List<string>? Projects { get; set; }
        public string? Project { get; set; }
        public string? SerialNo { get; set; }
        public bool? IsWaterMarkEnabled { get; set; }
        public Dictionary<string, string>? AdditionalProperties { get; set; }
        public bool? ShouldVisisbleOnListing { get; set; }
        public List<PropertyOwnerDetailsDto>? PropertyOwnerDetails { get; set; }
        public PossesionType? PossesionType { get; set; }


    }
    public class PropertyUserAssignmentDto
    {
        public Guid? Id { get; set; }
        public string? Title { get; set; }
        public List<Guid>? AssignedTo { get; set; }
    }

    #region Listing Management
    public class PullViewPropertyForListingDto : PullBasePropertyForListingDto
    {
        public DateTime? LastModifiedOn { get; set; }
        public DateTime CreatedOn { get; set; }
        public PropertyTypeDto? PropertyType { get; set; }
        public string? MicrositeURL { get; set; }
        public int? MicrositeUniqueNo { get; set; }
        public List<PropertyVideoGallaryDto>? Videos { get; set; }
        public ListingStatus ListingStatus { get; set; }
        public DateTime? ListingExpireDate { get; set; }
        public List<CustomListingSourceDto>? ListingSources { get; set; }
        public ListingLevel? ListingLevel { get; set; }
        public List<ViewListingSourceAddressDto>? ListingSourceAddresses { get; set; }
    }
    public class PullBasePropertyForListingDto : IDto
    {
        public Guid Id { get; set; }
        public IEnumerable<PropertyAttributeDto>? Attributes { get; set; }
        public string? Title { get; set; }
        public string? SaleType { get; set; }
        public string? EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public string? FurnishStatus { get; set; }
        public string? Status { get; set; }
        public string? Rating { get; set; }
        public int ShareCount { get; set; }
        public DateTime? PossessionDate { get; set; }
        public bool IsGOListingEnabled { get; set; }
        public string? Facing { get; set; }
        public double NoOfBHK { get; set; }
        public string BHKType { get; set; }
        public PropertyMonetaryInfoDto? MonetaryInfo { get; set; }
        public PropertyOwnerDetailsDto? OwnerDetails { get; set; }
        public PropertyDimensionDto? Dimension { get; set; }
        public PropertyTagDto? TagInfo { get; set; }
        public Dictionary<string, List<PropertyGalleryDto>>? ImageUrls { get; set; }
        public IEnumerable<string>? Amenities { get; set; }
        public bool IsArchived { get; set; }
        public List<BrochureDto>? Brochures { get; set; }
        public int WhatsAppShareCount { get; set; }
        public int CallShareCount { get; set; }
        public int EmailShareCount { get; set; }
        public int SMSShareCount { get; set; }
        public string? AboutProperty { get; set; }
        public AddressDto? Address { get; set; }
        public string? Landmark { get; set; }
        public double? MaintenanceCost { get; set; }
        public int MyProperty { get; set; }
        public string? PropertySource { get; set; }
        public int UnitNo { get; set; }
        public List<string>? Links { get; set; }
        public List<string>? Projects { get; set; }
        public string? Project { get; set; }
        public string? SerialNo { get; set; }
        public bool? IsWaterMarkEnabled { get; set; }
        public Dictionary<string, string>? AdditionalProperties { get; set; }
        public bool? ShouldVisisbleOnListing { get; set; }
        public string? DLDPermitNumber { get; set; }
        public string? RefrenceNo { get; set; }
        public string? DTCMPermit { get; set; }
        public string? OfferingType { get; set; }
        public string? CompletionStatus { get; set; }
        public string? Language { get; set; }
        public string? TitleWithLanguage { get; set; }
        public string? AboutPropertyWithLanguage { get; set; }
        public List<PropertyOwnerDetailsDto>? PropertyOwnerDetails { get; set; }

        public string? PossesionType { get; set; }

    }

    public class ViewFormattedPropertyListingDtoV2
    {
        public string? ReferenceInfo { get; set; }
        public string? UaeEmirate { get; set; }
        public string? Portals { get; set; } // This can be a comma-separated string of portal names
        public string? PermitNumber { get; set; }
        public string? PermitType { get; set; }
        public string? EnquiredFor { get; set; }
        public string? PropertyType { get; set; }
        public string? Title { get; set; }
        public string? AboutProperty { get; set; }
        public long? Price { get; set; }
        public long? Downpayment { get; set; }
        public string? AreaWithUnit { get; set; }
        public string? Notes { get; set; }
        public PossesionType? PossesionType { get; set; }
        public DateTime? PossessionDate { get; set; }
        public string? Facing { get; set; }
        public string? BR { get; set; }
        public string? ListingLevel { get; set; }
        public string? OfferingType { get; set; }
        public string? CompletionStatus { get; set; }
        public string? FurnishStatus { get; set; }
        public double? Age { get; set; }
        public string? AgentName { get; set; }
        public string? AgentPhoneNo { get; set; }
        public string? AgentEmail { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerPhoneNo { get; set; }
        public string? OwnerAlternateNo { get; set; }
        public string? OwnerEmail { get; set; }
        public string? Project { get; set; }
        public string? TowerName { get; set; }
        public string? SubCommunity { get; set; }
        public string? Community { get; set; }
        public string? City { get; set; }
        public string? ThirdPartyURL { get; set; }
        public string? Attributes { get; set; }
        public string? Amenities { get; set; }
        public string? View360Url { get; set; }
        public string? ImageUrls { get; set; }
        public string? Videos { get; set; }
        public string? Brochures { get; set; }
        public int ShareCount { get; set; }
    }

    public class ReferenceInfo
    {
        public string? PortalName { get; set; }
        public string? ReferenceNo { get; set; }
    }

    public class ViewFormattedPropertyListingDto
    {
        public string? Title { get; set; }
        public string? PropertyType { get; set; }
        public long? Budget { get; set; }
        public string SaleType { get; set; }
        public string? AreaWithUnit { get; set; }
        public string EnquiredFor { get; set; }
        public string? Notes { get; set; }
        public string? FurnishStatus { get; set; }
        public string? Status { get; set; }
        public string? Rating { get; set; }
        public int ShareCount { get; set; }
        public DateTime? PossessionDate { get; set; }
        public string? Facing { get; set; }
        public double? BHK { get; set; }
        public string? ListingLevel { get; set; }
        public string? OfferingType { get; set; }
        public string? CompletionStatus { get; set; }
        public string? OwnerName { get; set; }
        public string? OwnerPhoneNo { get; set; }
        public string? OwnerEmail { get; set; }
        public string? Project { get; set; }
        public string? AboutProperty { get; set; }
        public string? Location { get; set; }
        public string? TowerName { get; set; }
        public string? SubCommunity { get; set; }
        public string? Community { get; set; }
        public string? ThirdPartyURL { get; set; }
        public string? Attributes { get; set; }
        public string? OwnerAlternateNo { get; set; }
        public string? View360Url { get; set; }
        public string? ImageUrls { get; set; }
        public string? Videos { get; set; }
        public string? Brochures { get; set; }
        public PossesionType? PossesionType { get; set; }
        public double? SecurityDepositAmount { get; set; }
        public string? SecurityDepositUnit { get; set; }


    }

    public class CreateListingAddressDto
    {
        public Guid? ListingSourceId { get; set; }
        public Guid? LocationId { get; set; }
    }
    #endregion


    public class CustomPropertyAttributeDto
    {
        public Guid? Id { get; set; }
        public string? AttributeDisplayName { get; set; }
    }
}

