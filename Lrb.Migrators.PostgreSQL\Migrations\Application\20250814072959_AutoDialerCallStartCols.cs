﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AutoDialerCallStartCols : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "ShouldUseBGJob",
                schema: "LeadratBlack",
                table: "AutoDialerConfigurations",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CallStarted",
                schema: "LeadratBlack",
                table: "AutoDialerAudits",
                type: "timestamp with time zone",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ShouldUseBGJ<PERSON>",
                schema: "LeadratBlack",
                table: "AutoDialerConfigurations");

            migrationBuilder.DropColumn(
                name: "CallStarted",
                schema: "LeadratBlack",
                table: "AutoDialerAudits");
        }
    }
}
