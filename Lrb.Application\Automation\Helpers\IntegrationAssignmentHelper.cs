﻿using Lrb.Application.Automation.Specs;
using Lrb.Application.Integration.Web;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Domain.Entities.Integration;

namespace Lrb.Application.Automation.Helpers
{
    public static class IntegrationAssignmentHelper
    {
        public static async Task<(UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project)> GetMostPrioritizedUserAssignmentAsync(Guid intrAccId,
            LeadSource source,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            Domain.Entities.GlobalSettings? globalSettings = null,
            IRepositoryWithEvents<FacebookAdsInfo>? fbAdsRepo = null,
            IRepositoryWithEvents<FacebookLeadGenForm>? fbFormRepo = null,
            IRepositoryWithEvents<IntegrationAccountInfo>? integrationAccRepo = null,
            Lrb.Domain.Entities.Project? projectWithAssignment = null,
            Location? locationWithUserAssignment = null)
        {
            var intgrAssgnId = Guid.Empty;
            UserAssignment? intgrAccUserAssignment = null;
            if (fbAdsRepo != null)
            {
                var ad = await fbAdsRepo.FirstOrDefaultAsync(new FacebookAdsInfoByIdSpec(intrAccId), default);
                intgrAssgnId = ad?.Assignment?.Id ?? Guid.Empty;
                intgrAccUserAssignment = ad?.UserAssignment;
            }
            else if (fbFormRepo != null)
            {
                var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(intrAccId), default);
                intgrAssgnId = form?.Assignment?.Id ?? Guid.Empty;
                intgrAccUserAssignment = form?.UserAssignment;
            }
            else if (integrationAccRepo != null)
            {
                IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intrAccId);
                intgrAssgnId = integrationAccountInfo?.Assignment?.Id ?? Guid.Empty;
                intgrAccUserAssignment = integrationAccountInfo?.UserAssignment;
            }

            IntegrationAssignment? intgrAssignmentIncludedLocAndProj = await integrationAssignmentRepo.FirstOrDefaultAsync(new IntegrationAssignmentByIdSpec(intgrAssgnId), default);
            if (intgrAssignmentIncludedLocAndProj == null && projectWithAssignment == null && locationWithUserAssignment == null)
            {
                return (intgrAccUserAssignment, null);
            }
            var assignmentModules = await assignmentModuleRepo.ListAsync(default);
            if (assignmentModules == null || !assignmentModules.Any())
            {
                assignmentModules = AssignmentModule.DefaultModules;
            }
            assignmentModules = assignmentModules.Where(i => i != null).OrderBy(i => i.Priority).ToList();
            foreach (var assignmentModule in assignmentModules)
            {
                switch (assignmentModule.Name)
                {
                    case AssignmentModule.Project:
                        if (projectWithAssignment != null && (projectWithAssignment.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (projectWithAssignment.UserAssignment, null);
                        }
                        else if (intgrAssignmentIncludedLocAndProj?.Project != null && (intgrAssignmentIncludedLocAndProj.Project?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (intgrAssignmentIncludedLocAndProj.Project?.UserAssignment, intgrAssignmentIncludedLocAndProj.Project);
                        }
                        break;
                    case AssignmentModule.Location:
                        if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment != null && (locationWithUserAssignment.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (locationWithUserAssignment.UserAssignment, null);
                        }
                        else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location != null && (intgrAssignmentIncludedLocAndProj.Location?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (intgrAssignmentIncludedLocAndProj.Location?.UserAssignment, null);
                        }
                        break;
                    case AssignmentModule.Zone:
                        if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment?.Zone != null && (locationWithUserAssignment.Zone?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (locationWithUserAssignment.Zone.UserAssignment, null);
                        }
                        else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location?.Zone != null && (intgrAssignmentIncludedLocAndProj.Location?.Zone?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (intgrAssignmentIncludedLocAndProj.Location?.Zone?.UserAssignment, null);
                        }
                        break;
                    case AssignmentModule.City:
                        if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment?.City != null && (locationWithUserAssignment.City?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (locationWithUserAssignment.City.UserAssignment, null);
                        }
                        else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location?.City != null && (intgrAssignmentIncludedLocAndProj.Location?.City?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (intgrAssignmentIncludedLocAndProj.Location?.City?.UserAssignment, null);
                        }
                        break;
                    case AssignmentModule.SubSource:
                        if (intgrAccUserAssignment != null && (intgrAccUserAssignment.UserIds?.Any() ?? false))
                        {
                            return (intgrAccUserAssignment, null);
                        }
                        break;
                    default:
                        break;
                }
            }
            return (intgrAccUserAssignment, null);
        }

        public static (UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project) GetMostPrioritizedUserAssignmentAsync(Guid intrAccId,
            LeadSource source,
            IntegrationAssignment? integrationAccount,
            List<AssignmentModule>? assignmentModules,
            Domain.Entities.GlobalSettings? globalSettings = null,
            FacebookAdsInfo? fbAd = null,
            FacebookLeadGenForm? fbForm = null,
            IntegrationAccountInfo? integrationAcc = null,
            Lrb.Domain.Entities.Project? projectWithAssignment = null,
            Location? locationWithUserAssignment = null)
        {
            var intgrAssgnId = Guid.Empty;
            UserAssignment? intgrAccUserAssignment = null;
            if (fbAd != null)
            {
                intgrAssgnId = fbAd?.Assignment?.Id ?? Guid.Empty;
                intgrAccUserAssignment = fbAd?.UserAssignment;
            }
            else if (fbForm != null)
            {
                intgrAssgnId = fbForm?.Assignment?.Id ?? Guid.Empty;
                intgrAccUserAssignment = fbForm?.UserAssignment;
            }
            else if (integrationAcc != null)
            {
                intgrAssgnId = integrationAcc?.Assignment?.Id ?? Guid.Empty;
                intgrAccUserAssignment = integrationAcc?.UserAssignment;
            }

            IntegrationAssignment? intgrAssignmentIncludedLocAndProj = integrationAcc?.Assignment;
            if (intgrAssignmentIncludedLocAndProj == null && projectWithAssignment == null && locationWithUserAssignment == null)
            {
                return (intgrAccUserAssignment, null);
            }
            if (assignmentModules == null || !assignmentModules.Any())
            {
                assignmentModules = AssignmentModule.DefaultModules;
            }
            assignmentModules = assignmentModules.Where(i => i != null).OrderBy(i => i.Priority).ToList();
            foreach (var assignmentModule in assignmentModules)
            {
                switch (assignmentModule.Name)
                {
                    case AssignmentModule.Project:
                        if (projectWithAssignment != null && (projectWithAssignment?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (projectWithAssignment.UserAssignment, null);
                        }
                        else if (intgrAssignmentIncludedLocAndProj?.Project != null && (intgrAssignmentIncludedLocAndProj.Project?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (intgrAssignmentIncludedLocAndProj.Project?.UserAssignment, intgrAssignmentIncludedLocAndProj.Project);
                        }
                        break;
                    case AssignmentModule.Location:
                        if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment != null && (locationWithUserAssignment.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (locationWithUserAssignment.UserAssignment, null);
                        }
                        else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location != null && (intgrAssignmentIncludedLocAndProj.Location?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (intgrAssignmentIncludedLocAndProj.Location?.UserAssignment, null);
                        }
                        break;
                    case AssignmentModule.Zone:
                        if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment?.Zone != null && (locationWithUserAssignment.Zone?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (locationWithUserAssignment.Zone.UserAssignment, null);
                        }
                        else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location?.Zone != null && (intgrAssignmentIncludedLocAndProj.Location?.Zone?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (intgrAssignmentIncludedLocAndProj.Location?.Zone?.UserAssignment, null);
                        }
                        break;
                    case AssignmentModule.City:
                        if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment?.City != null && (locationWithUserAssignment.City?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (locationWithUserAssignment.City.UserAssignment, null);
                        }
                        else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location?.City != null && (intgrAssignmentIncludedLocAndProj.Location?.City?.UserAssignment?.UserIds?.Any() ?? false))
                        {
                            return (intgrAssignmentIncludedLocAndProj.Location?.City?.UserAssignment, null);
                        }
                        break;
                    case AssignmentModule.SubSource:
                        if (intgrAccUserAssignment != null && (intgrAccUserAssignment.UserIds?.Any() ?? false))
                        {
                            return (intgrAccUserAssignment, null);
                        }
                        break;
                    default:
                        break;
                }
            }
            return (intgrAccUserAssignment, null);
        }




        public static async Task<(UserAssignment? UserAssignment, Lrb.Domain.Entities.Project? Project, int? Priority)> GetMostPrioritizedUserAssignmentAndPriorityAsync(Guid intrAccId,
            LeadSource source,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<AssignmentModule> assignmentModuleRepo,
            Domain.Entities.GlobalSettings? globalSettings = null,
            IRepositoryWithEvents<FacebookAdsInfo>? fbAdsRepo = null,
            IRepositoryWithEvents<FacebookLeadGenForm>? fbFormRepo = null,
            IRepositoryWithEvents<IntegrationAccountInfo>? integrationAccRepo = null,
            Lrb.Domain.Entities.Project? projectWithAssignment = null,
            Location? locationWithUserAssignment = null,
            int? priority = null,
            PropertyReferenceInfo? refInfoWithAssignment = null)
        {
            var intgrAssgnId = Guid.Empty;
            UserAssignment? intgrAccUserAssignment = null;
            if (fbAdsRepo != null)
            {
                var ad = await fbAdsRepo.FirstOrDefaultAsync(new FacebookAdsInfoByIdSpec(intrAccId), default);
                intgrAssgnId = ad?.Assignment?.Id ?? Guid.Empty;
                intgrAccUserAssignment = ad?.UserAssignment;
            }
            else if (fbFormRepo != null)
            {
                var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(intrAccId), default);
                intgrAssgnId = form?.Assignment?.Id ?? Guid.Empty;
                intgrAccUserAssignment = form?.UserAssignment;
            }
            else if (integrationAccRepo != null)
            {
                IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intrAccId);
                intgrAssgnId = integrationAccountInfo?.Assignment?.Id ?? Guid.Empty;
                intgrAccUserAssignment = integrationAccountInfo?.UserAssignment;
            }

            IntegrationAssignment? intgrAssignmentIncludedLocAndProj = await integrationAssignmentRepo.FirstOrDefaultAsync(new IntegrationAssignmentByIdSpec(intgrAssgnId), default);
            if (intgrAssignmentIncludedLocAndProj == null && projectWithAssignment == null && locationWithUserAssignment == null && refInfoWithAssignment == null)
            {
                return (intgrAccUserAssignment, null, null);
            }
            var assignmentModules = await assignmentModuleRepo.ListAsync(default);
            if (assignmentModules == null || !assignmentModules.Any())
            {
                assignmentModules = AssignmentModule.DefaultModules;
            }
            assignmentModules = assignmentModules.Where(i => i != null).OrderBy(i => i.Priority).ToList();

            if(priority != null || priority != default)
            {
                foreach (var assignmentModule in assignmentModules.SkipWhile(i => i.Priority <= (priority ?? default)))
                {
                    switch (assignmentModule.Name)
                    {
                        case AssignmentModule.Project:
                            if (projectWithAssignment != null && (projectWithAssignment.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (projectWithAssignment.UserAssignment, null, assignmentModule.Priority);
                            }
                            else if (intgrAssignmentIncludedLocAndProj?.Project != null && (intgrAssignmentIncludedLocAndProj.Project?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (intgrAssignmentIncludedLocAndProj.Project?.UserAssignment, intgrAssignmentIncludedLocAndProj.Project, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.Location:
                            if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment != null && (locationWithUserAssignment.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (locationWithUserAssignment.UserAssignment, null, assignmentModule.Priority);
                            }
                            else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location != null && (intgrAssignmentIncludedLocAndProj.Location?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (intgrAssignmentIncludedLocAndProj.Location?.UserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.Zone:
                            if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment?.Zone != null && (locationWithUserAssignment.Zone?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (locationWithUserAssignment.Zone.UserAssignment, null, assignmentModule.Priority);
                            }
                            else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location?.Zone != null && (intgrAssignmentIncludedLocAndProj.Location?.Zone?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (intgrAssignmentIncludedLocAndProj.Location?.Zone?.UserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.City:
                            if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment?.City != null && (locationWithUserAssignment.City?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (locationWithUserAssignment.City.UserAssignment, null, assignmentModule.Priority);
                            }
                            else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location?.City != null && (intgrAssignmentIncludedLocAndProj.Location?.City?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (intgrAssignmentIncludedLocAndProj.Location?.City?.UserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.SubSource:
                            if (intgrAccUserAssignment != null && (intgrAccUserAssignment.UserIds?.Any() ?? false) || ((intgrAccUserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased) && (intgrAccUserAssignment.UserAssignmentConfigurations?.Any() ?? false)))
                            {
                                return (intgrAccUserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.ReferenceId:
                            if (refInfoWithAssignment != null && (refInfoWithAssignment.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (refInfoWithAssignment.UserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
            else
            {
                foreach (var assignmentModule in assignmentModules)
                {
                    switch (assignmentModule.Name)
                    {
                        case AssignmentModule.Project:
                            if (projectWithAssignment != null && (projectWithAssignment.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (projectWithAssignment.UserAssignment, null, assignmentModule.Priority);
                            }
                            else if (intgrAssignmentIncludedLocAndProj?.Project != null && (intgrAssignmentIncludedLocAndProj.Project?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (intgrAssignmentIncludedLocAndProj.Project?.UserAssignment, intgrAssignmentIncludedLocAndProj.Project, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.Location:
                            if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment != null && (locationWithUserAssignment.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (locationWithUserAssignment.UserAssignment, null, assignmentModule.Priority);
                            }
                            else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location != null && (intgrAssignmentIncludedLocAndProj.Location?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (intgrAssignmentIncludedLocAndProj.Location?.UserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.Zone:
                            if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment?.Zone != null && (locationWithUserAssignment.Zone?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (locationWithUserAssignment.Zone.UserAssignment, null, assignmentModule.Priority);
                            }
                            else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location?.Zone != null && (intgrAssignmentIncludedLocAndProj.Location?.Zone?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (intgrAssignmentIncludedLocAndProj.Location?.Zone?.UserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.City:
                            if (globalSettings?.IsZoneLocationEnabled != false && locationWithUserAssignment?.City != null && (locationWithUserAssignment.City?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (locationWithUserAssignment.City.UserAssignment, null, assignmentModule.Priority);
                            }
                            else if (globalSettings?.IsZoneLocationEnabled != false && intgrAssignmentIncludedLocAndProj?.Location?.City != null && (intgrAssignmentIncludedLocAndProj.Location?.City?.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (intgrAssignmentIncludedLocAndProj.Location?.City?.UserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.SubSource:
                            if (intgrAccUserAssignment != null && (intgrAccUserAssignment.UserIds?.Any() ?? false) || ((intgrAccUserAssignment?.CategoryType == AssignmentCategoryType.PercentageBased) && (intgrAccUserAssignment.UserAssignmentConfigurations?.Any() ?? false)))
                            {
                                return (intgrAccUserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        case AssignmentModule.ReferenceId:
                            if (refInfoWithAssignment != null && (refInfoWithAssignment.UserAssignment?.UserIds?.Any() ?? false))
                            {
                                return (refInfoWithAssignment.UserAssignment, null, assignmentModule.Priority);
                            }
                            break;
                        default:
                            break;
                    }
                }
            }
            return (intgrAccUserAssignment, null, null);
        }









        public static async Task<IntegrationAssignment?> GetIntegrationAssignmentDetails(Guid intrAccId,
            LeadSource source,
            IRepositoryWithEvents<IntegrationAssignment> integrationAssignmentRepo,
            IRepositoryWithEvents<FacebookAdsInfo>? fbAdsRepo = null,
            IRepositoryWithEvents<FacebookLeadGenForm>? fbFormRepo = null,
            IRepositoryWithEvents<IntegrationAccountInfo>? integrationAccRepo = null)
        {
            var intgrAssgnId = Guid.Empty;
            IntegrationAssignment? intgrAssignmentIncludedLocAndProj;
            if (fbAdsRepo != null)
            {
                var ad = await fbAdsRepo.FirstOrDefaultAsync(new FacebookAdsInfoByIdSpec(intrAccId), default);
                intgrAssignmentIncludedLocAndProj = ad?.Assignment;
                intgrAssgnId = ad?.Assignment?.Id ?? Guid.Empty;
            }
            else if (fbFormRepo != null)
            {
                var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(intrAccId), default);
                intgrAssignmentIncludedLocAndProj = form?.Assignment;
                intgrAssgnId = form?.Assignment?.Id ?? Guid.Empty;
            }
            else if (integrationAccRepo != null)
            {
                IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intrAccId);
                intgrAssgnId = integrationAccountInfo?.Assignment?.Id ?? Guid.Empty;
            }

            intgrAssignmentIncludedLocAndProj = await integrationAssignmentRepo.FirstOrDefaultAsync(new IntegrationAssignmentByIdSpec(intgrAssgnId), default);
            return intgrAssignmentIncludedLocAndProj;
        }

        public static async Task<bool> HasValidAssignmentAsync(Guid intrAccId,
            LeadSource source,
            IRepositoryWithEvents<FacebookAdsInfo>? fbAdsRepo = null,
            IRepositoryWithEvents<FacebookLeadGenForm>? fbFormRepo = null,
            IRepositoryWithEvents<IntegrationAccountInfo>? integrationAccRepo = null)
        {
            UserAssignment? userAssignment = null;
            if (fbAdsRepo != null)
            {
                var ad = await fbAdsRepo.FirstOrDefaultAsync(new FacebookAdsInfoByIdSpec(intrAccId), default);
                userAssignment = ad?.Assignment?.Project?.UserAssignment?.UserIds?.Count() > 0 ? ad?.Assignment?.Project?.UserAssignment : ad?.Assignment?.Location?.UserAssignment?.UserIds?.Count() > 0 ? ad?.Assignment?.Location?.UserAssignment : ad?.UserAssignment;

            }
            else if (fbFormRepo != null)
            {
                var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(intrAccId), default);
                userAssignment = form?.Assignment?.Project?.UserAssignment?.UserIds?.Count() > 0 ? form?.Assignment?.Project?.UserAssignment : form?.Assignment?.Location?.UserAssignment?.UserIds?.Count() > 0 ? form?.Assignment?.Location?.UserAssignment : form?.UserAssignment;
            }
            else if (integrationAccRepo != null)
            {
                IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intrAccId);
                userAssignment = integrationAccountInfo?.Assignment?.Project?.UserAssignment?.UserIds?.Count() > 0 ? integrationAccountInfo?.Assignment?.Project?.UserAssignment : integrationAccountInfo?.Assignment?.Location?.UserAssignment?.UserIds?.Count() > 0 ? integrationAccountInfo?.Assignment?.Location?.UserAssignment : integrationAccountInfo?.UserAssignment;

            }
            return userAssignment != null && userAssignment.UserIds != null && userAssignment.UserIds.Count > 0;
        }

        public static async Task<(Lrb.Domain.Entities.Project? AssignedProject, Location? AssignedLocation)> GetAssignedProjLocAsync(Guid? adId = null, Guid? formId = null, Guid? intgrAccId = null,
            LeadSource? source = null, IRepositoryWithEvents<FacebookAdsInfo>? fbAdsRepo = null,
            IRepositoryWithEvents<FacebookLeadGenForm>? fbFormRepo = null, IRepositoryWithEvents<IntegrationAccountInfo>? integrationAccRepo = null)
        {
            Lrb.Domain.Entities.Project? proj = null;
            Location? loc = null;
            if (fbAdsRepo != null && adId != null && adId != Guid.Empty)
            {
                var ad = await fbAdsRepo.FirstOrDefaultAsync(new FacebookAdsInfoByIdSpec(adId.Value), default);
                proj = ad?.Assignment?.Project;
                loc = ad?.Assignment?.Location;
                if (proj != null && loc != null)
                {
                    return (proj, loc);
                }
            }
            if (proj == null && loc == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    proj = form?.Assignment?.Project;
                    loc = form?.Assignment?.Location;
                    if (proj != null && loc != null)
                    {
                        return (proj, loc);
                    }
                }
                if (loc == null && proj == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    proj = integrationAccountInfo?.Assignment?.Project;
                    loc = integrationAccountInfo?.Assignment?.Location;
                    if (proj != null && loc != null)
                    {
                        return (proj, loc);
                    }
                }
                else if (proj == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    proj = integrationAccountInfo?.Assignment?.Project;
                }
                else if (loc == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    loc = integrationAccountInfo?.Assignment?.Location;
                }
            }
            else if (proj == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    proj = form?.Assignment?.Project;
                }
                if (proj == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    proj = integrationAccountInfo?.Assignment?.Project;
                }
            }
            else if (loc == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    loc = form?.Assignment?.Location;
                }
                if (loc == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    loc = integrationAccountInfo?.Assignment?.Location;
                }
            }
            return (proj, loc);
        }
        public static async Task<IntegrationAssignment> GetAssignedProjLocAsyncV1(Guid? adId = null, Guid? formId = null, Guid? intgrAccId = null,
            LeadSource? source = null, IRepositoryWithEvents<FacebookAdsInfo>? fbAdsRepo = null,
            IRepositoryWithEvents<FacebookLeadGenForm>? fbFormRepo = null, IRepositoryWithEvents<IntegrationAccountInfo>? integrationAccRepo = null)
        {
            Lrb.Domain.Entities.Project? proj = null;
            Location? loc = null;
            Campaign? campaign = null;
            Domain.Entities.ChannelPartner? channelPartner = null;
            Domain.Entities.Property? property = null;
            Domain.Entities.Agency? agency = null;
            if (fbAdsRepo != null && adId != null && adId != Guid.Empty)
            {
                var ad = await fbAdsRepo.FirstOrDefaultAsync(new FacebookAdsInfoByIdSpec(adId.Value), default);
                proj = ad?.Assignment?.Project;
                loc = ad?.Assignment?.Location;
                campaign = ad?.Assignment?.Campaign;
                agency = ad?.Assignment?.Agency;
                channelPartner = ad?.Assignment?.ChannelPartner;
                property = ad?.Assignment?.Property;
                if (proj != null && loc != null && campaign != null && property != null && agency != null && channelPartner != null)
                {
                    return new IntegrationAssignment
                    {
                        Project = proj,
                        Location = loc,
                        Campaign = campaign,
                        Property = property,
                        Agency = agency,
                        ChannelPartner = channelPartner
                    };
                }
            }
            if (proj == null && loc == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    proj = form?.Assignment?.Project;
                    loc = form?.Assignment?.Location;
                    campaign = form?.Assignment?.Campaign;
                    agency = form?.Assignment?.Agency;
                    channelPartner = form?.Assignment?.ChannelPartner;
                    property = form?.Assignment?.Property;
                    if (proj != null && loc != null && campaign != null && property != null && agency != null && channelPartner != null)
                    {
                        return new IntegrationAssignment
                        {
                            Project = proj,
                            Location = loc,
                            Campaign = campaign,
                            Property = property,
                            Agency = agency,
                            ChannelPartner = channelPartner
                        };
                    }
                }
                if (proj == null && loc == null && campaign == null && property == null && agency == null && channelPartner == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    proj = integrationAccountInfo?.Assignment?.Project;
                    loc = integrationAccountInfo?.Assignment?.Location;
                    campaign = integrationAccountInfo?.Assignment?.Campaign;
                    agency = integrationAccountInfo?.Assignment?.Agency;
                    channelPartner = integrationAccountInfo?.Assignment?.ChannelPartner;
                    property = integrationAccountInfo?.Assignment?.Property;
                    if (proj != null && loc != null && campaign != null && agency != null && channelPartner != null && property != null)
                    {
                        return new IntegrationAssignment
                        {
                            Project = proj,
                            Location = loc,
                            Campaign = campaign,
                            Property = property,
                            Agency = agency,
                            ChannelPartner = channelPartner
                        };
                    }
                }
                else if (proj == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    proj = integrationAccountInfo?.Assignment?.Project;
                }
                else if (loc == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    loc = integrationAccountInfo?.Assignment?.Location;
                }
                else if (agency == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    agency = integrationAccountInfo?.Assignment?.Agency;
                }
                else if (campaign == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    campaign = integrationAccountInfo?.Assignment?.Campaign;
                }
                else if (property == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    property = integrationAccountInfo?.Assignment?.Property;
                }
                else if (channelPartner == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    channelPartner = integrationAccountInfo?.Assignment?.ChannelPartner;
                }
            }
            else if (proj == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    proj = form?.Assignment?.Project;
                }
                if (proj == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    proj = integrationAccountInfo?.Assignment?.Project;
                }
            }
            else if (loc == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    loc = form?.Assignment?.Location;
                }
                if (loc == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    loc = integrationAccountInfo?.Assignment?.Location;
                }
            }
            else if (loc == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    loc = form?.Assignment?.Location;
                }
                if (loc == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    loc = integrationAccountInfo?.Assignment?.Location;
                }
            }
            else if (agency == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    agency = form?.Assignment?.Agency;
                }
                if (agency == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    agency = integrationAccountInfo?.Assignment?.Agency;
                }
            }
            else if (property == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    property = form?.Assignment?.Property;
                }
                if (agency == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    property = integrationAccountInfo?.Assignment?.Property;
                }
            }
            else if (channelPartner == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    channelPartner = form?.Assignment?.ChannelPartner;
                }
                if (agency == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    channelPartner = integrationAccountInfo?.Assignment?.ChannelPartner;
                }
            }
            else if (campaign == null)
            {
                if (fbFormRepo != null && formId != null && formId != Guid.Empty)
                {
                    var form = await fbFormRepo.FirstOrDefaultAsync(new FacebookLeadGenFormByIdSpec(formId.Value), default);
                    campaign = form?.Assignment?.Campaign;
                }
                if (agency == null)
                {
                    IntegrationAccountInfo? integrationAccountInfo = await GetIntegrationAccountAsync(integrationAccRepo, source, intgrAccId);
                    campaign = integrationAccountInfo?.Assignment?.Campaign;
                }
            }
            return new IntegrationAssignment
            {
                Project = proj,
                Location = loc,
                Campaign = campaign,
                Property = property,
                Agency = agency,
                ChannelPartner = channelPartner
            };
        }
        private static async Task<IntegrationAccountInfo?> GetIntegrationAccountAsync(IRepositoryWithEvents<IntegrationAccountInfo>? integrationAccRepo, LeadSource? source, Guid? intgrAccId)
        {
            IntegrationAccountInfo? integrationAccountInfo = null;
            if (integrationAccRepo != null && intgrAccId != null && intgrAccId != Guid.Empty && source != null)
            {
                switch (source)
                {
                    case LeadSource.Facebook:
                        integrationAccountInfo = await integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccInfoByFacebookIdOrIdSpec(intgrAccId.Value), CancellationToken.None);
                        break;
                    case LeadSource.Gmail:
                        integrationAccountInfo = await integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccByGmailIdOrIdSpec(intgrAccId.Value), CancellationToken.None);
                        break;
                    case LeadSource.GoogleAds:
                        integrationAccountInfo = await integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccInfoByGoogleAdIdOrId(intgrAccId.Value), CancellationToken.None);
                        break;
                    default:
                        integrationAccountInfo = await integrationAccRepo.FirstOrDefaultAsync(new IntegrationAccountByIdSpec(intgrAccId.Value), CancellationToken.None);
                        break;
                }
            }
            return integrationAccountInfo;
        }
        public static async Task<IVRAssignment?> GetAssignmentDetailsForIVRAccountAsync(IRepositoryWithEvents<IntegrationAccountInfo>? integrationAccRepo, LeadSource? source, Guid? intgrAccId, string? virtualNumber)
        {
            var integrationAcc = await integrationAccRepo?.FirstOrDefaultAsync(new IntegrationAccountByIdSpec(intgrAccId ?? default));
            if(integrationAcc == null || virtualNumber == null)
            {
                return null;
            }
            var ivrAssignment = integrationAcc.IVRAssignments?.FirstOrDefault(i => i.VirtualNumber != null && i.VirtualNumber.Contains(virtualNumber));
            return (ivrAssignment);
        }
    }

}
