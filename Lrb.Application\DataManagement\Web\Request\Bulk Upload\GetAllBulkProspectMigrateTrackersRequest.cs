﻿using Lrb.Domain.Entities.DataManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Request.Bulk_Upload
{
    public class GetAllBulkProspectMigrateTrackersRequest : PaginationFilter, IRequest<PagedResponse<DataMigrateTracker, string>>
    {
    }
    public class GetAllBulkMigrateTrackersRequestHandler : IRequestHandler<GetAllBulkProspectMigrateTrackersRequest, PagedResponse<DataMigrateTracker, string>>
    {
        private readonly IRepositoryWithEvents<DataMigrateTracker> _trackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAllBulkMigrateTrackersRequestHandler(IRepositoryWithEvents<DataMigrateTracker> trackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _trackerRepo = trackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<DataMigrateTracker, string>> Handle(GetAllBulkProspectMigrateTrackersRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _trackerRepo.ListAsync(new DataMigrateTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new DataMigrateTrackerCountSpec( subIds), cancellationToken);
            return new(trackers, totalCount);
        }
    }

    public class DataMigrateTrackerSpec : EntitiesByPaginationFilterSpec<DataMigrateTracker>
    {
        public DataMigrateTrackerSpec(GetAllBulkProspectMigrateTrackersRequest filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class DataMigrateTrackerCountSpec : Specification<DataMigrateTracker>
    {
        public DataMigrateTrackerCountSpec( List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
