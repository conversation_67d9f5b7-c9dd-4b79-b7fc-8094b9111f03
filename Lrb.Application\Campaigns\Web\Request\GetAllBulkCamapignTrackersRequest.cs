﻿using Lrb.Application.Agency.Web;
using Lrb.Application.Campaigns.Spec;
using Lrb.Domain.Entities.Marketing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Campaigns.Request
{
    public class GetAllBulkCamapignTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
    }
    public class GetAllBulkCamapignTrackersRequestHandler : IRequestHandler<GetAllBulkCamapignTrackersRequest, PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> _trackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAllBulkCamapignTrackersRequestHandler(IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> trackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _trackerRepo = trackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<BulkMarketingAgencyUploadTracker, string>> Handle(GetAllBulkCamapignTrackersRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _trackerRepo.ListAsync(new CamapignTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new CamapignTrackerCountSpec(subIds), cancellationToken);
            return new(trackers, totalCount);
        }
    }
}
