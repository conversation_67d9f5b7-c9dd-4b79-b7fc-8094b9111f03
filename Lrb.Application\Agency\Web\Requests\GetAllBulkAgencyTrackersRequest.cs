﻿using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.Agency.Web.Requests
{
    public class GetAllBulkAgencyTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
    }
    public class GetAllBulkAgencyTrackersRequestHandler : IRequestHandler<GetAllBulkAgencyTrackersRequest, PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> _trackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAllBulkAgencyTrackersRequestHandler(IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> trackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _trackerRepo = trackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<BulkMarketingAgencyUploadTracker, string>> Handle(GetAllBulkAgencyTrackersRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _trackerRepo.ListAsync(new AgencyTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new AgencyTrackerCountSpec(subIds), cancellationToken);
            return new(trackers, totalCount);
        }
    }
}
