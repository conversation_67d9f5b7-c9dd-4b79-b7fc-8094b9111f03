﻿using Lrb.Application.DataManagement.Web.Export.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Specs
{
    public class GetExportProspectTrackerRequestSpecs : EntitiesByPaginationFilterSpec<ExportProspectTracker>
    {
        public GetExportProspectTrackerRequestSpecs(GetExportTrackerRequest filter, List<Guid> subIds) : base(filter) 
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy)).OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetExportProspectTrackerCountSpecs : Specification<ExportProspectTracker>
    {
        public GetExportProspectTrackerCountSpecs( List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
