﻿using Lrb.Application.LeadRotation.Web.Dtos;

namespace Lrb.Application.Common.LeadRotation
{
    public interface ILeadRotationService
    {
        Task<(bool, LeadsAssignRotationInfoDto)> RotateAssignLeadsAsync(LeadsAssignRotationInfoDto dto, Guid leadId);
       // Task<string> ScheduleLeadRotation(Guid leadId,Guid? accountId = null);
        Task<bool> ScheduleTeamRetentionRotation(List<Domain.Entities.Lead> leads);
        Task<(bool, LeadsAssignRotationInfoDto)> RotateTeamRetentionLeadsAsync(LeadsAssignRotationInfoDto dto, Guid leadId);
        Task<string> ScheduleTeamLeadRotation(Guid leadId, Guid? accountId = null);
        Task InitiateAutoDailetCalllingAsync(Guid userId);
    }
}
