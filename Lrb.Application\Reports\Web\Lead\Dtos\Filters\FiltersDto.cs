﻿using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Requests;

namespace Lrb.Application.Reports.Web.Dtos.FiltersName
{
    public class FormattedFiltersDto
    {
        public string? SearchText { get; set; }
        public List<Guid>? UserIds { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public List<string>? Localites { get; set; }
        public List<LeadSource>? Sources { get; set; }
        public List<string>? SubSources { get; set; }
        public List<string>? Projects { get; set; }
        public DateTime? FromDate { get; set; }
        public List<string>? AgencyNames { get; set; }
        public DateTime? ToDate { get; set; }
        public DateType? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public List<string>? SelectedColumns { get; set; }
        public bool? ShouldShowAll { get; set; }
        public bool? ShouldShowPercentage { get; set; }
        public DateTime? CallLogFromDate { get; set; }
        public DateTime? CallLogToDate { get; set; }
        public bool IsWithAssociatedProjects { get; set; } = false;
    }

    public class FiltersDto
    {
        public string? SearchText { get; set; }
        public string? UserNames { get; set; }
        public string? Sources { get; set; }
        public string? SubSources { get; set; }
        public string? City { get; set; }
        public string? State { get; set; }
        public string? Locality { get; set; }
        public string? Projects { get; set; }
        public DateTime? FromDate { get; set; }
        public string? AgencyNames { get; set; }
        public DateTime? ToDate { get; set; }
        public string? DateType { get; set; }
        public bool IsWithTeam { get; set; }
        public string? SelectedColumns { get; set; }
        public bool? ShouldShowAll { get; set; }
        public bool? ShouldShowPercentage { get; set; }
        public DateTime? CallLogFromDate { get; set; }
        public DateTime? CallLogToDate { get; set; }
        public bool IsWithAssociatedProjects { get; set; } = false;
    }

    public class PropertyExportFilter
    {
        public PropertyDimensionDto? PropertySize { get; set; }
        public List<string>? Locations { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public EnquiryType? EnquiredFor { get; set; }
        public double? NoOfBHK { get; set; }
        public string? Ratings { get; set; }
        public PropertyStatus? PropertyStatus { get; set; }
        public List<Guid>? PropertyTypes { get; set; }
        public List<Guid>? PropertySubTypes { get; set; }
        public Guid? BasePropertyTypeId { get; set; }
        public string? PropertySearch { get; set; }
        public PropertyDateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public List<Guid>? Amenities { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public DateTime? FromPossessionDate { get; set; }
        public DateTime? ToPossessionDate { get; set; }
        public List<string>? Projects { get; set; }
        public List<FurnishStatus>? FurnishStatuses { get; set; }
        public List<SaleType>? SaleTypes { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public Facing? Facing { get; set; }
        public List<int>? NoOfBathrooms { get; set; }
        public List<int>? NoOfLivingrooms { get; set; }
        public List<int>? NoOfBedrooms { get; set; }
        public List<int>? NoOfUtilites { get; set; }
        public List<int>? NoOfKitchens { get; set; }
        public List<int>? NoOfBalconies { get; set; }
        public List<int>? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public long? MaxBudget { get; set; }
        public long? MinBudget { get; set; }
        public string? UserNames { get; set; }
    }



    public class PropertyFormettedExportFilter
    {
        public string? PropertySize { get; set; }
        public string? CarpetArea { get; set; }
        public string? BuildUpArea { get; set; }
        public string? SaleableArea { get; set; }
        public string? Locations { get; set; }
        public string? Cities { get; set; }
        public string? States { get; set; }
        public string? EnquiredFor { get; set; }
        public string? NoOfBHKs { get; set; }
        public string? Ratings { get; set; }
        public string? PropertyStatus { get; set; }
        public string? PropertyTypes { get; set; }
        public string? PropertySubTypes { get; set; }
        public string? BasePropertyTypeId { get; set; }
        public string? PropertySearch { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public string? Amenities { get; set; }
        public string? BHKTypes { get; set; }
        public string? FromPossessionDate { get; set; }
        public string? ToPossessionDate { get; set; }
        public string? Projects { get; set; }
        public string? FurnishStatuses { get; set; }
        public string? SaleTypes { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public string? Facing { get; set; }
        public string? NoOfBathrooms { get; set; }
        public string? NoOfLivingrooms { get; set; }
        public string? NoOfBedrooms { get; set; }
        public string? NoOfUtilites { get; set; }
        public string? NoOfKitchens { get; set; }
        public string? NoOfBalconies { get; set; }
        public string? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public string? UserNames { get; set; }
        public string? NetArea { get; set; }


    }


    #region Listing Management Export
    public class PropertyExportFilterForListingManagement
    {
        public PropertyDimensionDto? PropertySize { get; set; }
        public List<string>? Locations { get; set; }
        public List<string>? Cities { get; set; }
        public List<string>? States { get; set; }
        public EnquiryType? EnquiredFor { get; set; }
        public double? NoOfBHK { get; set; }
        public string? Ratings { get; set; }
        public PropertyStatus? PropertyStatus { get; set; }
        public List<Guid>? PropertyTypes { get; set; }
        public List<Guid>? PropertySubTypes { get; set; }
        public Guid? BasePropertyTypeId { get; set; }
        public string? PropertySearch { get; set; }
        public PropertyDateType? DateType { get; set; }
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public List<Guid>? Amenities { get; set; }
        public List<BHKType>? BHKTypes { get; set; }
        public DateTime? FromPossessionDate { get; set; }
        public DateTime? ToPossessionDate { get; set; }
        public List<string>? Projects { get; set; }
        public List<FurnishStatus>? FurnishStatuses { get; set; }
        public List<SaleType>? SaleTypes { get; set; }
        public string? PropertyTitle { get; set; }
        public Facing? Facing { get; set; }
        public List<int>? NoOfBathrooms { get; set; }
        public List<int>? NoOfLivingrooms { get; set; }
        public List<int>? NoOfBedrooms { get; set; }
        public List<int>? NoOfUtilites { get; set; }
        public List<int>? NoOfKitchens { get; set; }
        public List<int>? NoOfBalconies { get; set; }
        public List<int>? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public long? MaxBudget { get; set; }
        public long? MinBudget { get; set; }
        public string? UserNames { get; set; }
        public PropertyVisiblity PropertyVisiblity { get; set; }
        public FirstLevelFilter FirstLevelFilter { get; set; }
        public SecondLevelFilter? SecondLevelFilter { get; set; }
        public CompletionStatus? CompletionStatus { get; set; }
        public ListingLevel? ListingLevel { get; set; }
        public List<string>? Communities { get; set; }
        public List<string>? SubCommunities { get; set; }
        public List<Guid>? ListingSourceIds { get; set; }
        public List<string>? OwnerName { get; set; }
        public UaeEmirate? UaeEmirate { get; set; }
    }

    public class PropertyFormettedExportFilterForListingManagement
    {
        public string? PropertySize { get; set; }
        public string? CarpetArea { get; set; }
        public string? BuildUpArea { get; set; }
        public string? SaleableArea { get; set; }
        public string? Locations { get; set; }
        public string? Cities { get; set; }
        public string? States { get; set; }
        public string? EnquiredFor { get; set; }
        public string? NoOfBHKs { get; set; }
        public string? Ratings { get; set; }
        public string? PropertyStatus { get; set; }
        public string? PropertyTypes { get; set; }
        public string? PropertySubTypes { get; set; }
        public string? BasePropertyTypeId { get; set; }
        public string? PropertySearch { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public string? Amenities { get; set; }
        public string? BHKTypes { get; set; }
        public string? FromPossessionDate { get; set; }
        public string? ToPossessionDate { get; set; }
        public string? Projects { get; set; }
        public string? FurnishStatuses { get; set; }
        public string? SaleTypes { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public string? Facing { get; set; }
        public string? NoOfBathrooms { get; set; }
        public string? NoOfLivingrooms { get; set; }
        public string? NoOfBedrooms { get; set; }
        public string? NoOfUtilites { get; set; }
        public string? NoOfKitchens { get; set; }
        public string? NoOfBalconies { get; set; }
        public string? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public string? UserNames { get; set; }
        public string? PropertyVisiblity { get; set; }
        public string? FirstLevelFilter { get; set; }
        public string? SecondLevelFilter { get; set; }
        public string? CompletionStatus { get; set; }
        public string? ListingLevel { get; set; }
        public string? Communities { get; set; }
        public string? SubCommunities { get; set; }
        public string? ListingSourceIds { get; set; }
        public string? NetArea { get; set; }


    }

    public class PropertyFormettedExportFilterForListingManagementV2
    {
        public string? PropertySize { get; set; }
        public string? TowerNames { get; set; }
        public string? Communities { get; set; }
        public string? SubCommunities { get; set; }
        public string? Cities { get; set; }
        public string? EnquiredFor { get; set; }
        public string? BR { get; set; }
        public string? PropertyStatus { get; set; }
        public string? PropertyTypes { get; set; }
        public string? PropertySubTypes { get; set; }
        public string? BasePropertyTypeId { get; set; }
        public string? PropertySearch { get; set; }
        public long? MinPrice { get; set; }
        public long? MaxPrice { get; set; }
        public string? Amenities { get; set; }
        public string? FromPossessionDate { get; set; }
        public string? ToPossessionDate { get; set; }
        public string? Projects { get; set; }
        public string? FurnishStatuses { get; set; }
        public string? OwnerName { get; set; }
        public string? PropertyTitle { get; set; }
        public string? Facing { get; set; }
        public string? NoOfBathrooms { get; set; }
        public string? NoOfBedrooms { get; set; }
        public string? NoOfFloor { get; set; }
        public int? FloorNumber { get; set; }
        public string? UserNames { get; set; }
        public string? PropertyVisiblity { get; set; }
        public string? FirstLevelFilter { get; set; }
        public string? SecondLevelFilter { get; set; }
        public string? CompletionStatus { get; set; }
        public string? ListingLevel { get; set; }
        public string? ListingSourceIds { get; set; }
        public string? UaeEmirate { get; set; }
    }

}

    

#endregion




