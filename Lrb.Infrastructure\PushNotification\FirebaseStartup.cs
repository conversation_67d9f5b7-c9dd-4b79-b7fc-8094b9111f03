using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.PushNotification;
using Lrb.Infrastructure.BackgroundJobs;
using Lrb.Infrastructure.PushNotification.Firebase;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Lrb.Infrastructure.PushNotification
{
    /// <summary>
    /// Firebase-based notification service startup configuration
    /// Replaces Hangfire and AWS Pinpoint with Firebase services
    /// </summary>
    public static class FirebaseStartup
    {
        /// <summary>
        /// Add Firebase-based notification services to the service collection
        /// This replaces the original AddNotification method with Firebase implementations
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="config">Configuration</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddFirebaseNotification(this IServiceCollection services, IConfiguration config)
        {
            // Configure Firebase settings
            services.Configure<FirebaseSetting>(config.GetSection(nameof(FirebaseSetting)));
            services.Configure<MobileFirebaseSetting>(config.GetSection(nameof(MobileFirebaseSetting)));

            // Register Firebase job service (replaces Hangfire)
            services.AddTransient<IFirebaseJobService, FirebaseJobService>();

            // Register Firebase notification sender service (replaces NotificationSenderService)
            services.AddTransient<INotificationSenderService, FirebaseNotificationSenderService>();

            // Keep existing services that don't depend on AWS Pinpoint or Hangfire
            services.AddTransient<INotificationService, NotificationService>();
            services.AddTransient<INotificationMessageBuilder, NotificationMessageBuilder>();
            services.AddTransient<ITemplateNotificationService, TemplateNotificationService>();

            return services;
        }

        /// <summary>
        /// Add Firebase-based notification services while maintaining backward compatibility
        /// This method allows gradual migration from the old service to the new one
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="config">Configuration</param>
        /// <param name="useFirebase">Whether to use Firebase implementation (default: true)</param>
        /// <returns>Service collection for chaining</returns>
        public static IServiceCollection AddNotificationWithFirebase(this IServiceCollection services, IConfiguration config, bool useFirebase = true)
        {
            // Configure Firebase settings
            services.Configure<FirebaseSetting>(config.GetSection(nameof(FirebaseSetting)));
            services.Configure<MobileFirebaseSetting>(config.GetSection(nameof(MobileFirebaseSetting)));

            if (useFirebase)
            {
                // Use Firebase implementations
                services.AddTransient<IFirebaseJobService, FirebaseJobService>();
                services.AddTransient<INotificationSenderService, FirebaseNotificationSenderService>();
            }
            else
            {
                // Fall back to original implementations (requires Hangfire and AWS Pinpoint)
                // This would require the original Startup.AddNotification method
                throw new NotSupportedException("Original implementation requires Hangfire and AWS Pinpoint dependencies. Use AddFirebaseNotification instead.");
            }

            // Common services (independent of implementation)
            services.AddTransient<INotificationService, NotificationService>();
            services.AddTransient<INotificationMessageBuilder, NotificationMessageBuilder>();
            services.AddTransient<ITemplateNotificationService, TemplateNotificationService>();

            return services;
        }
    }
}
