using System.Linq.Expressions;

namespace Lrb.Application.Common.Interfaces
{
    /// <summary>
    /// Firebase-based job service interface that replaces Hangfire functionality
    /// Uses Firebase Cloud Functions and Cloud Scheduler for job scheduling
    /// </summary>
    public interface IFirebaseJobService : ITransientService
    {
        /// <summary>
        /// Enqueue a job for immediate execution using Firebase Cloud Functions
        /// </summary>
        Task<string> EnqueueAsync(Expression<Action> methodCall);

        /// <summary>
        /// Enqueue a job for immediate execution using Firebase Cloud Functions
        /// </summary>
        Task<string> EnqueueAsync(Expression<Func<Task>> methodCall);

        /// <summary>
        /// Enqueue a job for immediate execution using Firebase Cloud Functions
        /// </summary>
        Task<string> EnqueueAsync<T>(Expression<Action<T>> methodCall);

        /// <summary>
        /// Enqueue a job for immediate execution using Firebase Cloud Functions
        /// </summary>
        Task<string> EnqueueAsync<T>(Expression<Func<T, Task>> methodCall);

        /// <summary>
        /// Schedule a job for delayed execution using Firebase Cloud Scheduler
        /// </summary>
        Task<string> ScheduleAsync(Expression<Action> methodCall, TimeSpan delay);

        /// <summary>
        /// Schedule a job for delayed execution using Firebase Cloud Scheduler
        /// </summary>
        Task<string> ScheduleAsync(Expression<Func<Task>> methodCall, TimeSpan delay);

        /// <summary>
        /// Schedule a job for execution at a specific time using Firebase Cloud Scheduler
        /// </summary>
        Task<string> ScheduleAsync(Expression<Action> methodCall, DateTimeOffset enqueueAt);

        /// <summary>
        /// Schedule a job for execution at a specific time using Firebase Cloud Scheduler
        /// </summary>
        Task<string> ScheduleAsync(Expression<Func<Task>> methodCall, DateTimeOffset enqueueAt);

        /// <summary>
        /// Schedule a job for delayed execution using Firebase Cloud Scheduler
        /// </summary>
        Task<string> ScheduleAsync<T>(Expression<Action<T>> methodCall, TimeSpan delay);

        /// <summary>
        /// Schedule a job for delayed execution using Firebase Cloud Scheduler
        /// </summary>
        Task<string> ScheduleAsync<T>(Expression<Func<T, Task>> methodCall, TimeSpan delay);

        /// <summary>
        /// Schedule a job for execution at a specific time using Firebase Cloud Scheduler
        /// </summary>
        Task<string> ScheduleAsync<T>(Expression<Action<T>> methodCall, DateTimeOffset enqueueAt);

        /// <summary>
        /// Schedule a job for execution at a specific time using Firebase Cloud Scheduler
        /// </summary>
        Task<string> ScheduleAsync<T>(Expression<Func<T, Task>> methodCall, DateTimeOffset enqueueAt);

        /// <summary>
        /// Delete a scheduled job using Firebase Cloud Scheduler
        /// </summary>
        Task<bool> DeleteAsync(string jobId);

        /// <summary>
        /// Create a recurring job using Firebase Cloud Scheduler with cron expression
        /// </summary>
        Task CreateRecurringJobAsync(string jobId, Expression<Func<Task>> methodCall, string cronExpression);

        /// <summary>
        /// Create a recurring job using Firebase Cloud Scheduler with cron expression
        /// </summary>
        Task CreateRecurringJobAsync<T>(string jobId, Expression<Func<T, Task>> methodCall, string cronExpression);

        /// <summary>
        /// Create a recurring job using Firebase Cloud Scheduler with cron expression
        /// </summary>
        Task CreateRecurringJobAsync(string jobId, Expression<Action> methodCall, string cronExpression);

        /// <summary>
        /// Create a recurring job using Firebase Cloud Scheduler with cron expression
        /// </summary>
        Task CreateRecurringJobAsync<T>(string jobId, Expression<Action<T>> methodCall, string cronExpression);

        /// <summary>
        /// Delete a recurring job if it exists
        /// </summary>
        Task DeleteRecurringJobIfExistsAsync(string jobId);

        /// <summary>
        /// Trigger a recurring job by ID
        /// </summary>
        Task TriggerRecurringJobByIdAsync(string jobId);

        /// <summary>
        /// Get the daily cron expression
        /// </summary>
        string DailyCron { get; }
    }
}
