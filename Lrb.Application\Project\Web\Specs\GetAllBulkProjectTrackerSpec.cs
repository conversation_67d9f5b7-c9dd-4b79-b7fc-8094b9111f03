﻿using Lrb.Application.Project.Web.Requests.Bulk_Upload;

namespace Lrb.Application.Project.Web
{
    public class GetAllBulkProjectTrackerSpec : EntitiesByPaginationFilterSpec<BulkProjectUploadTracker>
    {
        public GetAllBulkProjectTrackerSpec(GetProjectBulkUploadTrackersRequest filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetBulkProjectTrackerCountSpec : Specification<BulkProjectUploadTracker>
    {
        public GetBulkProjectTrackerCountSpec( List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
