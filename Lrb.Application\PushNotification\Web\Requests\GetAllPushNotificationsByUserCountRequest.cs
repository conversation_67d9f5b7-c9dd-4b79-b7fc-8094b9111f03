﻿using Lrb.Application.PushNotification.Web.Specs;

namespace Lrb.Application.PushNotification.Web.Requests
{
    public class GetAllPushNotificationsByUserCountRequest : IRequest<Response<long>>
    {

    }
    public class GetAllPushNotificationsByUserCountRequestHandler : IRequestHandler<GetAllPushNotificationsByUserCountRequest, Response<long>>
    {
        private readonly ICurrentUser _currentUserRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<LocalNotification> _localNotificationRepo;

        public GetAllPushNotificationsByUserCountRequestHandler(
                ICurrentUser currentUserRepo,
                IDapperRepository dapperRepository,
                IRepositoryWithEvents<LocalNotification> localNotificationRepo)
        {
            _currentUserRepo = currentUserRepo;
            _dapperRepository = dapperRepository;
            _localNotificationRepo = localNotificationRepo;
        }
        public async Task<Response<long>> Handle(GetAllPushNotificationsByUserCountRequest request, CancellationToken cancellationToken)
        {
            Guid currentUserId = _currentUserRepo.GetUserId();
            var notificationsCount = await _dapperRepository.GetTotalNotificationsCountAsync(currentUserId,_currentUserRepo.GetTenant());
            //long localNotificationsCount = await _localNotificationRepo.CountAsync(new GetLocalNotificationsByUserSpec(currentUserId), cancellationToken);
            return new()
            {
                Data = notificationsCount,
                Succeeded = true
            };
        }
    }
}
