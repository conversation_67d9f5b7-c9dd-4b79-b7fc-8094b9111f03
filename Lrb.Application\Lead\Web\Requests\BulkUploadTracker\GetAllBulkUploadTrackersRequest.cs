﻿using Lrb.Application.Common.Persistence;

namespace Lrb.Application.Lead.Web.Requests.BulkUploadTracker
{
    public class GetAllBulkUploadTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkLeadUploadTracker, string>>
    {
    }
    public class GetAllBulkUploadTrackersRequestHandler : IRequestHandler<GetAllBulkUploadTrackersRequest, PagedResponse<BulkLeadUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkLeadUploadTracker> _trackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
    
        public GetAllBulkUploadTrackersRequestHandler(IRepositoryWithEvents<BulkLeadUploadTracker> trackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _trackerRepo = trackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<BulkLeadUploadTracker, string>> Handle(GetAllBulkUploadTrackersRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _trackerRepo.ListAsync(new BulkLeadUploadSpec(request, subIds), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new BulkLeadUploadCountSpec(subIds), cancellationToken);
            return new(trackers, totalCount);
        }
    }

    public class BulkLeadUploadSpec : EntitiesByPaginationFilterSpec<BulkLeadUploadTracker>
    {
        public BulkLeadUploadSpec(GetAllBulkUploadTrackersRequest filter,List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class BulkLeadUploadCountSpec : Specification<BulkLeadUploadTracker>
    {
        public BulkLeadUploadCountSpec(List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
