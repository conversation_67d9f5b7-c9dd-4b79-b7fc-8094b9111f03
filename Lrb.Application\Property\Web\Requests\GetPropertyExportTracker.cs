﻿using Lrb.Application.ExportTemplate;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead;
using Lrb.Application.Property.Web.Dtos;
using Microsoft.Graph;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Lrb.Application.Property.Web.Specs;



namespace Lrb.Application.Property.Web.Requests
{
    public class GetPropertyExportTracker : PaginationFilter, IRequest<PagedResponse<ExportPropertyTrackerDto, string>>
    {



    }
    public class GetPropertyExportTrackerhandler : IRequestHandler<GetPropertyExportTracker, PagedResponse<ExportPropertyTrackerDto, string>>
    {
        private readonly IReadRepository<ExportPropertyTracker> _exportPropertyTrackerRepo;
        public readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetPropertyExportTrackerhandler(IReadRepository<ExportPropertyTracker> exportLeadTrackerRepo,
            IUserService userService, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _exportPropertyTrackerRepo = exportLeadTrackerRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<ExportPropertyTrackerDto, string>> Handle(GetPropertyExportTracker request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var leadTrackers = await _exportPropertyTrackerRepo.ListAsync(new ExportPropertyTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _exportPropertyTrackerRepo.CountAsync(new GetExportPropertyTrackersCountSpec(subIds), cancellationToken);
            var leadTrackerDto = leadTrackers.Adapt<List<ExportPropertyTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(leadTrackerDto.Select(i => i.CreatedBy.ToString()).Distinct().ToList(), cancellationToken);
            foreach (var leadTracker in leadTrackerDto)
            {
                leadTracker.ExportTemplate = (JsonConvert.DeserializeObject<ViewExportTemplateDto>(leadTracker?.Template ?? string.Empty) ?? null);
                leadTracker.ExportedUser = users?.FirstOrDefault(i => leadTracker.CreatedBy == i.Id)?.Adapt<Team.Web.UserDto>();
            }
            return new(leadTrackerDto, totalCount);
        }
    }
}


