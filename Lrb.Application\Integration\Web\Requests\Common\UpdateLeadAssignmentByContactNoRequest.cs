﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Requests;
using Lrb.Domain.Entities.ErrorModule;
using Newtonsoft.Json;
using System.Reflection;

namespace Lrb.Application.Integration.Web.Requests
{
    public class UpdateLeadAssignmentByContactNoRequest : IRequest<Response<bool>>
    {
        public string? Mobile { get; set; }
        public string? CountryCode { get; set; }
        public Guid? UserId { get; set; }
        public string? ApiKey { get; set; }
    }

    public class UpdateLeadAssignmentByContactNoRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateLeadAssignmentByContactNoRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        public UpdateLeadAssignmentByContactNoRequestHandler(IServiceProvider serviceProvider, IRepositoryWithEvents<IntegrationAccountInfo> integrationAccRepo, IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo, IRepositoryWithEvents<Domain.Entities.Lead> leadRepo) : base(serviceProvider, typeof(UpdateLeadAssignmentByContactNoRequest).Name, "Handle")
        {
            _integrationAccRepo = integrationAccRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _leadRepo = leadRepo;
        }
        public async Task<Response<bool>> Handle(UpdateLeadAssignmentByContactNoRequest request, CancellationToken cancellationToken)
        {
            try
            {
                if (request.UserId == null || request.UserId == Guid.Empty)
                {
                    _logger.Information($"UpdateLeadAssignmentByContactNoRequestHandler -> Handle -> UserId cannot be null or empty");
                    return new(false, "UserId cannot be null or empty");
                }
                if (string.IsNullOrWhiteSpace(request.Mobile))
                {
                    _logger.Information($"UpdateLeadAssignmentByContactNoRequestHandler -> Handle -> Lead Mobile number cannot be null or empty");
                    return new(false, "Lead Mobile number cannot be null or empty");
                }
                var integrationAccountId = AccountIdHelper.GetAccountId(request.ApiKey ?? string.Empty);
                var integrationAccountInfo = await _integrationAccRepo.FirstOrDefaultAsync(new GetIntegrationAccInfoWithAgencySpec(integrationAccountId), cancellationToken);
                if (integrationAccountInfo == null)
                {
                    return new()
                    {
                        Message = "Api Key Not Valid",
                        Succeeded = false
                    };
                }
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var contactNo = ListingSitesHelper.ConcatenatePhoneNumberV2(request.CountryCode, request.Mobile, globalSettings, integrationAccountInfo?.CountryCode);
                var lead = await _leadRepo.FirstOrDefaultAsync(new CheckLeadByPrimaryContactNoSpec(new List<string> { contactNo }), cancellationToken);
                if (lead != null)
                {
                    var previousAssignedFromUser = ((request?.UserId == null || request?.UserId == Guid.Empty) && lead.AssignTo != Guid.Empty) ? lead.AssignTo : lead.AssignedFrom;
                    if (request?.UserId != null && request?.UserId != Guid.Empty && (request?.UserId != lead.AssignTo))
                    {
                        AssignLeadsBasedOnScenariosRequest assignmentRequest = new()
                        {
                            LeadIds = new() { lead.Id },
                            UserIds = new() { request?.UserId ?? Guid.Empty },
                            AssignmentType = LeadAssignmentType.WithHistory,
                            LeadSource = lead.Enquiries.FirstOrDefault(i => i.IsPrimary)?.LeadSource ?? default,
                        };
                        var assignmentResponse = await _mediator.Send(assignmentRequest);
                    }
                    else
                    {
                        //lead.AssignedFrom = lead.AssignTo;
                        lead.AssignedFrom = (lead.AssignTo == request?.UserId) ? lead.AssignedFrom : (request?.UserId == Guid.Empty) ? lead.AssignTo : (request?.UserId == null) ? lead.AssignTo : lead.AssignedFrom;
                        lead.AssignTo = request?.UserId ?? Guid.Empty;
                        if (lead.AssignTo != lead.AssignedFrom)
                        {
                            lead.PickedDate = null;
                            lead.IsPicked = false;
                            lead.ShouldUpdatePickedDate = false;
                        }
                        await _leadRepo.UpdateAsync(lead);
                        await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithHistory);
                    }

                    // Set OriginalOwner to the assigned user when first assigned
                    if ((lead.OriginalOwner == null || lead.OriginalOwner == Guid.Empty) && lead.AssignTo != Guid.Empty)
                    {
                        lead.OriginalOwner = lead.AssignTo;
                    }

                    var fullLead = (await _leadRepo.ListAsync(new LeadByIdSpec(lead.Id), cancellationToken))?.FirstOrDefault();
                    var leadDto = fullLead?.Adapt<ViewLeadDto>();
                    await leadDto?.SetUsersInViewLeadDtoAsync(_userService, cancellationToken);
                    await UpdateLeadHistoryAsync(lead, leadDto, cancellationToken: cancellationToken, previousAssignedFrom: previousAssignedFromUser);
                    try
                    {
                        await lead.SendLeadStatusChangeNotificationsAsync(leadDto, _notificationSenderService, globalSettings, _currentUser.GetUserId());
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "AssigLeadRequestHandler -> Handle() ->SendLeadStatusChangeNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    return new(true);
                }
                else
                {
                    return new()
                    {
                        Message = "Lead Not Found",
                        Succeeded = false
                    };
                }
            }
            catch(Exception ex)
            {
                return new()
                {
                    Message = ex.Message,
                    Succeeded = false
                };
            }
        }
    } 
}
