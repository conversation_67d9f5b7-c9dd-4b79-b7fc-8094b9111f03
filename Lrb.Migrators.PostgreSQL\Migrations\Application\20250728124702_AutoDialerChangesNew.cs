﻿using System;
using System.Collections.Generic;
using Lrb.Domain.Enums;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AutoDialerChangesNew : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "IsAutoDialerEnabled",
                schema: "LeadratBlack",
                table: "UserDetails",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsAvilableForCall",
                schema: "LeadratBlack",
                table: "UserDetails",
                type: "boolean",
                nullable: true);

            migrationBuilder.AddColumn<Dictionary<IVRCallStatus, string>>(
                name: "CallStatusMappings",
                schema: "LeadratBlack",
                table: "IVRPayloadMappings",
                type: "jsonb",
                nullable: true);

            migrationBuilder.AddColumn<Dictionary<IVRResponseType, string>>(
                name: "ResponseDataMapping",
                schema: "LeadratBlack",
                table: "IVROutboundConfigurations",
                type: "jsonb",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AutoDialerAudits",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    LeadId = table.Column<Guid>(type: "uuid", nullable: true),
                    CallTo = table.Column<string>(type: "text", nullable: true),
                    CallFrom = table.Column<string>(type: "text", nullable: true),
                    AssignTo = table.Column<Guid>(type: "uuid", nullable: true),
                    CallStatus = table.Column<int>(type: "integer", nullable: true),
                    CallLogId = table.Column<Guid>(type: "uuid", nullable: true),
                    OrderRank = table.Column<int>(type: "integer", nullable: true),
                    Priority = table.Column<int>(type: "integer", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastModifiedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AutoDialerAudits", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AutoDialerAudits_IVRCommonCallLogs_CallLogId",
                        column: x => x.CallLogId,
                        principalSchema: "LeadratBlack",
                        principalTable: "IVRCommonCallLogs",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AutoDialerAudits_Leads_LeadId",
                        column: x => x.LeadId,
                        principalSchema: "LeadratBlack",
                        principalTable: "Leads",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AutoDialerConfigurations",
                schema: "LeadratBlack",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    IVROutboundConfigurationId = table.Column<Guid>(type: "uuid", nullable: true),
                    MaxCallInterval = table.Column<int>(type: "integer", nullable: true),
                    PrioritizeIntegrationLead = table.Column<bool>(type: "boolean", nullable: true),
                    TenantId = table.Column<string>(type: "character varying(64)", maxLength: 64, nullable: false),
                    IsDeleted = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    LastModifiedBy = table.Column<Guid>(type: "uuid", nullable: false),
                    LastModifiedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedOn = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    DeletedBy = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AutoDialerConfigurations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AutoDialerConfigurations_IVROutboundConfigurations_IVROutbo~",
                        column: x => x.IVROutboundConfigurationId,
                        principalSchema: "LeadratBlack",
                        principalTable: "IVROutboundConfigurations",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AutoDialerAudits_CallLogId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits",
                column: "CallLogId");

            migrationBuilder.CreateIndex(
                name: "IX_AutoDialerAudits_LeadId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits",
                column: "LeadId");

            migrationBuilder.CreateIndex(
                name: "IX_AutoDialerConfigurations_IVROutboundConfigurationId",
                schema: "LeadratBlack",
                table: "AutoDialerConfigurations",
                column: "IVROutboundConfigurationId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AutoDialerAudits",
                schema: "LeadratBlack");

            migrationBuilder.DropTable(
                name: "AutoDialerConfigurations",
                schema: "LeadratBlack");

            migrationBuilder.DropColumn(
                name: "IsAutoDialerEnabled",
                schema: "LeadratBlack",
                table: "UserDetails");

            migrationBuilder.DropColumn(
                name: "IsAvilableForCall",
                schema: "LeadratBlack",
                table: "UserDetails");

            migrationBuilder.DropColumn(
                name: "CallStatusMappings",
                schema: "LeadratBlack",
                table: "IVRPayloadMappings");

            migrationBuilder.DropColumn(
                name: "ResponseDataMapping",
                schema: "LeadratBlack",
                table: "IVROutboundConfigurations");
        }
    }
}
