﻿using Lrb.Application.AutoDialer.Web.Dtos;
using Lrb.Application.AutoDialer.Web.Requests;
namespace Lrb.Application.AutoDialer.Web.Specs
{
    public class GetAutoDialerAuditSpec : EntitiesByPaginationFilterSpec<AutoDialerAudit>
    {
        public GetAutoDialerAuditSpec(GetAllBucketLeadsRequest filter, bool? isadmin,List<Guid>? userIds) : base(filter)
        {

            Query.Include(i => i.Lead)
                .ThenInclude(j => j.Enquiries)
                .Include(i => i.Lead.CustomLeadStatus)
                .OrderBy(i => i.Priority)
                .ThenByDescending(i => i.CallStatus)
                .ThenBy(i => i.Priority)
                .ThenByDescending(i => i.CreatedOn);
            if (isadmin != true)
            {
                Query.Where(i => (i.AssignTo.HasValue && userIds.Contains(i.AssignTo.Value)) || (i.AssignTo == filter.CurrentUserId));
            }
            if (!string.IsNullOrEmpty(filter.SearchByNameAndNumber))
            {
                Query.Where(i => i.Lead != null && ((i.Lead.Name.Contains(filter.SearchByNameAndNumber)) || (i.Lead.ContactNo.Contains(filter.SearchByNameAndNumber))));
            }
            if (filter.CallStatus != null && filter.CallStatus != IVRCallStatus.None)
            {
                Query.Where(i => i.Lead != null && i.CallStatus == filter.CallStatus);
            }
            if(filter.FilterStatusId != null)
            {
                Query.Where(i => i.Lead != null && (i.Lead.CustomLeadStatus.BaseId == filter.FilterStatusId) || (i.Lead.CustomLeadStatus.Id == filter.FilterStatusId));
            }
            if(filter.UserIds != null && filter.UserIds.Any())
            {
                Query.Where(i => filter.UserIds.Contains(i.Lead.AssignTo));
            }
        }
    }

    public class GetAutoDialerAuditByStatusSpec : Specification<AutoDialerAudit>
    {
        public GetAutoDialerAuditByStatusSpec(Guid userId, IVRCallStatus callStatus)
        {
            Query.Where(i => i.AssignTo == userId && i.CallStatus == callStatus).OrderBy(i => i.Priority).ThenBy(j => j.Priority).ThenByDescending(k => k.CreatedOn);
        }
    }

    public class GetAutoDialerAuditByOrderRankSpec : Specification<AutoDialerAudit>
    {
        public GetAutoDialerAuditByOrderRankSpec(Guid? userId)
        {
            Query.Where(i => i.UserId == userId && i.Priority == 1).OrderBy(i => i.OrderRank);
        }
    }

    public class GetAutoDialerAuditByUserWithLeadSpec : Specification<AutoDialerAudit>
    {
        public GetAutoDialerAuditByUserWithLeadSpec(Guid? userId)
        {
            Query.Include(i => i.Lead)
                .Where(i => i.UserId == userId && i.Priority == 1).OrderBy(i => i.OrderRank);
        }
    }

    public class GetStatusDisplayCountsSpec : Specification<AutoDialerAudit, StatusDisplayCountDto>
    {
        public GetStatusDisplayCountsSpec(Guid userId)
        {
            Query
                .Where(a => a.UserId == userId && a.Lead != null && a.Lead.CustomLeadStatus != null)
                .Include(a => a.Lead)
                    .ThenInclude(l => l.CustomLeadStatus)
                .AsNoTracking();

            Query.Select(a => new StatusDisplayCountDto
            {
                DisplayName = a.Lead!.CustomLeadStatus!.DisplayName,
                Count = 0
            });
        }
    }

    public class GetInvalidAutoDialerAuditSpec : Specification<AutoDialerAudit>
    {
        public GetInvalidAutoDialerAuditSpec(List<Guid?> leadIds)
        {
            if (leadIds != null)
            {
                Query.Where(i => leadIds.Contains(i.LeadId));
            }
        }
    }

    public class GetAutoDialerAuditsByLeadIdsSpec : Specification<AutoDialerAudit>
    {
        public GetAutoDialerAuditsByLeadIdsSpec(List<Guid?>? leadIds)
        {
            if (leadIds != null)
            {
                Query.Where(i => !i.IsDeleted && i.LeadId != null && leadIds.Contains(i.LeadId));
            }
        }
    }

    public class GetAuditByCallLogIdSpec : Specification<AutoDialerAudit>
    {
        public GetAuditByCallLogIdSpec(Guid? callLogId)
        {
            { 
                Query.Where(i => !i.IsDeleted && i.IVRCommonCallLogId == callLogId);
            }
        }
    }
}