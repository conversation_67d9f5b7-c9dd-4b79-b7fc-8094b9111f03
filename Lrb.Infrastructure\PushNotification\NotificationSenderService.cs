using Amazon.Pinpoint.Model;
using Finbuckle.MultiTenant;
using Hangfire.States;
using Lrb.Application.Common.Email;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.WhatsApp;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Notifications.Dtos;
using Lrb.Application.Notifications.Specs;
using Lrb.Application.PushNotification.Web.Dtos;
using Lrb.Application.PushNotification.Web.Specs;
using Lrb.Application.PushNotification.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Application.WhatsAppCloudApi.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Mapster;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Graph;
using Microsoft.Graph.CallRecords;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Reflection;
using System.Threading;

namespace Lrb.Infrastructure.PushNotification
{
    public class NotificationSenderService : INotificationSenderService
    {
        private readonly IJobService _hangfireService;
        private readonly Application.Common.PushNotification.INotificationService _notificationService;
        private readonly INotificationMessageBuilder _notificationMessageBuilder;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        private readonly ITenantInfo _tenantInfo;
        private readonly IRepositoryWithEvents<NotificationTracker> _notificationTrackerRepo;
        private readonly IRepositoryWithEvents<Notification> _notificationRepo;
        private readonly IRepositoryWithEvents<NotificationServiceTracker> _notificationServiceTrackerRepo;
        private readonly Serilog.ILogger _logger;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IRepositoryWithEvents<MasterEmailServiceProvider> _masterEmailServiceProviderRepo;
        private readonly IRepositoryWithEvents<NotificationContent> _notificationContentRepo;
        private readonly IRepositoryWithEvents<GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<WhatsAppTemplateInfo> _whatsAppTemplateInfoRepo;
        private readonly IWhatsAppSenderService _whatsAppSenderService;
        private readonly IReadRepository<Lead> _leadRepo;
        private readonly ITemplateNotificationService _templateNotificationService;
        private readonly IRepositoryWithEvents<NotificationInfo> _notificationConfig;
        private readonly IDapperRepository _dapperRepositoryRepo;


        private readonly IRepositoryWithEvents<Domain.Entities.Device> _deviceRepo;
        public NotificationSenderService(IJobService hangfireService,
                                        Application.Common.PushNotification.INotificationService notificationService,
                                        INotificationMessageBuilder notificationMessageBuilder,
                                        ICurrentUser currentUser,
                                        IUserService userService,
                                        ITenantInfo tenantInfo,
                                        IRepositoryWithEvents<NotificationTracker> notificationTrackerRepo,
                                        IRepositoryWithEvents<Notification> notificationRepo,
                                        IRepositoryWithEvents<NotificationServiceTracker> notificationServiceTrackerRepo,
                                        Serilog.ILogger logger,
                                        ILeadRepositoryAsync leadRepositoryAsync,
                                        IGraphEmailService graphEmailService,
                                        IRepositoryWithEvents<MasterEmailServiceProvider> masterEmailServiceProviderRepo,
                                        IRepositoryWithEvents<NotificationContent> notificationContentRepo,
                                        IRepositoryWithEvents<GlobalSettings> globalSettingsRepo,
                                        IRepositoryWithEvents<WhatsAppTemplateInfo> whatsAppTemplateInfoRepo,
                                        IWhatsAppSenderService whatsAppSenderService,
                                        IReadRepository<Lead> leadRepo,
                                        ITemplateNotificationService templateNotificationService,
                                        IRepositoryWithEvents<NotificationInfo> notificationConfig,
                                        IDapperRepository dapperRepositoryRepo,
                                        IRepositoryWithEvents<Domain.Entities.Device> deviceRepo)
        {
            _hangfireService = hangfireService;
            _notificationService = notificationService;
            _notificationMessageBuilder = notificationMessageBuilder;
            _currentUser = currentUser;
            _userService = userService;
            _tenantInfo = tenantInfo;
            _notificationTrackerRepo = notificationTrackerRepo;
            _notificationRepo = notificationRepo;
            _notificationServiceTrackerRepo = notificationServiceTrackerRepo;
            _logger = logger;
            _leadRepositoryAsync = leadRepositoryAsync;
            _graphEmailService = graphEmailService;
            _masterEmailServiceProviderRepo = masterEmailServiceProviderRepo;
            _notificationContentRepo = notificationContentRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _whatsAppTemplateInfoRepo = whatsAppTemplateInfoRepo;
            _whatsAppSenderService = whatsAppSenderService;
            _leadRepo = leadRepo;
            _templateNotificationService = templateNotificationService;
            _notificationConfig = notificationConfig;
            _dapperRepositoryRepo = dapperRepositoryRepo;
            _deviceRepo = deviceRepo;
        }

        public async Task<List<string>> ScheduleNotificationsAsync<T>(Domain.Enums.Event @event, T entity, Guid? userId = default, string? userName = default, List<string>? topics = null, int? noOfEntities = null, Guid? currentUserIdPm = null, List<Guid>? userIds = null, LeadSource? leadSourceParam = null, Guid? currentUserId = null, int? rotationTime = null,Domain.Entities.GlobalSettings? globalSettings = null,List<UserDetailsDto>? allUserDetails = null, List<NotificationContent>? contents = null, string? status= null,double? bufferTime = null)
        {
            Console.WriteLine($"ScheduleNotificationsAsync Called");
            List<Notification> notifications = new();
            currentUserId = currentUserId ?? _currentUser.GetUserId();
            globalSettings ??= await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), CancellationToken.None);
            //var currentUserDetails = await _userService.GetAsync(currentUserId?.ToString() ?? string.Empty, CancellationToken.None);
            var currentUserDetails = allUserDetails?.FirstOrDefault(i => (currentUserId != null) && i.Id == currentUserId);
            currentUserDetails ??= (await _userService.GetListOfUsersByIdsAsync(new List<string>() { currentUserId?.ToString() ?? string.Empty }, CancellationToken.None)).FirstOrDefault();
            //_logger.Information($"NotificationSenderService -> ScheduledNotificationsAsync() -> CurrentUserDetailsByUserService: {JsonConvert.SerializeObject(currentUserDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
            if (currentUserDetails == null && currentUserIdPm != null)
            {
                currentUserDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { currentUserIdPm?.ToString() ?? string.Empty }, CancellationToken.None)).FirstOrDefault();
            }

            //_logger.Information($"NotificationSenderService -> ScheduledNotificationsAsync() -> currentUserIdPm: {currentUserIdPm}");
            //_logger.Information($"NotificationSenderService -> ScheduledNotificationsAsync() -> CurrentUserDetails after checking currentUserIdPm : {JsonConvert.SerializeObject(currentUserDetails, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
            if (userId != Guid.Empty && userId != null)
            {
                try
                {
                    if (!string.IsNullOrWhiteSpace(userName))
                    {
                        notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, userName, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime,contents:contents, status:status,bufferTime:bufferTime);
                    }
                    else
                    {
                        UserDetailsDto? userDetails = allUserDetails?.FirstOrDefault(i => (userId != default) &&  i.Id == userId);
                        userDetails ??= (await _userService.GetListOfUsersByIdsAsync(new List<string>() { userId?.ToString() ?? string.Empty }, CancellationToken.None)).FirstOrDefault();
                        notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, userDetails?.FirstName + " " + userDetails?.LastName, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime, contents: contents, status: status);
                    }
                }
                catch (NotFoundException ex)
                {
                    if (currentUserDetails != null)
                    {
                        notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, currentUserDetails.FirstName + " " + currentUserDetails.LastName, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime, contents: contents, status: status);
                    }
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "NotificationSenderService -> ScheduleNotificationsAsync()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
            }
            else
            {
                if (currentUserDetails != null)
                {
                    notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, userName ?? currentUserDetails.FirstName + " " + currentUserDetails.LastName, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime, contents: contents, status: status);
                }
                else if (userIds?.Any() ?? false)
                {
                    notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, string.Empty, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime, contents: contents, status: status);
                }
            }
            List<string> schedulingResponse = new();

            PropertyInfo[] properties = typeof(T).GetProperties();
            string? entityId = properties?.FirstOrDefault(i => i?.Name == "Id")?.GetValue(entity).ToString() ?? string.Empty;
            DateTime scheduledDate = DateTime.UtcNow;
            string type = typeof(T).FullName;
            if (type != null)
            {
                if (type == typeof(Domain.Entities.Todo).FullName)
                {
                    scheduledDate = (DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDateTime")?.GetValue(entity) ?? DateTime.UtcNow);
                }
                else if (type == typeof(Domain.Entities.Lead).FullName)
                {
                    scheduledDate = (DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                }
            }
            try
            {
                List<string> existingNotificationTrackerInfo = (await _dapperRepositoryRepo.GetNotificationTrackersJobId(_tenantInfo.Id ?? _currentUser.GetTenant(), Guid.Parse(entityId), scheduledDate.ToUniversalTime())).ToList();
                if (existingNotificationTrackerInfo != null && existingNotificationTrackerInfo.Count > 0)
                {
                    existingNotificationTrackerInfo.ForEach(i => _hangfireService.Delete(i ?? string.Empty));
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationSenderService -> ScheduleNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            globalSettings ??= (await _globalSettingsRepo.ListAsync()).FirstOrDefault();
            NotificationSettings? notificationSettings = globalSettings != null ? GetNotificationSettings(globalSettings) : null;
            List<NotificationTracker> newNotificationTrackerInfo = new();
            foreach (Notification notification in notifications)
            {
                string jobId = string.Empty;
                try
                {
                    var notificationDto = notification.Adapt<NotificationDTO>();

                    notificationDto.CurrentUserId = currentUserId ?? _currentUser.GetUserId();
                    if (notificationDto.CurrentUserId == Guid.Empty && currentUserDetails != null)
                    {
                        notificationDto.CurrentUserId = currentUserDetails.Id;
                    }
                    if (userIds?.Any() ?? false)
                    {
                        notificationDto.UserIds = userIds;
                    }
                    else
                    {
                        var loneUserId = userId == null || userId == Guid.Empty ? currentUserId ?? default : userId.Value;
                        notificationDto.UserIds = new() { loneUserId };
                    }
                    notificationDto.TenantInfoDto = new()
                    {
                        Id = _tenantInfo.Id,
                        ConnectionString = _tenantInfo.ConnectionString,
                        Identifier = _tenantInfo.Identifier,
                        Name = _tenantInfo.Name
                    };
                    List<string> userEmails = new();
                    MasterEmailServiceProvider? masterEmailServiceProvider = (await _masterEmailServiceProviderRepo.FirstOrDefaultAsync(new GetLREmailServiceProviderSpec(), CancellationToken.None));
                    NotificationContent? leadInfoForEmail = null;

                    EmailSenderDto? emailSenderDto = null;
                    if (notificationDto.IsScheduled == true)
                    {
                        notificationDto.IsScheduled = false;

                        jobId = _hangfireService.Schedule(() => _notificationService.SendNotificationAsync(notificationDto), notificationDto.ScheduledDate);
                        emailSenderDto = await _notificationMessageBuilder.BuildEmailNotification(entity, notificationSettings, notificationDto, currentUserDetails ?? new(), noOfEntities, globalSettings, userName, rotationTime: rotationTime);
                        if (emailSenderDto != null && (@event == Domain.Enums.Event.LeadFromIntegration || @event == Domain.Enums.Event.DuplicateLeadEnquiryAlert))
                        {
                            var jobIdForEmail = _hangfireService.Schedule(() => _notificationService.SendEmailNotification(emailSenderDto), notificationDto.ScheduledDate);
                        }

                        #region Web Notification
                        try
                        {
                            if (userId == Guid.Empty || userId == default)
                            {

                                var deviceInfos = await _dapperRepositoryRepo.GetDeviceInfo(_tenantInfo.Id ?? _currentUser?.GetTenant(), userIds ?? new());

                                foreach (var deviceInfo in deviceInfos)
                                {
                                    if (!string.IsNullOrEmpty(deviceInfo.NewNotificationToken))
                                    {
                                        var webNotificationJobId = _hangfireService.Schedule(() => _notificationService.SendWebNotificationAsync(notificationDto, deviceInfo.NewNotificationToken ?? string.Empty), notificationDto.ScheduledDate);
                                    }
                                }
                            }
                            else if (userId != Guid.Empty)
                            {
                                var deviceInfos = await _dapperRepositoryRepo.GetDeviceInfo(_tenantInfo.Id ?? _currentUser?.GetTenant(), userIds ?? new());
                                foreach (var deviceInfo in deviceInfos)
                                {
                                    if (!string.IsNullOrEmpty(deviceInfo.NewNotificationToken))
                                    {
                                        var webNotificationJobId = _hangfireService.Schedule(() => _notificationService.SendWebNotificationAsync(notificationDto, deviceInfo.NewNotificationToken ?? string.Empty), notificationDto.ScheduledDate);
                                    }
                                }
                                if (((notificationSettings?.IsAdminEnabled ?? false) || (notificationSettings?.IsManagerEnabled ?? false)))
                                {
                                    var adminDevices = await _dapperRepositoryRepo.GetDeviceInfo(_tenantInfo.Id ?? _currentUser.GetTenant(), userIds ?? new());
                                    foreach (var deviceInfo in adminDevices)
                                    {
                                        if (!string.IsNullOrEmpty(deviceInfo.NewNotificationToken))
                                        {
                                            var webNotificationJobId = _hangfireService.Schedule(() => _notificationService.SendWebNotificationAsync(notificationDto, deviceInfo.NewNotificationToken ?? string.Empty), notificationDto.ScheduledDate);
                                        }
                                    }
                                }
                            }

                        }
                        catch (Exception ex)
                        {
                            _logger.Information(ex, "Error occurred while processing web notifications.");
                        }
                        #endregion

                    }
                    else
                    {
                        jobId = _hangfireService.Enqueue(() => _notificationService.SendNotificationAsync(notificationDto));
                        emailSenderDto = await _notificationMessageBuilder.BuildEmailNotification(entity, notificationSettings, notificationDto, currentUserDetails ?? new(), noOfEntities, globalSettings, userName, rotationTime: rotationTime);
                        if (emailSenderDto != null && (@event == Domain.Enums.Event.LeadFromIntegration || @event == Domain.Enums.Event.DuplicateLeadEnquiryAlert))
                        {
                            var jobIdForEmail = _hangfireService.Enqueue(() => _notificationService.SendEmailNotification(emailSenderDto));
                        }
                        else if (emailSenderDto != null && userId != null && userId != currentUserId)
                        {
                            var jobIdForEmail = _hangfireService.Enqueue(() => _notificationService.SendEmailNotification(emailSenderDto));
                        }
                        else if ((emailSenderDto != null) && (userIds?.Any() ?? false))
                        {
                            var jobIdForEmail = _hangfireService.Enqueue(() => _notificationService.SendEmailNotification(emailSenderDto));
                        }

                        #region Web notification
                        try
                        {
                            if ((userId == Guid.Empty || userId == default)  && (userIds?.Any() ?? false))
                            {
                                var deviceInfos = await _dapperRepositoryRepo.GetDeviceInfo(_tenantInfo.Id ?? _currentUser.GetTenant(), userIds ?? new());
                                foreach (var deviceInfo in deviceInfos)
                                {
                                    if (!string.IsNullOrEmpty(deviceInfo.NewNotificationToken))
                                    {
                                        var webNotificationJobId = _hangfireService.Enqueue(() => _notificationService.SendWebNotificationAsync(notificationDto, deviceInfo.NewNotificationToken ?? string.Empty));
                                    }
                                }
                            }
                            else if (userId != Guid.Empty)
                            {
                                var deviceInfos = await _dapperRepositoryRepo.GetDeviceInfo(_tenantInfo.Id ?? _currentUser.GetTenant(), new List<Guid>() { userId ?? Guid.Empty});
                                foreach (var device in deviceInfos)
                                {
                                    if (!string.IsNullOrEmpty(device.NewNotificationToken))
                                    {
                                        var webNotificationJobId = _hangfireService.Enqueue(() => _notificationService.SendWebNotificationAsync(notificationDto, device.NewNotificationToken ?? string.Empty));
                                    }
                                }

                                if ((notificationSettings?.IsAdminEnabled ?? false) || (notificationSettings?.IsManagerEnabled ?? false) && (userIds?.Any() ?? false))
                                {
                                    var adminDevices = await _dapperRepositoryRepo.GetDeviceInfo(_tenantInfo.Id ?? _currentUser.GetTenant(), userIds ?? new());
                                    foreach (var deviceInfo in adminDevices)
                                    {
                                        if (!string.IsNullOrEmpty(deviceInfo.NewNotificationToken))
                                        {
                                            var webNotificationJobId = _hangfireService.Enqueue(() => _notificationService.SendWebNotificationAsync(notificationDto, deviceInfo.NewNotificationToken ?? string.Empty));
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.Information(ex, "Error occurred while processing web notifications.");
                        }
                        #endregion

                    }
                    schedulingResponse.Add(jobId);
                    if (!string.IsNullOrWhiteSpace(entityId))
                    {
                        NotificationTracker notificationTracker = new()
                        {
                            NotificationId = notificationDto.Id,
                            EntityId = Guid.Parse(entityId),
                            Event = @event,
                            ScheduleTime = scheduledDate,
                            JobId = jobId
                        };
                        newNotificationTrackerInfo.Add(notificationTracker);
                    }
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "NotificationSenderService -> ScheduleNotificationsAsync()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                    _logger.Information($"NotificationSenderService -> Exception -> {JsonConvert.SerializeObject(ex, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");
                }
            }
            try
            {
                foreach (var notificationTracker in newNotificationTrackerInfo)
                {
                    await _notificationTrackerRepo.AddAsync(notificationTracker);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationSenderService -> ScheduleNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            //Scheduling WhatsApp Notifications Reminder For Lead
            try
            {
                if (entity is Lead lead)
                {
                    await SendWhatsAppNotificationAsync(entity, @event, notificationSettings ?? new(), globalSettings ?? new(), lead: lead, userName: userName);
                }
                else
                {
                    await SendWhatsAppNotificationAsync(entity, @event, notificationSettings ?? new(), globalSettings ?? new());
                }

            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationSenderService -> ScheduleNotificationsAsync() -> Scheduling WhatsApp Notification Reminders"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }

            try
            {
                if (entity is Lead lead)
                {
                    await SendTemplateNotificationAsync(entity, null,@event, noOfEntities : noOfEntities);
                }
                if (globalSettings?.IsEngageToEnabled ?? false)
                {
                    await _notificationService.SendLeadUpdateToEngageto(entity, CancellationToken.None);
                }

            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationSenderService -> ScheduleNotificationsAsync() -> Scheduling WhatsApp Notification Reminders"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

            return schedulingResponse;
        }

        public async Task SendWhatsAppNotificationAsync<T>(T entity, Domain.Enums.Event @event, NotificationSettings? notificationSettings, GlobalSettings globalSettings, bool? IsFeedbackNotification = null, Lead? lead = null, string? userName = null)
        {

            //re-assinment
            if (@event == Domain.Enums.Event.LeadAssignment && entity is Lead l)
            {
                var assignments = l.Assignments;
                if (assignments?.Count > 1)
                {
                    @event = Domain.Enums.Event.LeadReAssignment;
                }
            }

            notificationSettings ??= GetNotificationSettings(globalSettings);
            var whatsAppTemplateInfos = await _whatsAppTemplateInfoRepo.ListAsync(new GetWhatsAppTemplateInfoByEventSpec(@event, IsLeadSpecific: true), CancellationToken.None);
            var whatsAppTemplateWithLeadInfos = await GetUpdatedWhatsAppTemplateDtoAsync(entity, notificationSettings ?? new(), globalSettings, @event, lead: lead, userName: userName);
            //Getting Assignment User Details
            PropertyInfo[] properties = typeof(T).GetProperties();
            var assignmentUser = (properties.FirstOrDefault(i => i?.Name == "AssignTo")?.GetValue(entity) as Guid?) ?? Guid.Empty;
            UserDetailsDto? userDetailsDto = null;
            List<BaseWhatsAppTemplateWithLeadIdDto> baseWhatsAppTemplateWithLeadIdDtos = new();
            if (assignmentUser != Guid.Empty)
            {
                var users = await _userService.GetListOfUsersByIdsAsync(new List<string> { assignmentUser.ToString() }, CancellationToken.None);
                userDetailsDto = users?.FirstOrDefault();
            }
            if ((whatsAppTemplateWithLeadInfos?.Any() ?? false) && (whatsAppTemplateInfos?.Any() ?? false))
            {
                if ((@event == Domain.Enums.Event.CallbackReminder
                    || @event == Domain.Enums.Event.ScheduleMeetingReminder
                    || @event == Domain.Enums.Event.ScheduleSiteVisitReminder
                    || @event == Domain.Enums.Event.ScheduledTaskReminder)
                    || (@event == Domain.Enums.Event.LeadSiteVisitDone || @event == Domain.Enums.Event.LeadMeetingDone))
                {
                    foreach (var tempWithLead in whatsAppTemplateWithLeadInfos)
                    {
                        var defaultVariables = whatsAppTemplateInfos.FirstOrDefault(x => x.CodeName == tempWithLead.Template?.Name)?.DefaultBodyValues?.Where(x => x.StartsWith("#") && x.EndsWith("#")).ToList() ?? new();
                        tempWithLead.Template.BodyValues = defaultVariables.Any() ? MappingVariables(defaultVariables, entity as Lead, userDetailsDto, null) : tempWithLead.Template.BodyValues;

                        tempWithLead.IsScheduled = true;
                        if (tempWithLead.ScheduleBeforeMinutes?.Any() ?? false)
                        {
                            foreach (var min in tempWithLead.ScheduleBeforeMinutes)
                            {
                                if (tempWithLead.ScheduledDateTime != null && tempWithLead.ScheduledDateTime.Value.AddMinutes(-min) > DateTime.UtcNow)
                                {
                                    var newDto = tempWithLead.Adapt<BaseWhatsAppTemplateWithLeadIdDto>();
                                    newDto.ScheduledDateTime = tempWithLead.ScheduledDateTime.Value.AddMinutes(-min);
                                    newDto.WhatsAppHeaderTypes = tempWithLead.WhatsAppHeaderTypes;
                                    baseWhatsAppTemplateWithLeadIdDtos.Add(newDto);
                                }
                            }
                        }

                        if ((tempWithLead.ScheduleAfterMinutes?.Any() ?? false) && (notificationSettings?.WhatsAppNotificationSettings?.LeadAfterEvents?.Contains(@event) ?? false))
                        {
                            foreach (var min in tempWithLead.ScheduleAfterMinutes)
                            {
                                if ((IsFeedbackNotification ?? false) && whatsAppTemplateInfos.Any(i => i.IsAfterEventTemplate && (i.CodeName?.Equals(tempWithLead.Template?.Name) ?? false)))
                                {
                                    var newDto = tempWithLead.Adapt<BaseWhatsAppTemplateWithLeadIdDto>();
                                    newDto.ScheduledDateTime = DateTime.UtcNow.AddMinutes(min);
                                    newDto.WhatsAppHeaderTypes = tempWithLead.WhatsAppHeaderTypes;
                                    baseWhatsAppTemplateWithLeadIdDtos.Add(newDto);
                                }
                                //else if (tempWithLead.ScheduledDateTime != null && tempWithLead.ScheduledDateTime.Value.AddMinutes(min) > DateTime.UtcNow)
                                //{
                                //    var newDto = tempWithLead.Adapt<BaseWhatsAppTemplateWithLeadIdDto>();
                                //    newDto.ScheduledDateTime = tempWithLead.ScheduledDateTime.Value.AddMinutes(min);
                                //    newDto.WhatsAppHeaderTypes = tempWithLead.WhatsAppHeaderTypes;
                                //    baseWhatsAppTemplateWithLeadIdDtos.Add(newDto);
                                //}
                            }
                        }
                    }
                }
                else
                {
                    foreach (var tempWithLead in whatsAppTemplateWithLeadInfos)
                    {
                        var defaultVariables = whatsAppTemplateInfos.FirstOrDefault(x => x.CodeName == tempWithLead.Template?.Name)?.DefaultBodyValues?.Where(x => x.StartsWith("#") && x.EndsWith("#")).ToList() ?? new();
                        tempWithLead.Template.BodyValues = defaultVariables.Any() ? MappingVariables(defaultVariables, entity as Lead, userDetailsDto, null) : tempWithLead.Template.BodyValues;
                        tempWithLead.IsScheduled = false;
                        baseWhatsAppTemplateWithLeadIdDtos = new() { tempWithLead };
                    }

                }
                var response = await _whatsAppSenderService.SendCommonTemplateAsync(baseWhatsAppTemplateWithLeadIdDtos, default, false);
            }
        }
        public async Task SendWhatsAppNotificationAsync<T>(T entity, List<Guid> waIds, NotificationSettings? notificationSettings, GlobalSettings? globalSettings, bool? IsFeedbackNotification = null, object? viewLeadDto = null)
        {
            globalSettings ??= (await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(true))) ?? new();
            notificationSettings ??= GetNotificationSettings(globalSettings);
            var whatsAppTemplateInfos = await _whatsAppTemplateInfoRepo.ListAsync(new GetWhatsAppTemplateInfoByEventSpec(IsLeadSpecific: true, ids: waIds), CancellationToken.None);
            var whatsAppTemplateWithLeadInfos = await GetUpdatedWhatsAppTemplateDtoAsync(entity, notificationSettings ?? new(), globalSettings, whatsAppTemplateInfos, viewLeadDto);
            List<BaseWhatsAppTemplateWithLeadIdDto> baseWhatsAppTemplateWithLeadIdDtos = new();
            if (whatsAppTemplateWithLeadInfos?.Any() ?? false)
            {
                foreach (var tempWithLead in whatsAppTemplateWithLeadInfos)
                {
                    tempWithLead.IsScheduled = false;
                    baseWhatsAppTemplateWithLeadIdDtos = new() { tempWithLead };
                }
                var response = await _whatsAppSenderService.SendCommonTemplateAsync(baseWhatsAppTemplateWithLeadIdDtos, default, false);
            }
        }

        private async Task<List<BaseWhatsAppTemplateWithLeadIdDto>?> GetUpdatedWhatsAppTemplateDtoAsync<T>(T entity, NotificationSettings notificationSettings, GlobalSettings globalSettings, List<WhatsAppTemplateInfo> whatsAppTemplateInfos, object? viewLeadDto = null)
        {
            var dtoWithLeadInfo = await _notificationMessageBuilder.BuildWhatsAppTemplateDtoWithLeadInfoAsync(entity, globalSettings);
            List<CommonWhatsAppTemplateDto> templateDtos = new();
            PropertyInfo[] properties = typeof(T).GetProperties();
            UserDetailsDto? userDetailsDto = null;
            var assignmentUser = (properties.FirstOrDefault(i => i?.Name == "AssignTo")?.GetValue(entity) as Guid?) ?? Guid.Empty;

            if (assignmentUser != Guid.Empty)
            {
                var users = await _userService.GetListOfUsersByIdsAsync(new List<string> { assignmentUser.ToString() }, CancellationToken.None);
                userDetailsDto = users?.FirstOrDefault();
            }
            whatsAppTemplateInfos.ForEach(async i =>
            {
                var bodyVariables = i.DefaultBodyValues?.Where(x => x.StartsWith("#") && x.EndsWith("#")).ToList();
                var bodyValues = MappingVariables(bodyVariables, entity as Lead, userDetailsDto, viewLeadDto);
                templateDtos.Add(new()
                {
                    BodyValues = bodyValues.Count() == 0 ? new() { dtoWithLeadInfo.LeadName ?? string.Empty } : bodyValues,
                    Name = i?.CodeName ?? string.Empty,
                });
            });
            List<BaseWhatsAppTemplateWithLeadIdDto> baseWhatsAppTemplateWithLeadIdDtos = new();
            templateDtos.ForEach(i =>
            {
                var dto = dtoWithLeadInfo.Adapt<BaseWhatsAppTemplateWithLeadIdDto>();
                dto.Template = i;
                dto.ScheduleBeforeMinutes = dtoWithLeadInfo.ScheduleBeforeMinutes;
                dto.ScheduleAfterMinutes = dtoWithLeadInfo.ScheduleAfterMinutes;
                baseWhatsAppTemplateWithLeadIdDtos.Add(dto);
            });
            return baseWhatsAppTemplateWithLeadIdDtos;
        }
        private List<string> MappingVariables(List<string>? variables, Lead lead, UserDetailsDto? userDetailsDto, object? leadDto)
        {
            List<string> bodyValues = new();

            var leadDict = leadDto == null ? GetKeyValuesFromObject(lead) : GetKeyValuesFromObject(leadDto);

            Dictionary<string, string> userDetailsDict = userDetailsDto != null ? GetKeyValuesFromObject(userDetailsDto) : new();
            TimeZoneInfo istZone = TimeZoneInfo.FindSystemTimeZoneById("India Standard Time");
            foreach (var variable in variables ?? new())
            {
                var trimVariables = variable.Trim('#').Split(",");
                string value = string.Empty;

                foreach (var trimVariable in trimVariables)
                {
                    string tempValue = string.Empty;

                    if (trimVariable == "LScheduledDate")
                    {
                        var scheduleDate = TimeZoneInfo.ConvertTimeFromUtc(lead.ScheduledDate ?? DateTime.UtcNow, istZone);
                        tempValue = scheduleDate.ToString("dd-MMM-yyyy") ?? string.Empty;
                    }

                    if (trimVariable == "LScheduledTime")
                    {
                        var scheduleDate = TimeZoneInfo.ConvertTimeFromUtc(lead.ScheduledDate ?? DateTime.UtcNow, istZone);
                        tempValue = scheduleDate.ToString("HH:mm tt") ?? string.Empty;
                    }
                    if (trimVariable.Contains("LeadSource"))
                    {
                        tempValue = lead.Enquiries?.FirstOrDefault()?.LeadSource.ToString() ?? string.Empty;
                    }
                    if (trimVariable == "OldAssignmentUser")
                    {
                        var oldUserId = lead.Assignments?.OrderByDescending(x => x.CreatedOn)?.Skip(1).FirstOrDefault()?.AssignTo;
                        if (oldUserId != null && oldUserId != Guid.Empty)
                        {
                            var user = _userService.GetListOfUsersByIdsAsync(new List<string> { oldUserId.ToString() }, CancellationToken.None).Result;
                            tempValue = user.FirstOrDefault()?.FirstName ?? string.Empty;
                        }
                    }
                    if (string.IsNullOrEmpty(tempValue) && !leadDict.TryGetValue(trimVariable, out tempValue))
                    {
                        userDetailsDict.TryGetValue(trimVariable, out tempValue);
                    }

                    if (!string.IsNullOrEmpty(tempValue))
                        value = string.IsNullOrEmpty(value) ? tempValue : value + " " + tempValue;
                }
                bodyValues.Add(value ?? string.Empty);
            }
            return bodyValues;
        }

        private Dictionary<string, string> GetKeyValuesFromObject(object obj)
        {

            var settings = new JsonSerializerSettings
            {
                ReferenceLoopHandling = ReferenceLoopHandling.Ignore,
                PreserveReferencesHandling = PreserveReferencesHandling.Objects
            };
            string json = JsonConvert.SerializeObject(obj, settings);
            JObject jobject = JObject.Parse(json);
            return JTokenHelper.GetWithArrayKeysAndValues(jobject, "");
        }
        private async Task<List<BaseWhatsAppTemplateWithLeadIdDto>?> GetUpdatedWhatsAppTemplateDtoAsync<T>(T entity, NotificationSettings notificationSettings, GlobalSettings globalSettings, Domain.Enums.Event @event, Lead? lead = null, string? userName = null)
        {
            if (!globalSettings.IsWhatsAppEnabled || (!notificationSettings.ChannelSettings?.IsWhatsAppNotificationEnabled ?? true)
                || (!notificationSettings.WhatsAppNotificationSettings?.LeadNotificationEvents?.Contains(@event) ?? true))
            {
                return null;
            }
            var dtoWithLeadInfo = await _notificationMessageBuilder.BuildWhatsAppTemplateDtoWithLeadInfoAsync(entity, globalSettings);
            var whatsAppTemplateInfos = await _whatsAppTemplateInfoRepo.ListAsync(new GetWhatsAppTemplateInfoByEventSpec(@event, IsLeadSpecific: true), CancellationToken.None);
            List<CommonWhatsAppTemplateDto> templateDtos = new();
            whatsAppTemplateInfos.ForEach(i =>
            {
                templateDtos.Add(new()
                {
                    BodyValues = new()
                    {
                        dtoWithLeadInfo.LeadName ?? string.Empty,
                        lead?.Enquiries?.FirstOrDefault(i => i.IsPrimary)?.LeadSource.ToString() ?? string.Empty,
                        lead?.Enquiries.FirstOrDefault(i => i.IsPrimary)?.NoOfBHKs.ToString() ?? string.Empty,
                        lead?.Projects?.FirstOrDefault()?.Name ?? string.Empty,
                        lead?.Projects?.FirstOrDefault()?.BuilderDetail?.Name ?? string.Empty,
                        userName ?? string.Empty,
                    },
                    Name = i?.CodeName ?? string.Empty,
                });
            });
            List<BaseWhatsAppTemplateWithLeadIdDto> baseWhatsAppTemplateWithLeadIdDtos = new();
            templateDtos.ForEach(i =>
            {
                var dto = dtoWithLeadInfo.Adapt<BaseWhatsAppTemplateWithLeadIdDto>();
                dto.Template = i;
                dto.ScheduleBeforeMinutes = dtoWithLeadInfo.ScheduleBeforeMinutes;
                dto.ScheduleAfterMinutes = dtoWithLeadInfo.ScheduleAfterMinutes;
                dto.ShouldAddLeadNameInBody = false;
                baseWhatsAppTemplateWithLeadIdDtos.Add(dto);
            });
            return baseWhatsAppTemplateWithLeadIdDtos;
        }
        private NotificationSettings? GetNotificationSettings(GlobalSettings globalSettings)
        {
            var notificationSettingsString = globalSettings.NotificationSettings;
            NotificationSettings? notificationSettings = null;
            if (!string.IsNullOrWhiteSpace(notificationSettingsString))
            {
                notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(notificationSettingsString);
            }
            return notificationSettings;
        }
        public async Task<bool> DeleteScheduledNotificationsAsync<T>(T entity)
        {
            PropertyInfo[] properties = typeof(T).GetProperties();
            string? entityId = properties?.FirstOrDefault(i => i?.Name == "Id")?.GetValue(entity).ToString() ?? string.Empty;
            List<string> existingNotificationTrackerInfo = (await _dapperRepositoryRepo.GetNotificationTrackersJobId(_tenantInfo.Id ?? _currentUser.GetTenant(), Guid.Parse(entityId))).ToList();
            if (existingNotificationTrackerInfo?.Any() ?? false)
            {
                existingNotificationTrackerInfo.ForEach(i => _hangfireService.Delete(i ?? string.Empty));
            }
            return true;
        }
        public async Task<bool> DeleteScheduledNotificationsBulkAsync<T>(List<T> entities)
        {
            List<string> existingNotificationTrackers = new();
            foreach (var entity in entities)
            {
                PropertyInfo[] properties = typeof(T).GetProperties();
                string? entityId = properties?.FirstOrDefault(i => i?.Name == "Id")?.GetValue(entity).ToString() ?? string.Empty;
                List<string> existingNotificationTrackerInfo = (await _dapperRepositoryRepo.GetNotificationTrackersJobId(_tenantInfo.Id ?? _currentUser.GetTenant(), Guid.Parse(entityId))).ToList();
                existingNotificationTrackers.AddRange(existingNotificationTrackerInfo);
            }
            if (existingNotificationTrackers?.Any() ?? false)
            {
                existingNotificationTrackers.ForEach(i => _hangfireService.Delete(i ?? string.Empty));
            }
            return true;
        }
        public static DateTime ConvertUtcToLocalTime(DateTime utcDateTime, string timeZoneId)
        {
            TimeZoneInfo userTimeZone = TimeZoneInfo.FindSystemTimeZoneById(timeZoneId);
            DateTime localDateTime = TimeZoneInfo.ConvertTimeFromUtc(utcDateTime, userTimeZone);
            return localDateTime;
        }
        public async Task<List<string>> SchedulePushNotificationsAsync<T>(Domain.Enums.Event @event, T entity, Domain.Entities.GlobalSettings? globalSettings, List<Guid>? userIds = default, string? userName = default, List<string>? topics = null, Guid? currentUserId = null)
        {
            List<Notification> notifications = new();
            currentUserId = currentUserId ?? _currentUser.GetUserId();
            var currentUserDetails = await _userService.GetAsync(currentUserId.ToString() ?? string.Empty, CancellationToken.None);
            Dictionary<Guid, List<Notification>> notificationsByUserId = new();
            if (userIds != null && userIds.Any())
            {
                foreach (var userId in userIds)
                {
                    try
                    {
                        UserDetailsDto userDetails = await _userService.GetAsync(userId.ToString() ?? string.Empty, CancellationToken.None);
                        var userNotifications = await _notificationMessageBuilder.BuildNotificationsAsync(@event, entity, userDetails.FirstName + " " + userDetails.LastName, globalSettings);
                        notificationsByUserId[userId] = userNotifications;

                    }
                    catch (NotFoundException ex)
                    {
                        var userNotifications = await _notificationMessageBuilder.BuildNotificationsAsync(@event, entity, currentUserDetails.FirstName + " " + currentUserDetails.LastName, globalSettings);
                        notificationsByUserId[userId] = userNotifications;
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "NotificationSenderService -> SchedulePushNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                }
            }
            else
            {
                var userNotifications = await _notificationMessageBuilder.BuildNotificationsAsync(@event, entity, currentUserDetails.FirstName + " " + currentUserDetails.LastName, globalSettings);
                notificationsByUserId[currentUserId ?? Guid.Empty] = userNotifications;
            }
            List<string> schedulingResponse = new();

            PropertyInfo[] properties = typeof(T).GetProperties();
            string? entityId = properties?.FirstOrDefault(i => i?.Name == "Id")?.GetValue(entity).ToString() ?? string.Empty;
            DateTime scheduledDate = DateTime.UtcNow;
            string type = typeof(T).FullName;
            if (type != null)
            {
                if (type == typeof(Domain.Entities.Todo).FullName)
                {
                    scheduledDate = (DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDateTime")?.GetValue(entity) ?? DateTime.UtcNow);
                }
                else if (type == typeof(Domain.Entities.Lead).FullName)
                {
                    scheduledDate = (DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                }
            }
            List<NotificationServiceTracker> trackers = new();
            foreach (var notificationSet in notificationsByUserId)
            {
                foreach (Notification notification in notificationSet.Value)
                {
                    string jobId;
                    try
                    {
                        var notificationDto = notification.Adapt<NotificationDTO>();

                        //notificationDto.Topics = topics ?? new();
                        notificationDto.CurrentUserId = currentUserId ?? _currentUser.GetUserId();
                        notificationDto.UserId = (notificationSet.Key == null || notificationSet.Key == Guid.Empty) ? currentUserId ?? Guid.Empty : notificationSet.Key;
                        notificationDto.TenantInfoDto = new()
                        {
                            Id = _tenantInfo.Id,
                            ConnectionString = _tenantInfo.ConnectionString,
                            Identifier = _tenantInfo.Identifier,
                            Name = _tenantInfo.Name
                        };

                        if (notification.IsScheduled == true)
                        {
                            notification.IsScheduled = false;
                            jobId = _hangfireService.Schedule(() => _notificationService.SendNotificationAsync(notificationDto), notificationDto.ScheduledDate.ToLocalTime());
                        }
                        else
                        {
                            jobId = _hangfireService.Enqueue(() => _notificationService.SendNotificationAsync(notificationDto));
                        }
                        schedulingResponse.Add(jobId);
                        NotificationServiceTracker tracker = new();
                        tracker.JobId = jobId;
                        tracker.EntityId = entityId == null ? Guid.Empty : Guid.Parse(entityId);
                        tracker.Event = @event;
                        tracker.ScheduledDate = scheduledDate;
                        trackers.Add(tracker);
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex.Message,
                            ErrorSource = ex.Source,
                            StackTrace = ex.StackTrace,
                            ErrorModule = "NotificationSenderService -> SchedulePushNotificationsAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                        throw;
                    }
                }
            }
            try
            {
                foreach (var tracker in trackers)
                {
                    await _notificationServiceTrackerRepo.AddAsync(tracker, CancellationToken.None);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "NotificationSenderService -> SchedulePushNotificationsAsync()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                throw;
            }
            return schedulingResponse;
        }

        public async Task<string> ScheduleWANotificationsAsync<T>(T entity, Guid userId, string mediaType, string message)
        {
            Console.WriteLine($"ScheduleNotificationsAsync Called");
            Notification notification = new();
            var currentUserDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string> { userId.ToString() ?? string.Empty }, CancellationToken.None)).FirstOrDefault();
            PropertyInfo[] properties = typeof(T).GetProperties();
            var leadName = properties.FirstOrDefault(i => i.Name == "Name")?.GetValue(entity)?.ToString();
            var leadId = properties.FirstOrDefault(i => i.Name == "Id")?.GetValue(entity)?.ToString();
            switch (mediaType.ToLower().Trim())
            {
                case "image":
                    var imageContent = "New message received from #leadName#: 📷 Image";
                    imageContent = imageContent.Replace("#leadName#", leadName);
                    notification.MessageBody = imageContent;
                    break;
                case "video":
                    var videoContent = "New message received from #leadName#: 🎥 Video";
                    videoContent = videoContent.Replace("#leadName#", leadName);
                    notification.MessageBody = videoContent;
                    break;
                case "document":
                    var documentContent = "New message received from #leadName#: 📝 Document";
                    documentContent = documentContent.Replace("#leadName#", leadName);
                    notification.MessageBody = documentContent;
                    break;
                case "audio":
                    var audioContent = "New message received from #leadName#: 🎤 Document";
                    documentContent = audioContent.Replace("#leadName#", leadName);
                    notification.MessageBody = audioContent;
                    break;
                default:
                    var textContent = "New message received from #leadName#: #message#";
                    textContent = textContent.Replace("#leadName#", leadName);
                    textContent = textContent.Replace("#message#", message);
                    notification.MessageBody = textContent;
                    break;
            }
            notification.IsMutableContent = true;
            notification.IsAndroidNotification = true;
            notification.Id = Guid.NewGuid();
            notification.Title = "Whatsapp";
            var deepLinkUrl = "app://com.leadrat.black.mobile.droid/main?&screen=21&id=#entityId#";
            deepLinkUrl = deepLinkUrl.Replace("#entityId#", leadId);
            deepLinkUrl = deepLinkUrl.Replace(' ', '#');
            string jobId = string.Empty;
            try
            {
                var notificationDto = notification.Adapt<NotificationDTO>();
                notificationDto.UserIds = new() { userId };
                notificationDto.FCMDeepLinkUrl = deepLinkUrl;
                notificationDto.TenantInfoDto = new()
                {
                    Id = _tenantInfo.Id,
                    ConnectionString = _tenantInfo.ConnectionString,
                    Identifier = _tenantInfo.Identifier,
                    Name = _tenantInfo.Name
                };
                jobId = _hangfireService.Enqueue(() => _notificationService.SendNotificationAsync(notificationDto));
            }
            catch
            {

            }
            return jobId;
        }

        public async Task SendTemplateNotificationAsync<T>(T entity, List<Guid>? eventIds, Lrb.Domain.Enums.Event? @event = null,int? noOfEntities = null)
        {
            if (entity is Lead lead)
            {
                var viewLeadDto = lead.Adapt<ViewLeadDto>();

                if (viewLeadDto?.Status?.Status?.Equals("new", StringComparison.OrdinalIgnoreCase) == true)
                {
                    eventIds ??= new List<Guid>();
                    eventIds.Add(viewLeadDto.Status.Id);
                }
            }
            var notificationConfigs = await _notificationConfig.ListAsync(new GetNotificationTemplateConfigSpec(eventIds, @event));
            PropertyInfo[] properties = typeof(T).GetProperties();

            _ = Guid.TryParse(properties.FirstOrDefault(i => i.Name == "Id")?.GetValue(entity)?.ToString(), out Guid leadId);

            if (leadId != Guid.Empty)
            {
                foreach (var notiConfig in notificationConfigs)
                {
                    var noti = notiConfig.Adapt<NotificationInfoDto>();
                    noti.LeadIds = new() { leadId };
                    noti.TenantInfoDto = new()
                    {
                        Id = _tenantInfo.Id,
                        ConnectionString = _tenantInfo.ConnectionString,
                        Identifier = _tenantInfo.Identifier,
                        Name = _tenantInfo.Name
                    };
                    if (DateTime.TryParse(properties.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity)?.ToString(), out DateTime scheduledDate) && noti.MinutesBefore?.Any() == true)
                    {
                        if (noti.MinutesBefore?.Any() == true)
                        {
                            foreach (var minute in notiConfig.MinutesBefore ?? new List<int>())
                            {
                                var scheduleDateTime = scheduledDate.ToLocalTime();

                                scheduleDateTime = scheduleDateTime.AddMinutes(-minute);

                                _logger.Information($"Scheduling notification at {scheduleDateTime} (UTC)");

                                try
                                {
                                    var jobId = _hangfireService.Schedule(
                                        () => _templateNotificationService.ProcessTemplateNotificationAsync(
                                            noti,
                                            CancellationToken.None,
                                            null,
                                            null,
                                            null,
                                            null,
                                            noOfEntities ?? null
                                        ),
                                        scheduleDateTime
                                    );

                                    _logger.Information($"Scheduled notification with JobId: {jobId}");
                                }
                                catch (Exception ex)
                                {
                                    _logger.Error(ex, "Error scheduling notification, sending immediately.");
                                    await _templateNotificationService.ProcessTemplateNotificationAsync(noti, CancellationToken.None);
                                }
                            }
                        }
                    }
                    else
                    {
                        _logger.Warning("ScheduledDate not found or invalid, sending notification immediately.");
                        noti.LeadIds = new() { leadId };
                        await _templateNotificationService.ProcessTemplateNotificationAsync(noti, CancellationToken.None, noOfEntities : noOfEntities);
                    }
                }
            }
        }
       
    }
}
