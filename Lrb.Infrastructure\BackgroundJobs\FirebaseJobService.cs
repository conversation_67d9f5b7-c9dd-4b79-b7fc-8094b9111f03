using Google.Cloud.Firestore;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.Persistence;
using Lrb.Infrastructure.PushNotification.Firebase;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;

namespace Lrb.Infrastructure.BackgroundJobs
{
    /// <summary>
    /// Firebase-based job service that replaces Hangfire functionality
    /// Uses Firebase Cloud Functions for immediate execution and Cloud Scheduler for delayed/recurring jobs
    /// </summary>
    public class FirebaseJobService : IFirebaseJobService
    {
        private readonly IConfiguration _config;
        private readonly string _env;
        private readonly bool _isNotDevEnv;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly FirebaseSetting _firebaseSetting;
        private readonly CloudSchedulerClient _schedulerClient;
        private readonly CloudFunctionsServiceClient _functionsClient;
        private readonly FirestoreDb _firestoreDb;
        private readonly Serilog.ILogger _logger;

        public string DailyCron { get; set; } = "0 0 * * *"; // Firebase cron format

        public FirebaseJobService(
            IConfiguration config,
            ILeadRepositoryAsync leadRepositoryAsync,
            IOptions<FirebaseSetting> firebaseSetting,
            Serilog.ILogger logger)
        {
            _config = config;
            _leadRepositoryAsync = leadRepositoryAsync;
            _firebaseSetting = firebaseSetting.Value;
            _logger = logger;
            _env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development";
            _isNotDevEnv = !_env.Equals("Development", StringComparison.OrdinalIgnoreCase);

            try
            {
                // Initialize Firebase Admin SDK if not already initialized
                InitializeFirebaseApp();

                // Initialize Google Cloud clients
                var credential = GetGoogleCredential();
                _schedulerClient = CloudSchedulerClient.Create(credential);
                _functionsClient = CloudFunctionsServiceClient.Create(credential);
                _firestoreDb = FirestoreDb.Create(_firebaseSetting.project_id, credential);
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "FirebaseJobService -> Constructor"
                };
                _leadRepositoryAsync.AddErrorAsync(error);
                _logger.Error(ex, "Failed to initialize FirebaseJobService");
                throw;
            }
        }

        #region Immediate Execution (Firebase Cloud Functions)
        public async Task<string> EnqueueAsync(Expression<Func<Task>> methodCall)
        {
            if (!_isNotDevEnv)
                return string.Empty;

            try
            {
                var jobId = Guid.NewGuid().ToString();
                var jobData = SerializeMethodCall(methodCall);

                // Store job in Firestore for Cloud Function to process
                await StoreJobInFirestore(jobId, jobData, DateTime.UtcNow);

                // Trigger Cloud Function for immediate execution
                await TriggerCloudFunction(jobId, jobData);

                return jobId;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "EnqueueAsync");
                return string.Empty;
            }
        }

        public async Task<string> EnqueueAsync<T>(Expression<Action<T>> methodCall)
        {
            if (!_isNotDevEnv)
                return string.Empty;

            try
            {
                var jobId = Guid.NewGuid().ToString();
                var jobData = SerializeMethodCall(methodCall);

                await StoreJobInFirestore(jobId, jobData, DateTime.UtcNow);
                await TriggerCloudFunction(jobId, jobData);

                return jobId;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "EnqueueAsync<T>");
                return string.Empty;
            }
        }

        public async Task<string> EnqueueAsync(Expression<Action> methodCall)
        {
            if (!_isNotDevEnv)
                return string.Empty;

            try
            {
                var jobId = Guid.NewGuid().ToString();
                var jobData = SerializeMethodCall(methodCall);

                await StoreJobInFirestore(jobId, jobData, DateTime.UtcNow);
                await TriggerCloudFunction(jobId, jobData);

                return jobId;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "EnqueueAsync Action");
                return string.Empty;
            }
        }

        public async Task<string> EnqueueAsync<T>(Expression<Func<T, Task>> methodCall)
        {
            if (!_isNotDevEnv)
                return string.Empty;

            try
            {
                var jobId = Guid.NewGuid().ToString();
                var jobData = SerializeMethodCall(methodCall);

                await StoreJobInFirestore(jobId, jobData, DateTime.UtcNow);
                await TriggerCloudFunction(jobId, jobData);

                return jobId;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "EnqueueAsync<T> Func");
                return string.Empty;
            }
        }
        #endregion

        #region Scheduled Execution (Firebase Cloud Scheduler)
        public async Task<string> ScheduleAsync(Expression<Action> methodCall, TimeSpan delay)
        {
            return await ScheduleAsync(methodCall, DateTimeOffset.UtcNow.Add(delay));
        }

        public async Task<string> ScheduleAsync(Expression<Func<Task>> methodCall, TimeSpan delay)
        {
            return await ScheduleAsync(methodCall, DateTimeOffset.UtcNow.Add(delay));
        }

        public async Task<string> ScheduleAsync(Expression<Action> methodCall, DateTimeOffset enqueueAt)
        {
            if (!_isNotDevEnv)
                return string.Empty;

            try
            {
                var jobId = Guid.NewGuid().ToString();
                var jobData = SerializeMethodCall(methodCall);

                // Store job in Firestore
                await StoreJobInFirestore(jobId, jobData, enqueueAt.DateTime);

                // Create Cloud Scheduler job
                await CreateScheduledJob(jobId, jobData, enqueueAt);

                return jobId;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ScheduleAsync Action");
                return string.Empty;
            }
        }

        public async Task<string> ScheduleAsync(Expression<Func<Task>> methodCall, DateTimeOffset enqueueAt)
        {
            if (!_isNotDevEnv)
                return string.Empty;

            try
            {
                var jobId = Guid.NewGuid().ToString();
                var jobData = SerializeMethodCall(methodCall);

                await StoreJobInFirestore(jobId, jobData, enqueueAt.DateTime);
                await CreateScheduledJob(jobId, jobData, enqueueAt);

                return jobId;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ScheduleAsync Func");
                return string.Empty;
            }
        }

        public async Task<string> ScheduleAsync<T>(Expression<Action<T>> methodCall, TimeSpan delay)
        {
            return await ScheduleAsync(methodCall, DateTimeOffset.UtcNow.Add(delay));
        }

        public async Task<string> ScheduleAsync<T>(Expression<Func<T, Task>> methodCall, TimeSpan delay)
        {
            return await ScheduleAsync(methodCall, DateTimeOffset.UtcNow.Add(delay));
        }

        public async Task<string> ScheduleAsync<T>(Expression<Action<T>> methodCall, DateTimeOffset enqueueAt)
        {
            if (!_isNotDevEnv)
                return string.Empty;

            try
            {
                var jobId = Guid.NewGuid().ToString();
                var jobData = SerializeMethodCall(methodCall);

                await StoreJobInFirestore(jobId, jobData, enqueueAt.DateTime);
                await CreateScheduledJob(jobId, jobData, enqueueAt);

                return jobId;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ScheduleAsync<T> Action");
                return string.Empty;
            }
        }

        public async Task<string> ScheduleAsync<T>(Expression<Func<T, Task>> methodCall, DateTimeOffset enqueueAt)
        {
            if (!_isNotDevEnv)
                return string.Empty;

            try
            {
                var jobId = Guid.NewGuid().ToString();
                var jobData = SerializeMethodCall(methodCall);

                await StoreJobInFirestore(jobId, jobData, enqueueAt.DateTime);
                await CreateScheduledJob(jobId, jobData, enqueueAt);

                return jobId;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ScheduleAsync<T> Func");
                return string.Empty;
            }
        }
        #endregion

        #region Job Management
        public async Task<bool> DeleteAsync(string jobId)
        {
            if (!_isNotDevEnv || string.IsNullOrWhiteSpace(jobId))
                return false;

            try
            {
                // Delete from Cloud Scheduler
                var jobName = $"projects/{_firebaseSetting.project_id}/locations/us-central1/jobs/{jobId}";
                try
                {
                    await _schedulerClient.DeleteJobAsync(jobName);
                }
                catch (Google.GoogleApiException ex) when (ex.Error.Code == 404)
                {
                    // Job doesn't exist in scheduler, that's okay
                }

                // Delete from Firestore
                var docRef = _firestoreDb.Collection("scheduled_jobs").Document(jobId);
                await docRef.DeleteAsync();

                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "DeleteAsync");
                return false;
            }
        }

        #region Recurring Jobs
        public async Task CreateRecurringJobAsync(string jobId, Expression<Func<Task>> methodCall, string cronExpression)
        {
            if (!_isNotDevEnv)
                return;

            try
            {
                var jobData = SerializeMethodCall(methodCall);
                await CreateRecurringScheduledJob(jobId, jobData, cronExpression);
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "CreateRecurringJobAsync");
            }
        }

        public async Task CreateRecurringJobAsync<T>(string jobId, Expression<Func<T, Task>> methodCall, string cronExpression)
        {
            if (!_isNotDevEnv)
                return;

            try
            {
                var jobData = SerializeMethodCall(methodCall);
                await CreateRecurringScheduledJob(jobId, jobData, cronExpression);
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "CreateRecurringJobAsync<T>");
            }
        }

        public async Task CreateRecurringJobAsync(string jobId, Expression<Action> methodCall, string cronExpression)
        {
            if (!_isNotDevEnv)
                return;

            try
            {
                var jobData = SerializeMethodCall(methodCall);
                await CreateRecurringScheduledJob(jobId, jobData, cronExpression);
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "CreateRecurringJobAsync Action");
            }
        }

        public async Task CreateRecurringJobAsync<T>(string jobId, Expression<Action<T>> methodCall, string cronExpression)
        {
            if (!_isNotDevEnv)
                return;

            try
            {
                var jobData = SerializeMethodCall(methodCall);
                await CreateRecurringScheduledJob(jobId, jobData, cronExpression);
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "CreateRecurringJobAsync<T> Action");
            }
        }

        public async Task DeleteRecurringJobIfExistsAsync(string jobId)
        {
            if (!_isNotDevEnv)
                return;

            try
            {
                await DeleteAsync(jobId);
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "DeleteRecurringJobIfExistsAsync");
            }
        }

        public async Task TriggerRecurringJobByIdAsync(string jobId)
        {
            if (!_isNotDevEnv)
                return;

            try
            {
                // Retrieve job data from Firestore and trigger immediately
                var docRef = _firestoreDb.Collection("scheduled_jobs").Document(jobId);
                var snapshot = await docRef.GetSnapshotAsync();

                if (snapshot.Exists)
                {
                    var jobData = snapshot.GetValue<string>("jobData");
                    await TriggerCloudFunction(jobId, jobData);
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "TriggerRecurringJobByIdAsync");
            }
        }
        #endregion

        #region Private Helper Methods
        private void InitializeFirebaseApp()
        {
            try
            {
                // Check if app already exists
                var existingApp = FirebaseApp.GetInstance("job-service");
                if (existingApp != null)
                    return;

                // Create new app
                var firebaseFile = JsonConvert.SerializeObject(_firebaseSetting);
                using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(firebaseFile));

                var credential = GoogleCredential.FromStream(memoryStream);
                var options = new AppOptions()
                {
                    Credential = credential,
                    ProjectId = _firebaseSetting.project_id
                };

                FirebaseApp.Create(options, "job-service");
            }
            catch (Exception ex)
            {
                _logger.Error(ex, "Failed to initialize Firebase app for job service");
                throw;
            }
        }

        private GoogleCredential GetGoogleCredential()
        {
            var firebaseFile = JsonConvert.SerializeObject(_firebaseSetting);
            using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(firebaseFile));
            return GoogleCredential.FromStream(memoryStream);
        }

        private string SerializeMethodCall(Expression expression)
        {
            // Serialize the expression to JSON for storage and later execution
            // This is a simplified implementation - in production, you might want to use a more robust serialization method
            return JsonConvert.SerializeObject(new
            {
                ExpressionType = expression.GetType().Name,
                ExpressionString = expression.ToString(),
                Timestamp = DateTime.UtcNow
            });
        }

        private async Task StoreJobInFirestore(string jobId, string jobData, DateTime scheduledTime)
        {
            try
            {
                var docRef = _firestoreDb.Collection("scheduled_jobs").Document(jobId);
                await docRef.SetAsync(new
                {
                    jobId = jobId,
                    jobData = jobData,
                    scheduledTime = scheduledTime,
                    status = "pending",
                    createdAt = DateTime.UtcNow
                });
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Failed to store job {jobId} in Firestore");
                throw;
            }
        }

        private async Task TriggerCloudFunction(string jobId, string jobData)
        {
            try
            {
                // This would trigger a Cloud Function that processes the job
                // The actual implementation depends on your Cloud Function setup

                // For now, we'll use a simple HTTP trigger approach
                var functionUrl = $"https://us-central1-{_firebaseSetting.project_id}.cloudfunctions.net/processJob";

                using var httpClient = new HttpClient();
                var payload = JsonConvert.SerializeObject(new { jobId, jobData });
                var content = new StringContent(payload, Encoding.UTF8, "application/json");

                var response = await httpClient.PostAsync(functionUrl, content);
                response.EnsureSuccessStatusCode();
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Failed to trigger Cloud Function for job {jobId}");
                throw;
            }
        }

        private async Task CreateScheduledJob(string jobId, string jobData, DateTimeOffset enqueueAt)
        {
            try
            {
                var parent = $"projects/{_firebaseSetting.project_id}/locations/us-central1";
                var jobName = $"{parent}/jobs/{jobId}";

                var job = new Job
                {
                    Name = jobName,
                    HttpTarget = new HttpTarget
                    {
                        Uri = $"https://us-central1-{_firebaseSetting.project_id}.cloudfunctions.net/processScheduledJob",
                        HttpMethod = HttpMethod.Post,
                        Body = Google.Protobuf.ByteString.CopyFromUtf8(JsonConvert.SerializeObject(new { jobId, jobData })),
                        Headers = { ["Content-Type"] = "application/json" }
                    },
                    Schedule = ConvertToScheduleExpression(enqueueAt),
                    TimeZone = "UTC"
                };

                await _schedulerClient.CreateJobAsync(parent, job);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Failed to create scheduled job {jobId}");
                throw;
            }
        }

        private async Task CreateRecurringScheduledJob(string jobId, string jobData, string cronExpression)
        {
            try
            {
                var parent = $"projects/{_firebaseSetting.project_id}/locations/us-central1";
                var jobName = $"{parent}/jobs/{jobId}";

                var job = new Job
                {
                    Name = jobName,
                    HttpTarget = new HttpTarget
                    {
                        Uri = $"https://us-central1-{_firebaseSetting.project_id}.cloudfunctions.net/processRecurringJob",
                        HttpMethod = HttpMethod.Post,
                        Body = Google.Protobuf.ByteString.CopyFromUtf8(JsonConvert.SerializeObject(new { jobId, jobData })),
                        Headers = { ["Content-Type"] = "application/json" }
                    },
                    Schedule = cronExpression,
                    TimeZone = "UTC"
                };

                await _schedulerClient.CreateJobAsync(parent, job);
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Failed to create recurring job {jobId}");
                throw;
            }
        }

        private string ConvertToScheduleExpression(DateTimeOffset enqueueAt)
        {
            // Convert DateTimeOffset to cron expression for one-time execution
            var utcTime = enqueueAt.UtcDateTime;
            return $"{utcTime.Minute} {utcTime.Hour} {utcTime.Day} {utcTime.Month} *";
        }

        private async Task LogErrorAsync(Exception ex, string context)
        {
            try
            {
                _logger.Error(ex, $"FirebaseJobService -> {context}: {ex.Message}");
            }
            catch
            {
                // Fallback logging if repository fails
                _logger.Error(ex, $"FirebaseJobService -> {context}: {ex.Message}");
            }
        }
        #endregion
    }
}
