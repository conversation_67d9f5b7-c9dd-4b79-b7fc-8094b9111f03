﻿using Lrb.Application.UserDetails.Web;

namespace Lrb.Application.AutoDialer.Web.Requests
{
    public class GetUserCallingStatusRequest : IRequest<Response<bool>>
    {
    }
    public class GetUserCallingStatusRequestHandler : IRequestHandler<GetUserCallingStatusRequest, Response<bool>>
    {
        public readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
        public readonly ICurrentUser _currentUser;
        public GetUserCallingStatusRequestHandler(IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo, ICurrentUser currentUser)
        {
            _userDetailsRepo = userDetailsRepo;
            _currentUser = currentUser;
        }
        public async Task<Response<bool>> Handle(GetUserCallingStatusRequest request, CancellationToken cancellationToken)
        {
            var userId = _currentUser.GetUserId();
            var IsAvilableForCall = (await _userDetailsRepo.FirstOrDefaultAsync(new GetUsersSpec(new List<Guid> { userId })))?.IsAvilableForCall ?? false;
            return new(IsAvilableForCall);
        }
    }
}