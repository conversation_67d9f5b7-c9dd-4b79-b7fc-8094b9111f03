﻿using Lrb.Application.Lead.Web.Requests.BulkUploadTracker;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.DataManagement.Web.Request.Bulk_Upload
{
    public class GetAllBulkProspectTrackerRequest : PaginationFilter, IRequest<PagedResponse<BulkProspectUploadTracker, string>>
    {
    }

    public class GetAllBulkProspectTrackerRequestHandler : IRequestHandler<GetAllBulkProspectTrackerRequest, PagedResponse<BulkProspectUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkProspectUploadTracker> _bulkProspectTracker;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllBulkProspectTrackerRequestHandler(IRepositoryWithEvents<BulkProspectUploadTracker> bulkProspectTracker, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _bulkProspectTracker = bulkProspectTracker;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<BulkProspectUploadTracker, string>> Handle(GetAllBulkProspectTrackerRequest request, CancellationToken cancellationToken)
        {
            try
            {
                var currentUserId = _currentUser.GetUserId();
                var tenantId = _currentUser.GetTenant();
                var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
                var trackers = await _bulkProspectTracker.ListAsync(new BulkProspectUploadSpec(request, subIds), cancellationToken);
                var totalCount = await _bulkProspectTracker.CountAsync(new BulkProspectUploadCountSpec(subIds), cancellationToken);
                return new(trackers, totalCount);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }

    public class BulkProspectUploadSpec : EntitiesByPaginationFilterSpec<BulkProspectUploadTracker>
    {
        public BulkProspectUploadSpec(GetAllBulkProspectTrackerRequest filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class BulkProspectUploadCountSpec : Specification<BulkProspectUploadTracker>
    {
        public BulkProspectUploadCountSpec( List<Guid> subIds) 
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
