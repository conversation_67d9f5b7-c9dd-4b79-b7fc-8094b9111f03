﻿using Lrb.Application.DataManagement.Web.Request.Bulk_Upload;
using Lrb.Application.Project.Web.Specs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Project.Web.Requests.Bulk_Upload
{
    public class GetAllBulkUnitTrackerRequest : PaginationFilter, IRequest<PagedResponse<BulkUnitUploadTracker, string>>
    {
    }
    public class GetAllBulkUnitrackerRequestHandler : IRequestHandler<GetAllBulkUnitTrackerRequest, PagedResponse<BulkUnitUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkUnitUploadTracker> _bulkUnitTracker;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllBulkUnitrackerRequestHandler(IRepositoryWithEvents<BulkUnitUploadTracker> bulkUnitTracker, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _bulkUnitTracker = bulkUnitTracker;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<BulkUnitUploadTracker, string>> Handle(GetAllBulkUnitTrackerRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _bulkUnitTracker.ListAsync(new BulkUnitUploadSpec(request, subIds), cancellationToken);
            var totalCount = await _bulkUnitTracker.CountAsync(new BulkUnitUploadCountSpec( subIds), cancellationToken);
            return new(trackers, totalCount);
        }

    }
}
