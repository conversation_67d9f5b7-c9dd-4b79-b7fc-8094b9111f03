﻿namespace Lrb.Application.Lead.Web
{
    public class GetExportLeadTrackersSpec : EntitiesByPaginationFilterSpec<ExportLeadTracker>
    {
        public GetExportLeadTrackersSpec(GetAllExportLeadTracker filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy)).OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetExportLeadTrackersCountSpec : Specification<ExportLeadTracker>
    {
        public GetExportLeadTrackersCountSpec(List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
