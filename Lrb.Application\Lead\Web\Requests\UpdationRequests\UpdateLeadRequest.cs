﻿using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Lead.Web.Requests;

namespace Lrb.Application.Lead.Web
{
    public class UpdateLeadRequest : UpdateLeadDto, IRequest<Response<Guid>>
    {
    }
    public class UpdateLeadRequestHandler : LeadCommonRequestHandler, IRequestHandler<UpdateLeadRequest, Response<Guid>>
    {
        public UpdateLeadRequestHandler(IServiceProvider serviceProvider) : base(serviceProvider, typeof(UpdateLeadRequestHandler).Name, "Handle")
        {
        }
        public async Task<Response<Guid>> Handle(UpdateLeadRequest request, CancellationToken cancellationToken)
        {
            try
            {
                Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                var (ContactNo, AltContactNo) = await ValidateContactNoAsync(request.ContactNo, request.AlternateContactNo, globalSettings, cancellationToken);
                request.ContactNo = ContactNo;
                request.AlternateContactNo = AltContactNo;

                var existingFullLead = await GetExistingLeadAsync(request.Id, request.ContactNo, cancellationToken) ?? throw new NotFoundException("Lead not found");
                var existingContactNo = existingFullLead.ContactNo;
                var existingAltContactNo = existingFullLead.AlternateContactNo;
                var newAddress = await CreateAddressAsync(request.Address, cancellationToken);

                try
                {
                    existingFullLead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(existingFullLead, request.Adapt<PickedLeadDto>(), isLeadFormUpdated: true);
                }
                catch (Exception ex)
                {
                    throw;
                }
                var existingAssignedUserId = existingFullLead.AssignTo;
                var existingSecondaryUserId = existingFullLead.SecondaryUserId;
                var lead = request.Adapt(existingFullLead);
                lead.ChannelPartners = null;
                lead.Address = newAddress;
                lead.AssignedFrom = (existingAssignedUserId != lead.AssignTo) ? existingAssignedUserId : lead.AssignedFrom;
                lead.SecondaryFromUserId = (existingSecondaryUserId != lead.SecondaryFromUserId) ? existingSecondaryUserId : lead.SecondaryFromUserId;
                await SetLeadStatusAsync(lead, request.LeadStatusId, cancellationToken);
                await SetLeadAssignedToAsync(lead, request.AssignTo, cancellationToken);

                lead.OriginalOwner = existingFullLead.OriginalOwner;
                if (lead.OriginalOwner == null || lead.OriginalOwner == Guid.Empty)
                {
                    lead.OriginalOwner = lead.AssignTo;
                }
                else if (lead.OriginalOwner != existingAssignedUserId && lead.OriginalOwner != existingSecondaryUserId)
                {
                    lead.OriginalOwner = existingFullLead.OriginalOwner;
                }
                await SetLeadProjectsAsync(lead, request.ProjectsList, globalSettings, cancellationToken);

                await SetLeadPropertiesAsync(lead, request.PropertiesList, globalSettings, cancellationToken);
                await SetChannelPartnersAsync(lead, request.ChannelPartnerList, cancellationToken:cancellationToken);

                var agenciesToAdd = request.Agencies?.Where(i => !string.IsNullOrWhiteSpace(i.Name)).Select(i => i.Name).ToList();
                
                await SetLeadAgencyAsync(lead, agenciesToAdd ?? default, cancellationToken: cancellationToken);
                var campaignToAdd = request.Campaigns?.Where(i => !string.IsNullOrWhiteSpace(i.Name)).Select(i => i.Name).ToList();
                await SetLeadCampaignAsync(lead, campaignToAdd ?? default, cancellationToken: cancellationToken);
                //try
                //{
                //    var originalLead = lead.CreateDeepCopy();
                //    lead.ShouldUpdatePickedDate = await ShouldUpdatePickedDate(originalLead, request.Adapt<PickedLeadDto>());
                //}
                //catch (Exception ex)
                //{
                //    throw;
                //}

                await _leadRepo.UpdateAsync(lead);

                var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(request.Id), cancellationToken) ?? throw new NotFoundException("Lead not found, may be deleted.");

                await UpdateLeadTagInfoAsync(fullLead, request.LeadTags, cancellationToken);

                //var propertyType = await ValidatePropertyTypeAsync(request.Enquiry?.PropertyTypeId, cancellationToken);
                var propertyTypes = await ValidatePropertyTypesAsync(request?.Enquiry?.PropertyTypeIds, cancellationToken);

                var enquiredAddress = await CreateAddressAsync(request.Enquiry?.Address, cancellationToken);

                var enquiredAddresses = await CreateAddressesAsync(request.Enquiry?.Addresses, cancellationToken);

                await UpdateLeadEnquiryAsync(fullLead, request.Enquiry, enquiredAddress, enquiredAddresses, propertyTypes?.FirstOrDefault(), cancellationToken, propertyTypes);

                if (request.AssignTo != null && request.AssignTo != Guid.Empty && (request.AssignTo != existingAssignedUserId))
                {
                    AssignLeadsBasedOnScenariosRequest assignmentRequest = new()
                    {
                        LeadIds = new() { fullLead.Id },
                        UserIds = new() { request.AssignTo ?? Guid.Empty },
                        AssignmentType = LeadAssignmentType.WithHistory,
                        LeadSource = fullLead.Enquiries.FirstOrDefault(i => i.IsPrimary)?.LeadSource ?? default,
                    };
                    var assignmentResponse = await _mediator.Send(assignmentRequest);
                }
                if ((existingContactNo != ContactNo) || (AltContactNo != null && ( existingAltContactNo != AltContactNo)))
                {
                    await UpdateDuplicateVersionAsync(fullLead, cancellationToken: cancellationToken);
                }

                await UpdateLeadHistoryAsync(fullLead, cancellationToken: cancellationToken);

                await CreateLeadAssignmentHistory(lead, LeadAssignmentType.WithHistory);

                await SendLeadUpdateNotificationsAsync(fullLead, globalSettings, cancellationToken);

                await ScheduleLeadRetentionRotation(new List<Domain.Entities.Lead>() { fullLead }, globalSettings, cancellationToken);

                return new(lead.Id);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{typeof(UpdateLeadRequestHandler).Name} - Handle()");
                throw;
            }
        }
    }
}
