using Finbuckle.MultiTenant;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Google.Cloud.Firestore;
using Google.Cloud.Scheduler.V1;
using Lrb.Application.Common.Email;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.WhatsApp;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Email.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Notifications.Dtos;
using Lrb.Application.Notifications.Specs;
using Lrb.Application.PushNotification.Web.Dtos;
using Lrb.Application.Utils;
using Lrb.Application.WhatsAppCloudApi.Web;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.PushNotification.Firebase;
using Mapster;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using System.Reflection;
using System.Text;

namespace Lrb.Infrastructure.PushNotification
{
    /// <summary>
    /// Firebase-based notification service that replaces Hangfire and AWS Pinpoint
    /// Provides the same interface as NotificationSenderService but uses Firebase for scheduling and delivery
    /// </summary>
    public class FirebaseNotificationSenderService : INotificationSenderService
    {
        #region Private Fields
        private readonly IFirebaseJobService _firebaseJobService;
        private readonly Application.Common.PushNotification.INotificationService _notificationService;
        private readonly INotificationMessageBuilder _notificationMessageBuilder;
        private readonly ICurrentUser _currentUser;
        private readonly IUserService _userService;
        private readonly ITenantInfo _tenantInfo;
        private readonly IRepositoryWithEvents<NotificationTracker> _notificationTrackerRepo;
        private readonly IRepositoryWithEvents<Notification> _notificationRepo;
        private readonly IRepositoryWithEvents<NotificationServiceTracker> _notificationServiceTrackerRepo;
        private readonly Serilog.ILogger _logger;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IRepositoryWithEvents<MasterEmailServiceProvider> _masterEmailServiceProviderRepo;
        private readonly IRepositoryWithEvents<NotificationContent> _notificationContentRepo;
        private readonly IRepositoryWithEvents<GlobalSettings> _globalSettingsRepo;
        private readonly IRepositoryWithEvents<WhatsAppTemplateInfo> _whatsAppTemplateInfoRepo;
        private readonly IWhatsAppSenderService _whatsAppSenderService;
        private readonly IReadRepository<Lead> _leadRepo;
        private readonly ITemplateNotificationService _templateNotificationService;
        private readonly IRepositoryWithEvents<NotificationInfo> _notificationConfig;
        private readonly IDapperRepository _dapperRepositoryRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Device> _deviceRepo;
        private readonly FirebaseSetting _firebaseSetting;
        private readonly MobileFirebaseSetting _mobileFirebaseSetting;
        #endregion

        #region Constructor
        public FirebaseNotificationSenderService(
            IFirebaseJobService firebaseJobService,
            Application.Common.PushNotification.INotificationService notificationService,
            INotificationMessageBuilder notificationMessageBuilder,
            ICurrentUser currentUser,
            IUserService userService,
            ITenantInfo tenantInfo,
            IRepositoryWithEvents<NotificationTracker> notificationTrackerRepo,
            IRepositoryWithEvents<Notification> notificationRepo,
            IRepositoryWithEvents<NotificationServiceTracker> notificationServiceTrackerRepo,
            Serilog.ILogger logger,
            ILeadRepositoryAsync leadRepositoryAsync,
            IGraphEmailService graphEmailService,
            IRepositoryWithEvents<MasterEmailServiceProvider> masterEmailServiceProviderRepo,
            IRepositoryWithEvents<NotificationContent> notificationContentRepo,
            IRepositoryWithEvents<GlobalSettings> globalSettingsRepo,
            IRepositoryWithEvents<WhatsAppTemplateInfo> whatsAppTemplateInfoRepo,
            IWhatsAppSenderService whatsAppSenderService,
            IReadRepository<Lead> leadRepo,
            ITemplateNotificationService templateNotificationService,
            IRepositoryWithEvents<NotificationInfo> notificationConfig,
            IDapperRepository dapperRepositoryRepo,
            IRepositoryWithEvents<Domain.Entities.Device> deviceRepo,
            IOptions<FirebaseSetting> firebaseSetting,
            IOptions<MobileFirebaseSetting> mobileFirebaseSetting)
        {
            _firebaseJobService = firebaseJobService;
            _notificationService = notificationService;
            _notificationMessageBuilder = notificationMessageBuilder;
            _currentUser = currentUser;
            _userService = userService;
            _tenantInfo = tenantInfo;
            _notificationTrackerRepo = notificationTrackerRepo;
            _notificationRepo = notificationRepo;
            _notificationServiceTrackerRepo = notificationServiceTrackerRepo;
            _logger = logger;
            _leadRepositoryAsync = leadRepositoryAsync;
            _graphEmailService = graphEmailService;
            _masterEmailServiceProviderRepo = masterEmailServiceProviderRepo;
            _notificationContentRepo = notificationContentRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _whatsAppTemplateInfoRepo = whatsAppTemplateInfoRepo;
            _whatsAppSenderService = whatsAppSenderService;
            _leadRepo = leadRepo;
            _templateNotificationService = templateNotificationService;
            _notificationConfig = notificationConfig;
            _dapperRepositoryRepo = dapperRepositoryRepo;
            _deviceRepo = deviceRepo;
            _firebaseSetting = firebaseSetting.Value;
            _mobileFirebaseSetting = mobileFirebaseSetting.Value;
        }
        #endregion

        #region Public Methods - Interface Implementation
        public async Task<List<string>> ScheduleNotificationsAsync<T>(
            Domain.Enums.Event @event, 
            T entity, 
            Guid? userId = default, 
            string? userName = default, 
            List<string>? topics = null, 
            int? noOfEntities = null, 
            Guid? currentUserIdPm = null, 
            List<Guid>? userIds = null, 
            LeadSource? leadSourceParam = null, 
            Guid? currentUserId = null, 
            int? rotationTime = null, 
            Domain.Entities.GlobalSettings? globalSettings = null, 
            List<UserDetailsDto>? allUserDetails = null, 
            List<NotificationContent>? contents = null, 
            string? status = null, 
            double? bufferTime = null)
        {
            _logger.Information("FirebaseNotificationSenderService -> ScheduleNotificationsAsync Called");
            
            List<Notification> notifications = new();
            currentUserId = currentUserId ?? _currentUser.GetUserId();
            globalSettings ??= await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), CancellationToken.None);
            
            var currentUserDetails = allUserDetails?.FirstOrDefault(i => (currentUserId != null) && i.Id == currentUserId);
            currentUserDetails ??= (await _userService.GetListOfUsersByIdsAsync(new List<string>() { currentUserId?.ToString() ?? string.Empty }, CancellationToken.None)).FirstOrDefault();
            
            if (currentUserDetails == null && currentUserIdPm != null)
            {
                currentUserDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { currentUserIdPm?.ToString() ?? string.Empty }, CancellationToken.None)).FirstOrDefault();
            }

            // Build notifications using existing message builder
            if (userId != Guid.Empty && userId != null)
            {
                try
                {
                    if (!string.IsNullOrWhiteSpace(userName))
                    {
                        notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, userName, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime, contents: contents, status: status, bufferTime: bufferTime);
                    }
                    else
                    {
                        UserDetailsDto? userDetails = allUserDetails?.FirstOrDefault(i => (userId != default) && i.Id == userId);
                        userDetails ??= (await _userService.GetListOfUsersByIdsAsync(new List<string>() { userId?.ToString() ?? string.Empty }, CancellationToken.None)).FirstOrDefault();
                        notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, userDetails?.FirstName + " " + userDetails?.LastName, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime, contents: contents, status: status);
                    }
                }
                catch (NotFoundException ex)
                {
                    if (currentUserDetails != null)
                    {
                        notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, currentUserDetails.FirstName + " " + currentUserDetails.LastName, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime, contents: contents, status: status);
                    }
                    await LogErrorAsync(ex, "ScheduleNotificationsAsync - User not found");
                }
            }
            else
            {
                if (currentUserDetails != null)
                {
                    notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, userName ?? currentUserDetails.FirstName + " " + currentUserDetails.LastName, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime, contents: contents, status: status);
                }
                else if (userIds?.Any() ?? false)
                {
                    notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, string.Empty, noOfEntities, currentUserDetails, userIds, leadSourceParam: leadSourceParam, rotationTime: rotationTime, contents: contents, status: status);
                }
            }

            List<string> schedulingResponse = new();
            PropertyInfo[] properties = typeof(T).GetProperties();
            string? entityId = properties?.FirstOrDefault(i => i?.Name == "Id")?.GetValue(entity).ToString() ?? string.Empty;
            DateTime scheduledDate = DateTime.UtcNow;
            
            string type = typeof(T).FullName;
            if (type != null)
            {
                if (type == typeof(Domain.Entities.Todo).FullName)
                {
                    scheduledDate = (DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDateTime")?.GetValue(entity) ?? DateTime.UtcNow);
                }
                else if (type == typeof(Domain.Entities.Lead).FullName)
                {
                    scheduledDate = (DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                }
            }

            // Delete existing scheduled notifications using Firebase
            try
            {
                List<string> existingNotificationTrackerInfo = (await _dapperRepositoryRepo.GetNotificationTrackersJobId(_tenantInfo.Id ?? _currentUser.GetTenant(), Guid.Parse(entityId), scheduledDate.ToUniversalTime())).ToList();
                if (existingNotificationTrackerInfo != null && existingNotificationTrackerInfo.Count > 0)
                {
                    foreach (var jobId in existingNotificationTrackerInfo)
                    {
                        await _firebaseJobService.DeleteAsync(jobId ?? string.Empty);
                    }
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ScheduleNotificationsAsync - Delete existing notifications");
            }

            globalSettings ??= (await _globalSettingsRepo.ListAsync()).FirstOrDefault();
            NotificationSettings? notificationSettings = globalSettings != null ? GetNotificationSettings(globalSettings) : null;
            List<NotificationTracker> newNotificationTrackerInfo = new();

            // Process each notification
            foreach (Notification notification in notifications)
            {
                string jobId = string.Empty;
                try
                {
                    var notificationDto = notification.Adapt<NotificationDTO>();
                    notificationDto.CurrentUserId = currentUserId ?? _currentUser.GetUserId();
                    
                    if (notificationDto.CurrentUserId == Guid.Empty && currentUserDetails != null)
                    {
                        notificationDto.CurrentUserId = currentUserDetails.Id;
                    }
                    
                    if (userIds?.Any() ?? false)
                    {
                        notificationDto.UserIds = userIds;
                    }
                    else
                    {
                        var loneUserId = userId == null || userId == Guid.Empty ? currentUserId ?? default : userId.Value;
                        notificationDto.UserIds = new() { loneUserId };
                    }
                    
                    notificationDto.TenantInfoDto = new()
                    {
                        Id = _tenantInfo.Id,
                        ConnectionString = _tenantInfo.ConnectionString,
                        Identifier = _tenantInfo.Identifier,
                        Name = _tenantInfo.Name
                    };

                    // Schedule or enqueue notification using Firebase
                    if (notificationDto.IsScheduled == true)
                    {
                        notificationDto.IsScheduled = false;
                        jobId = await _firebaseJobService.ScheduleAsync(() => _notificationService.SendNotificationAsync(notificationDto), notificationDto.ScheduledDate);
                        
                        // Handle email notifications
                        var emailSenderDto = await _notificationMessageBuilder.BuildEmailNotification(entity, notificationSettings, notificationDto, currentUserDetails ?? new(), noOfEntities, globalSettings, userName, rotationTime: rotationTime);
                        if (emailSenderDto != null && (@event == Domain.Enums.Event.LeadFromIntegration || @event == Domain.Enums.Event.DuplicateLeadEnquiryAlert))
                        {
                            await _firebaseJobService.ScheduleAsync(() => _notificationService.SendEmailNotification(emailSenderDto), notificationDto.ScheduledDate);
                        }

                        // Handle web notifications
                        await ProcessWebNotifications(userId, userIds, notificationDto, notificationSettings, true);
                    }
                    else
                    {
                        jobId = await _firebaseJobService.EnqueueAsync(() => _notificationService.SendNotificationAsync(notificationDto));
                        
                        // Handle email notifications
                        var emailSenderDto = await _notificationMessageBuilder.BuildEmailNotification(entity, notificationSettings, notificationDto, currentUserDetails ?? new(), noOfEntities, globalSettings, userName, rotationTime: rotationTime);
                        if (emailSenderDto != null && (@event == Domain.Enums.Event.LeadFromIntegration || @event == Domain.Enums.Event.DuplicateLeadEnquiryAlert))
                        {
                            await _firebaseJobService.EnqueueAsync(() => _notificationService.SendEmailNotification(emailSenderDto));
                        }
                        else if (emailSenderDto != null && userId != null && userId != currentUserId)
                        {
                            await _firebaseJobService.EnqueueAsync(() => _notificationService.SendEmailNotification(emailSenderDto));
                        }
                        else if ((emailSenderDto != null) && (userIds?.Any() ?? false))
                        {
                            await _firebaseJobService.EnqueueAsync(() => _notificationService.SendEmailNotification(emailSenderDto));
                        }

                        // Handle web notifications
                        await ProcessWebNotifications(userId, userIds, notificationDto, notificationSettings, false);
                    }
                    
                    schedulingResponse.Add(jobId);
                    
                    if (!string.IsNullOrWhiteSpace(entityId))
                    {
                        NotificationTracker notificationTracker = new()
                        {
                            NotificationId = notificationDto.Id,
                            EntityId = Guid.Parse(entityId),
                            Event = @event,
                            ScheduleTime = scheduledDate,
                            JobId = jobId
                        };
                        newNotificationTrackerInfo.Add(notificationTracker);
                    }
                }
                catch (Exception ex)
                {
                    await LogErrorAsync(ex, "ScheduleNotificationsAsync - Process notification");
                }
            }

            // Save notification trackers
            try
            {
                foreach (var notificationTracker in newNotificationTrackerInfo)
                {
                    await _notificationTrackerRepo.AddAsync(notificationTracker);
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ScheduleNotificationsAsync - Save trackers");
                throw;
            }

            // Handle WhatsApp notifications
            try
            {
                if (entity is Lead lead)
                {
                    await SendWhatsAppNotificationAsync(entity, @event, notificationSettings ?? new(), globalSettings ?? new(), lead: lead, userName: userName);
                }
                else
                {
                    await SendWhatsAppNotificationAsync(entity, @event, notificationSettings ?? new(), globalSettings ?? new());
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ScheduleNotificationsAsync - WhatsApp notifications");
                throw;
            }

            // Handle template notifications
            try
            {
                if (entity is Lead lead)
                {
                    await SendTemplateNotificationAsync(entity, null, @event, noOfEntities: noOfEntities);
                }
                
                if (globalSettings?.IsEngageToEnabled ?? false)
                {
                    await _notificationService.SendLeadUpdateToEngageto(entity, CancellationToken.None);
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ScheduleNotificationsAsync - Template notifications");
            }

            return schedulingResponse;
        }

        public async Task<List<string>> SchedulePushNotificationsAsync<T>(
            Domain.Enums.Event @event,
            T entity,
            Domain.Entities.GlobalSettings? globalSettings,
            List<Guid>? userIds = default,
            string? userName = default,
            List<string>? topics = null,
            Guid? currentUserId = null)
        {
            _logger.Information("FirebaseNotificationSenderService -> SchedulePushNotificationsAsync Called");

            List<Notification> notifications = new();
            currentUserId = currentUserId ?? _currentUser.GetUserId();
            var currentUserDetails = await _userService.GetAsync(currentUserId.ToString() ?? string.Empty, CancellationToken.None);
            Dictionary<Guid, List<Notification>> notificationsByUserId = new();

            if (userIds != null && userIds.Any())
            {
                foreach (var userId in userIds)
                {
                    try
                    {
                        UserDetailsDto userDetails = await _userService.GetAsync(userId.ToString() ?? string.Empty, CancellationToken.None);
                        var userNotifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, userDetails?.FirstName + " " + userDetails?.LastName, null, currentUserDetails, new List<Guid> { userId });
                        notificationsByUserId[userId] = userNotifications;
                        notifications.AddRange(userNotifications);
                    }
                    catch (Exception ex)
                    {
                        await LogErrorAsync(ex, $"SchedulePushNotificationsAsync - Process user {userId}");
                    }
                }
            }
            else
            {
                notifications = await _notificationMessageBuilder.BuildNotificationsV1Async(@event, entity, userName ?? currentUserDetails?.FirstName + " " + currentUserDetails?.LastName, null, currentUserDetails, userIds);
            }

            List<string> schedulingResponse = new();
            PropertyInfo[] properties = typeof(T).GetProperties();
            string? entityId = properties?.FirstOrDefault(i => i?.Name == "Id")?.GetValue(entity).ToString() ?? string.Empty;
            DateTime scheduledDate = DateTime.UtcNow;

            string type = typeof(T).FullName;
            if (type != null)
            {
                if (type == typeof(Domain.Entities.Todo).FullName)
                {
                    scheduledDate = (DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDateTime")?.GetValue(entity) ?? DateTime.UtcNow);
                }
                else if (type == typeof(Domain.Entities.Lead).FullName)
                {
                    scheduledDate = (DateTime)(properties?.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity) ?? DateTime.UtcNow);
                }
            }

            List<NotificationServiceTracker> trackers = new();

            foreach (Notification notification in notifications)
            {
                string jobId = string.Empty;
                try
                {
                    var notificationDto = notification.Adapt<NotificationDTO>();
                    notificationDto.CurrentUserId = currentUserId ?? _currentUser.GetUserId();

                    if (userIds?.Any() ?? false)
                    {
                        notificationDto.UserIds = userIds;
                    }
                    else
                    {
                        notificationDto.UserIds = new() { currentUserId ?? Guid.Empty };
                    }

                    notificationDto.TenantInfoDto = new()
                    {
                        Id = _tenantInfo.Id,
                        ConnectionString = _tenantInfo.ConnectionString,
                        Identifier = _tenantInfo.Identifier,
                        Name = _tenantInfo.Name
                    };

                    // Use Firebase for scheduling
                    if (notification.IsScheduled == true)
                    {
                        notification.IsScheduled = false;
                        jobId = await _firebaseJobService.ScheduleAsync(() => _notificationService.SendNotificationAsync(notificationDto), notificationDto.ScheduledDate.ToLocalTime());
                    }
                    else
                    {
                        jobId = await _firebaseJobService.EnqueueAsync(() => _notificationService.SendNotificationAsync(notificationDto));
                    }

                    schedulingResponse.Add(jobId);

                    NotificationServiceTracker tracker = new();
                    tracker.JobId = jobId;
                    tracker.EntityId = entityId == null ? Guid.Empty : Guid.Parse(entityId);
                    tracker.Event = @event;
                    tracker.ScheduledDate = scheduledDate;
                    trackers.Add(tracker);
                }
                catch (Exception ex)
                {
                    await LogErrorAsync(ex, "SchedulePushNotificationsAsync - Process notification");
                }
            }

            // Save trackers
            try
            {
                await _notificationServiceTrackerRepo.AddRangeAsync(trackers);
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SchedulePushNotificationsAsync - Save trackers");
            }

            return schedulingResponse;
        }

        public async Task<bool> DeleteScheduledNotificationsAsync<T>(T entity)
        {
            try
            {
                PropertyInfo[] properties = typeof(T).GetProperties();
                string? entityId = properties?.FirstOrDefault(i => i?.Name == "Id")?.GetValue(entity).ToString();

                if (string.IsNullOrWhiteSpace(entityId))
                    return false;

                var trackers = await _notificationTrackerRepo.ListAsync(new NotificationTrackerByEntityIdSpec(Guid.Parse(entityId)));

                foreach (var tracker in trackers)
                {
                    if (!string.IsNullOrWhiteSpace(tracker.JobId))
                    {
                        await _firebaseJobService.DeleteAsync(tracker.JobId);
                    }
                }

                await _notificationTrackerRepo.DeleteRangeAsync(trackers);
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "DeleteScheduledNotificationsAsync");
                return false;
            }
        }

        public async Task<bool> DeleteScheduledNotificationsBulkAsync<T>(List<T> entities)
        {
            try
            {
                foreach (var entity in entities)
                {
                    await DeleteScheduledNotificationsAsync(entity);
                }
                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "DeleteScheduledNotificationsBulkAsync");
                return false;
            }
        }

        public async Task SendWhatsAppNotificationAsync<T>(
            T entity,
            Domain.Enums.Event @event,
            NotificationSettings? notificationSettings,
            Domain.Entities.GlobalSettings globalSettings,
            bool? isFeedbackNotification = false,
            Domain.Entities.Lead? lead = null,
            string? userName = null)
        {
            try
            {
                // Implementation similar to original but without AWS Pinpoint dependency
                // This would use Firebase Cloud Functions or direct WhatsApp API calls
                await _whatsAppSenderService.SendWhatsAppNotificationAsync(entity, @event, notificationSettings, globalSettings, isFeedbackNotification, lead, userName);
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SendWhatsAppNotificationAsync");
            }
        }

        public async Task SendWhatsAppNotificationAsync<T>(
            T entity,
            List<Guid> waIds,
            NotificationSettings? notificationSettings,
            Domain.Entities.GlobalSettings? globalSettings,
            bool? IsFeedbackNotification = null,
            object? leadDto = null)
        {
            try
            {
                // Implementation similar to original but without AWS Pinpoint dependency
                await _whatsAppSenderService.SendWhatsAppNotificationAsync(entity, waIds, notificationSettings, globalSettings, IsFeedbackNotification, leadDto);
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SendWhatsAppNotificationAsync with waIds");
            }
        }

        public async Task<string> ScheduleWANotificationsAsync<T>(T entity, Guid userId, string mediaType, string message)
        {
            _logger.Information("FirebaseNotificationSenderService -> ScheduleWANotificationsAsync Called");

            Notification notification = new();
            var currentUserDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string> { userId.ToString() ?? string.Empty }, CancellationToken.None)).FirstOrDefault();
            PropertyInfo[] properties = typeof(T).GetProperties();
            var leadName = properties.FirstOrDefault(i => i.Name == "Name")?.GetValue(entity)?.ToString();
            var leadId = properties.FirstOrDefault(i => i.Name == "Id")?.GetValue(entity)?.ToString();

            switch (mediaType.ToLower().Trim())
            {
                case "image":
                    var imageContent = "New message received from #leadName#: 📷 Image";
                    notification.MessageBody = imageContent.Replace("#leadName#", leadName);
                    break;
                case "video":
                    var videoContent = "New message received from #leadName#: 🎥 Video";
                    notification.MessageBody = videoContent.Replace("#leadName#", leadName);
                    break;
                case "audio":
                    var audioContent = "New message received from #leadName#: 🎵 Audio";
                    notification.MessageBody = audioContent.Replace("#leadName#", leadName);
                    break;
                case "document":
                    var documentContent = "New message received from #leadName#: 📄 Document";
                    notification.MessageBody = documentContent.Replace("#leadName#", leadName);
                    break;
                default:
                    notification.MessageBody = $"New message received from {leadName}: {message}";
                    break;
            }

            notification.Title = "New WhatsApp Message";
            notification.UserId = userId;
            notification.IsScheduled = false;
            notification.ScheduledDate = DateTime.UtcNow;

            string jobId = string.Empty;
            try
            {
                var notificationDto = notification.Adapt<NotificationDTO>();
                notificationDto.CurrentUserId = _currentUser.GetUserId();
                notificationDto.UserIds = new() { userId };
                notificationDto.TenantInfoDto = new()
                {
                    Id = _tenantInfo.Id,
                    ConnectionString = _tenantInfo.ConnectionString,
                    Identifier = _tenantInfo.Identifier,
                    Name = _tenantInfo.Name
                };

                jobId = await _firebaseJobService.EnqueueAsync(() => _notificationService.SendNotificationAsync(notificationDto));
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ScheduleWANotificationsAsync");
            }

            return jobId;
        }

        public async Task SendTemplateNotificationAsync<T>(T entity, List<Guid>? eventIds, Domain.Enums.Event? @event = null, int? noOfEntities = null)
        {
            try
            {
                if (entity is Lead lead)
                {
                    var viewLeadDto = lead.Adapt<ViewLeadDto>();

                    if (viewLeadDto?.Status?.Status?.Equals("new", StringComparison.OrdinalIgnoreCase) == true)
                    {
                        eventIds ??= new List<Guid>();
                        eventIds.Add(viewLeadDto.Status.Id);
                    }
                }

                if (eventIds?.Any() != true)
                    return;

                var notificationConfigs = await _notificationConfig.ListAsync(new NotificationInfoByEventIdsSpec(eventIds));

                foreach (var notiConfig in notificationConfigs)
                {
                    var noti = notiConfig.Adapt<NotificationInfo>();
                    PropertyInfo[] properties = typeof(T).GetProperties();

                    if (DateTime.TryParse(properties.FirstOrDefault(i => i.Name == "ScheduledDate")?.GetValue(entity)?.ToString(), out DateTime scheduledDate) && noti.MinutesBefore?.Any() == true)
                    {
                        if (noti.MinutesBefore?.Any() == true)
                        {
                            foreach (var minute in notiConfig.MinutesBefore ?? new List<int>())
                            {
                                var scheduleDateTime = scheduledDate.ToLocalTime();
                                scheduleDateTime = scheduleDateTime.AddMinutes(-minute);

                                _logger.Information($"Scheduling notification at {scheduleDateTime} (UTC)");

                                try
                                {
                                    var jobId = await _firebaseJobService.ScheduleAsync(
                                        () => _templateNotificationService.ProcessTemplateNotificationAsync(
                                            noti,
                                            CancellationToken.None,
                                            null,
                                            null,
                                            null,
                                            null,
                                            noOfEntities ?? null
                                        ),
                                        scheduleDateTime
                                    );

                                    _logger.Information($"Scheduled notification with JobId: {jobId}");
                                }
                                catch (Exception ex)
                                {
                                    await LogErrorAsync(ex, $"SendTemplateNotificationAsync - Schedule minute {minute}");
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SendTemplateNotificationAsync");
            }
        }
        #endregion

        #region Private Helper Methods
        private async Task ProcessWebNotifications(Guid? userId, List<Guid>? userIds, NotificationDTO notificationDto, NotificationSettings? notificationSettings, bool isScheduled)
        {
            try
            {
                if (notificationSettings?.IsWebNotificationEnabled == true)
                {
                    if (userIds?.Any() == true)
                    {
                        var deviceInfos = await _dapperRepositoryRepo.GetDeviceInfo(_tenantInfo.Id ?? _currentUser?.GetTenant(), userIds);
                        foreach (var deviceInfo in deviceInfos)
                        {
                            if (!string.IsNullOrEmpty(deviceInfo.NewNotificationToken))
                            {
                                if (isScheduled)
                                {
                                    await _firebaseJobService.ScheduleAsync(() => _notificationService.SendWebNotificationAsync(notificationDto, deviceInfo.NewNotificationToken ?? string.Empty), notificationDto.ScheduledDate);
                                }
                                else
                                {
                                    await _firebaseJobService.EnqueueAsync(() => _notificationService.SendWebNotificationAsync(notificationDto, deviceInfo.NewNotificationToken ?? string.Empty));
                                }
                            }
                        }
                    }
                    else if (userId != Guid.Empty)
                    {
                        var deviceInfos = await _dapperRepositoryRepo.GetDeviceInfo(_tenantInfo.Id ?? _currentUser?.GetTenant(), userIds ?? new());
                        foreach (var deviceInfo in deviceInfos)
                        {
                            if (!string.IsNullOrEmpty(deviceInfo.NewNotificationToken))
                            {
                                if (isScheduled)
                                {
                                    await _firebaseJobService.ScheduleAsync(() => _notificationService.SendWebNotificationAsync(notificationDto, deviceInfo.NewNotificationToken ?? string.Empty), notificationDto.ScheduledDate);
                                }
                                else
                                {
                                    await _firebaseJobService.EnqueueAsync(() => _notificationService.SendWebNotificationAsync(notificationDto, deviceInfo.NewNotificationToken ?? string.Empty));
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "ProcessWebNotifications");
            }
        }

        private NotificationSettings GetNotificationSettings(Domain.Entities.GlobalSettings globalSettings)
        {
            return new NotificationSettings
            {
                IsEmailNotificationEnabled = globalSettings.IsEmailNotificationEnabled,
                IsWebNotificationEnabled = globalSettings.IsWebNotificationEnabled,
                IsPushNotificationEnabled = globalSettings.IsPushNotificationEnabled,
                IsSMSNotificationEnabled = globalSettings.IsSMSNotificationEnabled,
                IsWhatsAppNotificationEnabled = globalSettings.IsWhatsAppNotificationEnabled
            };
        }

        private async Task LogErrorAsync(Exception ex, string context)
        {
            try
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = $"FirebaseNotificationSenderService -> {context}"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                _logger.Error(ex, $"FirebaseNotificationSenderService -> {context}: {ex.Message}");
            }
            catch
            {
                // Fallback logging if repository fails
                _logger.Error(ex, $"FirebaseNotificationSenderService -> {context}: {ex.Message}");
            }
        }
        #endregion
    }
}
