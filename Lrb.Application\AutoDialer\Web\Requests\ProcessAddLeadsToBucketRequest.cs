﻿using Lrb.Application.AutoDialer.Web.Dtos;
using Lrb.Application.AutoDialer.Web.Specs;
using Lrb.Application.Common.IVR;
using Lrb.Application.UserDetails.Web;
using Lrb.Domain.Entities;
using Lrb.Shared.Extensions;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.DependencyInjection;
using System.Collections.Concurrent;

namespace Lrb.Application.AutoDialer.Web.Requests
{
    public class ProcessAddLeadsToBucketRequest : IRequest<Response<bool>>
    {
        public List<AutoDialerAuditDto>? LeadDetails { get; set; }
        public string? TenantId { get; set; }
        public Guid? CurrentUserId { get; set; }
        public class ProcessAddLeadsToBucketRequestHandler : IRequestHandler<ProcessAddLeadsToBucketRequest, Response<bool>>
        {
            private readonly ICurrentUser _currentUser;
            private readonly IRepositoryWithEvents<Domain.Entities.UserDetails> _userDetailsRepo;
            private readonly IRepositoryWithEvents<Domain.Entities.AutoDialerConfiguration> _autoDialerConfig;
            private readonly IRepositoryWithEvents<Domain.Entities.BulkCommonTracker> _bulkCommonTracker;
            private readonly IServiceProvider _serviceProvider;
            public ProcessAddLeadsToBucketRequestHandler(ICurrentUser currentUser,
                IRepositoryWithEvents<Domain.Entities.UserDetails> userDetailsRepo,IRepositoryWithEvents<Domain.Entities.AutoDialerConfiguration> autoDialerConfig,
                IRepositoryWithEvents<Domain.Entities.BulkCommonTracker> bulkCommonTracker, IServiceProvider serviceProvider)
            {
                _autoDialerConfig = autoDialerConfig;
                _serviceProvider = serviceProvider;
                _bulkCommonTracker = bulkCommonTracker;
                _currentUser = currentUser;
                _userDetailsRepo = userDetailsRepo;
            }

            public async Task<Response<bool>> Handle(ProcessAddLeadsToBucketRequest request,CancellationToken cancellationToken)
           {
                try
                {
                    if (!request.LeadDetails?.Any() ?? false)
                    {
                        return new(false,"No lead details found");
                    }
                    var userId = _currentUser.GetUserId();
                    var user = await _userDetailsRepo.FirstOrDefaultAsync(new GetUserDetailsByIdSpec(userId));
                    var config = await _autoDialerConfig.FirstOrDefaultAsync(new GetAutoDialerConfigSpec());
                    if (user == null || user.IsAutoDialerEnabled != true || config == null)
                    {
                        return new("AutoDialer is not enabled for this User");
                    }
                    BulkCommonTracker? tracker = null;

                    int leadsPerchunk = request.LeadDetails?.Count >= 50 ? 25 : request.LeadDetails?.Count ?? 0;
                    var currentUser = request.CurrentUserId ?? userId;
                    string tenantId = request.TenantId ?? _currentUser.GetTenant();
                    var commonTracker = new BulkCommonTracker();

                    if (request.LeadDetails != null && request.LeadDetails.Any() && request.LeadDetails.Count >= 50)
                    {
                        commonTracker = new BulkCommonTracker()
                        {
                            TotalCount = request.LeadDetails.Count(),
                            Status = UploadStatus.InProgress,
                            RawJson = request.Serialize(),
                            ClassType = $"AddLeadsToBucketRequest",
                            Module = "autodialer",
                            CreatedBy = currentUser,
                            LastModifiedBy = currentUser,
                        };
                        tracker = await _bulkCommonTracker.AddAsync(commonTracker);
                    }
                    request.CurrentUserId = request.CurrentUserId ?? currentUser;
                    request.TenantId = request.TenantId ?? _currentUser.GetTenant();
                    var chunks = request.LeadDetails.Chunk(leadsPerchunk).Select(i => new ConcurrentBag<AutoDialerAuditDto>(i));
                    List<Task> tasks = new();
                    var totalLeadCount = request.LeadDetails.Count;
                    Response<bool> response = new();
                    try
                    {
                        foreach (var chunk in chunks.ToList())
                        {
                            var newRequest = request.Adapt<AddLeadsToBucketRequest>();
                            newRequest.LeadDetails = chunk.ToList();
                            newRequest.TenantId = tenantId;
                            newRequest.CurrentUserId = userId;
                            var task = Task.Run(async () =>
                            {
                                using (var scope = _serviceProvider.CreateScope())
                                {
                                    var mediator = scope.ServiceProvider.GetService<IMediator>();
                                    response = await mediator.Send(newRequest);
                                }
                            });
                            tasks.Add(task);
                        }
                    }
                    catch (Exception ex)
                    {
                    }
                    int maxDegreeOfParallelism = 8;
                    using var semaphore = new SemaphoreSlim(maxDegreeOfParallelism);

                    var exceptions = new ConcurrentBag<Exception>();

                    var taskList = tasks.Select(async task =>
                    {
                        await semaphore.WaitAsync();
                        try
                        {
                            await task;
                        }
                        catch (Exception ex)
                        {
                            exceptions.Add(ex);
                        }
                        finally
                        {
                            semaphore.Release();
                        }
                    }).ToList();

                    await Task.WhenAll(taskList);
                    return response;
                }
                catch (Exception ex) 
                {
                    return new(false);
                }
            }
        }
    }

}
