﻿using Lrb.Application.ListingManagement.Web.Requests;

namespace Lrb.Application.ListingManagement.Web.Specs
{
    public class GetAllListingSiteTrackerSpecs : EntitiesByPaginationFilterSpec<ListingSiteTracker>
    {
        public GetAllListingSiteTrackerSpecs(GetAllListingSiteTrackerRequest filter, List<Guid> subIds) : base(filter) 
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        } 
    }

    public class GetAllListingSiteTrackerCountSpecs : Specification<ListingSiteTracker>
    {
        public GetAllListingSiteTrackerCountSpecs( List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }

    public class GetListingSiteTrackerByIdSpecs : Specification<ListingSiteTracker>
    {
        public GetListingSiteTrackerByIdSpecs(Guid id)
        {
            Query.Where(i => !i.IsDeleted && i.Id == id);
        }
    }
}
