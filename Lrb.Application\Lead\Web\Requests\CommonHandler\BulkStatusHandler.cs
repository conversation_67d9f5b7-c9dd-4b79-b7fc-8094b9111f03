﻿using DocumentFormat.OpenXml.Bibliography;
using DocumentFormat.OpenXml.Office2010.Excel;
using DocumentFormat.OpenXml.Spreadsheet;
using Lrb.Application.Agency.Web;
using Lrb.Application.Common.GooglePlaces;
using Lrb.Application.Common.Interfaces;
using Lrb.Application.Common.LeadRotation;
using Lrb.Application.Common.NotificationService;
using Lrb.Application.Common.Persistence;
using Lrb.Application.Common.Persistence.New_Implementation;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.ServiceBus;
using Lrb.Application.Common.SMS;
using Lrb.Application.DailyStatusUpdates.Dtos;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.LeadRotation.Web.Specs;
using Lrb.Application.Project.Web.Specs;
using Lrb.Application.Property.Mobile.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.Reports.Web;
using Lrb.Application.Team.Web;
using Lrb.Application.ZonewiseLocation.Web.Dtos;
using Lrb.Application.ZonewiseLocation.Web.Requests;
using Lrb.Application.ZonewiseLocation.Web.Specs;
using Lrb.Domain.Constants;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Lead.Web.Requests.CommonHandler
{
    public class BulkStatusHandler
    {
        private readonly IRepositoryWithEvents<BulkCommonTracker> _bulkCommonTracker;
        protected readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        protected readonly IUserService _userService;
        protected readonly ICurrentUser _currentUserRepo;
        private readonly string _className;
        private readonly string _methodName;
        protected readonly IRepositoryWithEvents<Lrb.Domain.Entities.Project> _projectRepo;
        protected readonly INotificationSenderService _notificationSenderService;
        protected readonly ILeadRepositoryAsync _leadRepositoryAsync;
        protected readonly IServiceProvider _serviceProvider;
        protected readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        protected readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        protected readonly IRepositoryWithEvents<LeadAssignment> _leadAssignmentRepo;
        protected readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Property> _propertyRepository;
        protected readonly IRepositoryWithEvents<Domain.Entities.Agency> _agencyRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.ChannelPartner> _cpRepository;
        protected readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalsettingRepo;
        protected readonly IMediator _mediator;
        protected readonly IRepositoryWithEvents<Domain.Entities.Team> _teamRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.LeadRotationTracker> _rotationTrackerRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.Location> _locationRepo;
        protected readonly IGooglePlacesService _googlePlacesService;
        protected readonly IRepositoryWithEvents<Address> _addressRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.LeadBookedDetail> _leadBookedDetailRepo;
        protected readonly IRepositoryWithEvents<UnitType> _unitType;
        protected readonly Serilog.ILogger _logger;
        protected readonly INpgsqlRepository _npgsqlRepo;
        protected readonly IServiceBus _serviceBus;
        protected readonly ILeadRotationService _leadRotationService;
        protected readonly IRepositoryWithEvents<LeadAppointment> _appointmentRepo;
        protected readonly IRepositoryWithEvents<Domain.Entities.LeadEnquiry> _leadEnquiryRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly Application.Common.PushNotification.INotificationService _notificationService;

        public BulkStatusHandler
        (
            IServiceProvider serviceProvider,
            string className,
            string methodName
        )
        {
            _serviceProvider = serviceProvider;
            _className = className;
            _methodName = methodName;
            _leadRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Lead>>();
            _userService = _serviceProvider.GetRequiredService<IUserService>();
            _notificationSenderService = _serviceProvider.GetRequiredService<INotificationSenderService>();
            _leadRepositoryAsync = _serviceProvider.GetRequiredService<ILeadRepositoryAsync>();
            _projectRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Project>>();
            _leadHistoryRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadHistory>>();
            _customLeadStatusRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<CustomMasterLeadStatus>>();
            _leadAssignmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadAssignment>>();
            _notificationSenderService = _serviceProvider.GetRequiredService<INotificationSenderService>();
            _currentUserRepo = _serviceProvider.GetRequiredService<ICurrentUser>();
            _propertyTypeRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<MasterPropertyType>>();
            _propertyRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Property>>();
            _agencyRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Agency>>();
            _cpRepository = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.ChannelPartner>>();
            _globalsettingRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.GlobalSettings>>();
            _teamRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Team>>();
            _mediator = _serviceProvider.GetRequiredService<IMediator>();
            _rotationTrackerRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.LeadRotationTracker>>();
            _locationRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.Location>>();
            _googlePlacesService = _serviceProvider.GetRequiredService<IGooglePlacesService>();
            _addressRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Address>>();
            _leadBookedDetailRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.LeadBookedDetail>>();
            _unitType = _serviceProvider.GetRequiredService<IRepositoryWithEvents<UnitType>>();
            _logger = _serviceProvider.GetRequiredService<Serilog.ILogger>();
            _npgsqlRepo = _serviceProvider.GetRequiredService<INpgsqlRepository>();
            _serviceBus = _serviceProvider.GetRequiredService<IServiceBus>();
            _leadRotationService = _serviceProvider.GetRequiredService<ILeadRotationService>();
            _appointmentRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<LeadAppointment>>();
            _leadEnquiryRepo = _serviceProvider.GetRequiredService<IRepositoryWithEvents<Domain.Entities.LeadEnquiry>>();
            _dapperRepository = _serviceProvider.GetRequiredService<IDapperRepository>();
            _notificationService = _serviceProvider.GetRequiredService<Common.PushNotification.INotificationService>();
        }

        protected async Task AddLrbErrorAsync(Exception ex, string moduleName)
        {
            try
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = moduleName
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            catch
            {
                throw;
            }
        }
        protected async Task<bool> ShouldUpdatePickedDate(Domain.Entities.Lead existingLead, PickedLeadDto pickedLeadDto, bool? isLeadFormUpdated = null,Guid? currentUser = null)
        {
            if (pickedLeadDto.AssignTo != null && pickedLeadDto.AssignTo != Guid.Empty && existingLead.AssignTo != pickedLeadDto.AssignTo)
            {
                existingLead.PickedDate = null;
                existingLead.IsPicked = false;
                return false;
            }
            bool? shouldUpdatePickedDate = null;
            var currentUserId = currentUser ?? _currentUserRepo.GetUserId();
            var originalLead = existingLead.CreateDeepCopy();
            if (originalLead != null &&
                pickedLeadDto != null &&
                (currentUserId != default && currentUserId != Guid.Empty) &&
                originalLead.AssignTo != default && (originalLead.AssignTo == currentUserId || originalLead.SecondaryUserId == currentUserId) &&
                originalLead.PickedDate == null)
            {
                shouldUpdatePickedDate = GetPickedDateAsync(originalLead,pickedLeadDto, isLeadFormUpdated: isLeadFormUpdated).Result;
            }
            return shouldUpdatePickedDate ?? false;
        }
        protected  async Task<bool?> GetPickedDateAsync(Domain.Entities.Lead lead, PickedLeadDto dto, bool? isLeadFormUpdated)
        {
            try
            {
                if (dto.ContactType != null)
                {
                    return true;
                }
                var leadProperties = typeof(PickedDateCriteria).GetFields().Select(i => i.GetValue(i)?.ToString()).OrderBy(p => p).ToList();
                foreach (var property in leadProperties)
                {
                    if (property != null)
                    {
                        var originalValue = lead.GetType()?.GetProperty(property)?.GetValue(lead, null);
                        var updatedValue = dto.GetType()?.GetProperty(property)?.GetValue(dto, null);
                        if (CompareValues(property, originalValue, updatedValue, dto, isLeadFormUpdated: isLeadFormUpdated, isAddressProperty: property.ToLower().Equals("address")) ?? false)
                        {
                            return true;
                        }
                    }
                }
                return null;
            }
            catch (Exception ex)
            {
                //throw;
                return null;
            }
        }
        protected bool? CompareValues(string property, object? originalValue, object? updatedValue, PickedLeadDto dto, bool? isAddressProperty = null, bool? isLeadFormUpdated = null)
        {
            bool? result = false;
            //if (originalValue == null && updatedValue != null && (!isAddressProperty ?? true))
            //{
            //    return true;
            //}
            //else if (originalValue != null && updatedValue == null)
            //{
            //    return false;
            //}
            //else if (originalValue == null && updatedValue == null)
            //{
            //    return false;
            //}
            switch (property)
            {
                case "Name":
                case "Email":
                case "Notes":
                case "ChosenProject":
                case "ChosenProperty":
                case "BookedUnderName":
                case "SoldPrice":
                case "Rating":
                case "AgencyName":
                case "CompanyName":
                case "PurchasedFrom":
                case "PreferredLocation":
                case "ReferralName":
                case "ConfidentialNotes":
                case "ChannelPartnerName":
                case "ChannelPartnerExecutiveName":
                case "Designation":
                    result = CompareStrings((string?)originalValue, (string?)updatedValue, isLeadFormUpdated);
                    break;
                case "ContactNo":
                case "AlternateContactNo":
                case "LandLine":
                case "ReferralContactNo":
                case "ReferralEmail":
                case "ChannelPartnerContactNo":
                    result = CompareContactNo((string?)originalValue, (string?)updatedValue, isLeadFormUpdated);
                    break;
                case "ScheduledDate":
                case "RevertDate":
                case "PostponedDate":
                    result = CompareDatetime((DateTime?)originalValue, (DateTime?)updatedValue, isLeadFormUpdated);
                    break;
                case "ShareCount":
                    result = CompareInt((int?)originalValue, (int?)updatedValue, isLeadFormUpdated);
                    break;
                case "CallRecordingUrls":
                    result = CompareDictionary((Dictionary<DateTime, string>?)originalValue, (Dictionary<DateTime, string>?)updatedValue, isLeadFormUpdated);
                    break;
                case "ContactRecords":
                    result = CompareDictionary((Dictionary<ContactType, int>?)originalValue, (Dictionary<ContactType, int>?)updatedValue, isLeadFormUpdated);
                    break;
                case "Documents":
                    result = CompareDocuments((List<LeadDocument>?)originalValue, (List<LeadDocument>?)updatedValue, isLeadFormUpdated);
                    break;
                case "TagInfo":
                    result = CompareLeadTags((LeadTag?)originalValue, dto, isLeadFormUpdated);
                    break;
                case "Status":
                    //var statusId = (Guid?)dto.GetType().GetProperty("CustomLeadStatusId")?.GetValue(dto, null);
                    result = CompareStatus((CustomMasterLeadStatus?)originalValue, (CustomMasterLeadStatus?)updatedValue, dto, isLeadFormUpdated);
                    break;
                case "AccountId":
                case "ClosingManager":
                case "SourcingManager":
                    result = CompareIds((Guid?)originalValue, (Guid?)updatedValue, isLeadFormUpdated);
                    break;
                case "Enquiries":
                    result = CompareEnquiries((List<LeadEnquiry>?)originalValue, dto, isLeadFormUpdated);
                    break;
                case "Address":
                    result = CompareAddress((Address?)originalValue, (AddressDto?)updatedValue, dto, isLeadFormUpdated);
                    break;
                case "Projects":
                    result = CompareProjects((List<Lrb.Domain.Entities.Project>?)originalValue, ((List<ProjectsDtoV2>?)updatedValue), dto, isLeadFormUpdated);
                    break;
                case "Appointments":
                    result = CompareAppointments((List<LeadAppointment>?)originalValue, ((List<LeadAppointmentDto>?)updatedValue), dto, isLeadFormUpdated);
                    break;
                case "Properties":
                    result = CompareProperties((List<Domain.Entities.Property>?)originalValue, ((List<Domain.Entities.Property>?)updatedValue), dto, isLeadFormUpdated);
                    break;
                case "UnmatchedBudget":
                    result = CompareLong((long?)originalValue, (long?)updatedValue, isLeadFormUpdated);
                    break;
                case "IsArchived":
                    result = !originalValue?.Equals(updatedValue);
                    break;
                case "LeadCallLogs":
                    result = CompareLeadCallLogs((List<Domain.Entities.LeadCallLog>?)originalValue, ((List<Domain.Entities.LeadCallLog>?)updatedValue), isLeadFormUpdated);
                    break;
                case "Profession":
                    result = CompareProfession((Profession?)originalValue, (Profession?)updatedValue, isLeadFormUpdated);
                    break;

                case "Communications":
                    result = CompareCommunications((List<LeadCommunication>?)originalValue, dto, isLeadFormUpdated);
                    break;
                case "ChannelPartners":
                    result = CompareChannelPartners((List<Domain.Entities.ChannelPartner>?)originalValue, dto, isLeadFormUpdated);
                    break;
                case "CustomLeadStatus":
                    result = CompareStatus((CustomMasterLeadStatus?)originalValue, (CustomMasterLeadStatus?)updatedValue, dto, isLeadFormUpdated);
                    break;
            }
            return result;
        }
        public bool? CompareProfession(Profession? originalValue, Profession? updatedValue, bool? isLeadFormUpdated = null)
        {
            if (isLeadFormUpdated ?? false)
            {
                return !originalValue?.Equals(updatedValue);
            }
            if (updatedValue != null)
            {
                return !originalValue?.Equals(updatedValue);
            }
            return false;
        }
        public bool? CompareLeadTags(LeadTag? originalLeadTag, PickedLeadDto? pickedLeadDto, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                var updatedLeadTag = pickedLeadDto?.LeadTags ?? new();
                if (originalLeadTag?.IsHighlighted != updatedLeadTag.IsHighlighted ||
                    originalLeadTag?.IsEscalated != updatedLeadTag.IsEscalated ||
                    originalLeadTag?.IsAboutToConvert != updatedLeadTag.IsAboutToConvert ||
                    originalLeadTag?.IsHotLead != updatedLeadTag.IsHotLead ||
                    originalLeadTag?.IsWarmLead != updatedLeadTag.IsWarmLead ||
                    originalLeadTag?.IsColdLead != updatedLeadTag.IsColdLead
                    )
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else if (pickedLeadDto?.LeadTags != null)
            {
                var updatedLeadTag = pickedLeadDto?.LeadTags ?? new();
                if (originalLeadTag?.IsHighlighted != updatedLeadTag.IsHighlighted ||
                    originalLeadTag?.IsEscalated != updatedLeadTag.IsEscalated ||
                    originalLeadTag?.IsAboutToConvert != updatedLeadTag.IsAboutToConvert ||
                    originalLeadTag?.IsHotLead != updatedLeadTag.IsHotLead ||
                    originalLeadTag?.IsWarmLead != updatedLeadTag.IsWarmLead ||
                    originalLeadTag?.IsColdLead != updatedLeadTag.IsColdLead
                    )
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            return false;

        }
        public bool? CompareCommunications(List<LeadCommunication>? leadCommunications, PickedLeadDto? pickedLeadDto, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                var communicationDtos = pickedLeadDto?.Communications?.ToList() ?? new();
                return (leadCommunications?.Count ?? 0) != (communicationDtos?.Count ?? 0);
            }
            if (pickedLeadDto?.Communications?.Any() ?? false)
            {
                var communicationDtos = pickedLeadDto?.Communications?.ToList() ?? new();
                return (leadCommunications?.Count ?? 0) != (communicationDtos?.Count ?? 0);
            }
            return false;
        }
        public bool? CompareChannelPartners(List<Domain.Entities.ChannelPartner>? channelPartners, PickedLeadDto? pickedLeadDto, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                var existingChannelPartners = channelPartners?.Select(i => i.FirmName).ToList().Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                var updatedChannelPartners = pickedLeadDto?.ChannelPartners?.Select(i => i.FirmName).ToList().Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                return (updatedChannelPartners?.Any(i => !existingChannelPartners?.Contains(i) ?? false) ?? false);
            }
            if (pickedLeadDto?.ChannelPartners?.Any() ?? false)
            {
                var existingChannelPartners = channelPartners?.Select(i => i.FirmName).ToList().Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                var updatedChannelPartners = pickedLeadDto?.ChannelPartners?.Select(i => i.FirmName).ToList().Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                return (updatedChannelPartners?.Any(i => !existingChannelPartners?.Contains(i) ?? false) ?? false);
            }
            return false;
        }
        public bool? CompareLeadCallLogs(List<Domain.Entities.LeadCallLog>? originalValue, List<Domain.Entities.LeadCallLog>? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (!originalValue?.Any() ?? true)
            {
                if (updatedValue?.Any() ?? false)
                {
                    return true;
                }
                else
                {
                    return false;
                }
            }
            else if ((originalValue?.Any() ?? false) && (updatedValue?.Any() ?? false))
            {
                if (originalValue.Count() != updatedValue.Count())
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        public bool? CompareLong(long? originalValue, long? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                if (updatedValue == null && originalValue == null)
                {
                    return false;
                }
                if ((originalValue == null || originalValue.Value == default) && updatedValue != null && updatedValue.Value != default)
                {
                    return true;
                }
                if (originalValue != updatedValue)
                {
                    return true;
                }
                return false;
            }
            else if (updatedValue != null)
            {
                if ((originalValue == null || originalValue.Value == default) && updatedValue != null && updatedValue.Value != default)
                {
                    return true;
                }
                if (originalValue != updatedValue)
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        public bool? CompareProperties(List<Domain.Entities.Property>? originalProperties, List<Domain.Entities.Property>? updatedProperties, PickedLeadDto dto, bool? IsLeadFormUpdated = null)
        {
            if (dto.PropertiesList?.Any() ?? false)
            {
                List<string>? existingPropertyNames = originalProperties?.Select(i => i.Title?.ToLower() ?? string.Empty).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                List<string>? updatedPropertyNames = dto.PropertiesList?.Select(i => i?.ToLower() ?? string.Empty).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                if ((!existingPropertyNames?.Any() ?? true) && (updatedPropertyNames?.Any() ?? false))
                {
                    return true;
                }
                else if ((existingPropertyNames?.Any() ?? false) && (updatedPropertyNames?.Any() ?? false))
                {
                    return updatedPropertyNames.Any(i => !existingPropertyNames.Contains(i));
                }
                return false;
            }
            if (updatedProperties?.Any() ?? false)
            {
                var originalPropertyIds = originalProperties?.Select(i => i.Id).ToList();
                if (originalPropertyIds?.Any() ?? false)
                {
                    return updatedProperties.Any(i => !originalPropertyIds.Contains(i.Id));
                }
                return false;
            }
            return false;
        }
        public bool? CompareAppointments(List<LeadAppointment>? originalAppointments, List<LeadAppointmentDto>? updatedAppointments, PickedLeadDto pickedLeadDto, bool? IsLeadFormUpdated = null)
        {
            if ((!originalAppointments?.Any() ?? true) && (!updatedAppointments?.Any() ?? true))
            {
                return false;
            }
            else if (updatedAppointments?.Any(i => i.Type != AppointmentType.None) ?? false)
            {
                return true;
            }
            return false;
        }
        //public static bool? CompareTags()
        public bool? CompareProjects(List<Lrb.Domain.Entities.Project>? originalValue, List<ProjectsDtoV2>? updatedValue, PickedLeadDto dto, bool? IsLeadFormUpdated = null)
        {
            if (dto.ProjectsList?.Any() ?? false)
            {
                List<string>? existingProjectNames = originalValue?.Select(i => i.Name?.ToLower() ?? string.Empty).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                List<string>? updatedProjectNames = dto.ProjectsList?.Select(i => i.ToLower() ?? string.Empty).Where(i => !string.IsNullOrWhiteSpace(i)).ToList();
                if ((updatedProjectNames?.Any() ?? false) && (!existingProjectNames?.Any() ?? true))
                {
                    return true;
                }
                else if ((existingProjectNames?.Any() ?? false) && (updatedProjectNames?.Any() ?? false))
                {
                    return (updatedProjectNames.Any(i => !existingProjectNames.Contains(i)));
                }
                return false;
            }
            if (updatedValue?.Any() ?? false)
            {
                var originalProjectNames = originalValue?.Select(i => i.Name?.ToLower().Trim()).ToList();
                if (originalProjectNames?.Any() ?? false)
                {
                    return updatedValue.Any(i => !originalProjectNames.Contains(i.Name?.ToLower().Trim()));
                }
                else if ((!originalProjectNames?.Any() ?? true) && (updatedValue.Any(i => !string.IsNullOrWhiteSpace(i.Name))))
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        public bool? CompareAddress(Address? originalValue, AddressDto? updatedValue, PickedLeadDto dto, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                var updatedString = updatedValue?.ToString().Trim().ToLower() ?? string.Empty;
                var originalString = originalValue?.ToString().Trim().ToLower() ?? string.Empty;
                if (string.IsNullOrWhiteSpace(originalString) && !string.IsNullOrWhiteSpace(updatedString))
                {
                    return true;
                }
                if (!string.IsNullOrWhiteSpace(originalString) && !string.IsNullOrWhiteSpace(updatedString))
                {
                    return !updatedString.Equals(originalString);
                }
                return false;
            }
            else if (updatedValue != null)
            {
                var updatedString = updatedValue?.ToString().Trim().ToLower() ?? string.Empty;
                var originalString = originalValue?.ToString().Trim().ToLower() ?? string.Empty;
                if (string.IsNullOrWhiteSpace(originalString) && !string.IsNullOrWhiteSpace(updatedString))
                {
                    return true;
                }
                if (!string.IsNullOrWhiteSpace(originalString) && !string.IsNullOrWhiteSpace(updatedString))
                {
                    return !updatedString.Equals(originalString);
                }
                return false;
            }
            return false;
        }
        public bool? CompareEnquiries(List<LeadEnquiry>? originalValue, PickedLeadDto dto, bool? IsLeadFormUpdated = null)
        {
            //var statusId = (Guid?)dto.GetType().GetProperty("CustomLeadStatusId")?.GetValue(dto, null);
            if (IsLeadFormUpdated ?? false)
            {
                var enquiry = originalValue?.FirstOrDefault(i => !i.IsDeleted && i.IsPrimary);
                var enquiryDto = dto.Enquiry;
                bool? result;
                result = ((enquiryDto != null && enquiry != null) &&
                    ((enquiryDto.LeadSource != enquiry.LeadSource) ||
                    ((enquiryDto.SubSource != null && enquiry.SubSource != null) && !(enquiryDto.SubSource.Equals(enquiry.SubSource))) ||
                    (enquiryDto.BHKType != enquiry.BHKType) ||
                    (enquiryDto.BHKTypes != enquiry.BHKTypes) ||
                    ((enquiryDto.SaleType != null && enquiry.SaleType != null) && (enquiryDto.SaleType != enquiry.SaleType)) ||
                    (enquiryDto.Area != enquiry.Area) ||
                    (enquiryDto.AreaUnitId != enquiry.AreaUnitId) ||
                    ((enquiryDto.CarpetArea ?? 0) != (enquiry.CarpetArea ?? 0)) ||
                    (enquiryDto.CarpetAreaUnitId != enquiry.CarpetAreaUnitId) ||
                    (enquiryDto.EnquiredFor != enquiry.EnquiredFor) ||
                    (enquiryDto.EnquiryTypes != enquiry.EnquiryTypes) ||
                    ((enquiryDto.LowerBudget ?? 0) != (enquiry.LowerBudget ?? 0)) ||
                    ((enquiryDto.UpperBudget ?? 0) != (enquiry.UpperBudget ?? 0)) ||
                    ((enquiryDto.NoOfBHK ?? 0) != enquiry.NoOfBHKs) ||
                    ((enquiryDto.BHKs) != enquiry.BHKs) ||
                    ((enquiryDto.PossessionDate != null && enquiry.PossessionDate != null) && (enquiryDto.PossessionDate != enquiry.PossessionDate)) ||
                    ((enquiry.PropertyType != null) && enquiryDto.PropertyTypeId != enquiry.PropertyType.Id) ||
                    ((enquiryDto.SaleType != null && enquiry.SaleType != null) && (enquiryDto.SaleType != enquiry.SaleType))
                    ));
                if (result ?? false)
                {
                    return result;
                }
                //var updatedString = enquiryDto?.Address?.ToString().Trim().ToLower() ?? string.Empty;
                //var originalString = enquiry?.Address?.ToString().Trim().ToLower() ?? string.Empty;
                var updatedString = string.Join(", ", enquiryDto?.Addresses?.Select(address => address.ToString().Trim().ToLower()) ?? Enumerable.Empty<string>());
                var originalString = string.Join(", ", enquiry?.Addresses?.Select(address => address.ToString().Trim().ToLower()) ?? Enumerable.Empty<string>());
                if (string.IsNullOrWhiteSpace(originalString) && !string.IsNullOrWhiteSpace(updatedString))
                {
                    return true;
                }
                if (!string.IsNullOrWhiteSpace(originalString) && !string.IsNullOrWhiteSpace(updatedString))
                {
                    return !updatedString.Equals(originalString);
                }
                return result;
            }
            else if (dto.Enquiry != null)
            {
                var enquiry = originalValue?.FirstOrDefault(i => !i.IsDeleted && i.IsPrimary);
                var enquiryDto = dto.Enquiry;
                bool? result;
                result = ((enquiryDto != null && enquiry != null) &&
                    ((enquiryDto.LeadSource != enquiry.LeadSource) ||
                    ((enquiryDto.SubSource != null && enquiry.SubSource != null) && !(enquiryDto.SubSource.Equals(enquiry.SubSource))) ||
                    (enquiryDto.BHKType != enquiry.BHKType) ||
                    (enquiryDto.BHKTypes != enquiry.BHKTypes) ||
                    ((enquiryDto.SaleType != null && enquiry.SaleType != null) && (enquiryDto.SaleType != enquiry.SaleType)) ||
                    (enquiryDto.Area != enquiry.Area) ||
                    (enquiryDto.AreaUnitId != enquiry.AreaUnitId) ||
                    ((enquiryDto.CarpetArea ?? 0) != (enquiry.CarpetArea ?? 0)) ||
                    (enquiryDto.CarpetAreaUnitId != enquiry.CarpetAreaUnitId) ||
                    (enquiryDto.EnquiredFor != enquiry.EnquiredFor) ||
                    (enquiryDto.EnquiryTypes != enquiry.EnquiryTypes) ||
                    ((enquiryDto.LowerBudget ?? 0) != (enquiry.LowerBudget ?? 0)) ||
                    ((enquiryDto.UpperBudget ?? 0) != (enquiry.UpperBudget ?? 0)) ||
                    ((enquiryDto.NoOfBHK ?? 0) != enquiry.NoOfBHKs) ||
                     ((enquiryDto.BHKs) != enquiry.BHKs) ||
                    ((enquiryDto.PossessionDate != null && enquiry.PossessionDate != null) && (enquiryDto.PossessionDate != enquiry.PossessionDate)) ||
                    ((enquiry.PropertyType != null) && enquiryDto.PropertyTypeId != enquiry.PropertyType.Id) ||
                    ((enquiryDto.SaleType != null && enquiry.SaleType != null) && (enquiryDto.SaleType != enquiry.SaleType))
                    ));
                if (result ?? false)
                {
                    return result;
                }
                //var updatedString = enquiryDto?.Address?.ToString().Trim().ToLower() ?? string.Empty;
                //var originalString = enquiry?.Address?.ToString().Trim().ToLower() ?? string.Empty;
                var updatedString = string.Join(", ", enquiryDto?.Addresses?.Select(address => address.ToString().Trim().ToLower()) ?? Enumerable.Empty<string>());
                var originalString = string.Join(", ", enquiry?.Addresses?.Select(address => address.ToString().Trim().ToLower()) ?? Enumerable.Empty<string>());
                if (string.IsNullOrWhiteSpace(originalString) && !string.IsNullOrWhiteSpace(updatedString))
                {
                    return true;
                }
                if (!string.IsNullOrWhiteSpace(originalString) && !string.IsNullOrWhiteSpace(updatedString))
                {
                    return !updatedString.Equals(originalString);
                }
                return result;
            }
            return false;
        }
        public bool? CompareIds(Guid? originalValue, Guid? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                return originalValue != updatedValue;
            }
            if (updatedValue != null)
            {
                return originalValue != updatedValue;
            }
            return false;
        }
        public bool? CompareStatus(CustomMasterLeadStatus? originalValue, CustomMasterLeadStatus? updatedValue, PickedLeadDto? dto, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                if (originalValue?.Id != null && dto?.LeadStatusId != null && dto?.LeadStatusId != originalValue?.Id)
                {
                    return true;
                }
                return false;
            }
            if (dto?.LeadStatusId != null)
            {
                if (originalValue?.Id != null && dto?.LeadStatusId != null && dto?.LeadStatusId != originalValue?.Id)
                {
                    return true;
                }
                return false;
            }
            return false;
        }
        public bool? CompareDocuments(List<LeadDocument>? originalValue, List<LeadDocument>? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                if (originalValue?.Count != updatedValue?.Count)
                {
                    return true;
                }
                if ((!originalValue?.Any() ?? true) && (updatedValue?.Any() ?? false))
                {
                    return true;
                }
                var res = updatedValue?.Any(i => !(originalValue?.Select(i => i.Id) ?? new List<Guid>()).Contains(i.Id));
                return res;
            }
            else if (updatedValue != null)
            {
                if (originalValue?.Count != updatedValue?.Count)
                {
                    return true;
                }
                if ((!originalValue?.Any() ?? true) && (updatedValue?.Any() ?? false))
                {
                    return true;
                }
                var res = updatedValue?.Any(i => !(originalValue?.Select(i => i.Id) ?? new List<Guid>()).Contains(i.Id));
                return res;
            }
            return false;
        }
        public bool? CompareStrings(string? originalValue, string? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                return originalValue?.Trim().ToLower() != updatedValue?.Trim().ToLower();
            }
            else if (!string.IsNullOrWhiteSpace(updatedValue))
            {
                return originalValue?.Trim().ToLower() != updatedValue?.Trim().ToLower();
            }
            return false;
        }
        public bool? CompareContactNo(string? originalValue, string? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                if (originalValue?.Length >= 10 && updatedValue?.Length >= 10)
                {
                    return originalValue?.Trim()[^10..] != updatedValue?.Trim()[^10..];
                }
                return originalValue?.Trim().ToLower() != updatedValue?.Trim().ToLower();
            }
            else if (!string.IsNullOrWhiteSpace(updatedValue))
            {
                if (originalValue?.Length >= 10 && updatedValue?.Length >= 10)
                {
                    return originalValue?.Trim()[^10..] != updatedValue?.Trim()[^10..];
                }
                return originalValue?.Trim().ToLower() != updatedValue?.Trim().ToLower();
            }
            return false;
        }
        public bool? CompareDatetime(DateTime? originalValue, DateTime? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                return originalValue != updatedValue;
            }
            if (updatedValue != null)
            {
                return originalValue != updatedValue;
            }
            return false;
        }
        public bool? CompareInt(int? originalValue, int? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (IsLeadFormUpdated ?? false)
            {
                return originalValue != updatedValue;
            }
            if (updatedValue != null)
            {
                return originalValue != updatedValue;
            }
            return false;
        }
        public bool? CompareDictionary(Dictionary<DateTime, string>? originalValue, Dictionary<DateTime, string>? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (!IsLeadFormUpdated ?? false)
            {
                return !originalValue?.SequenceEqual(updatedValue ?? new());
            }
            else if (updatedValue != null)
            {
                return !originalValue?.SequenceEqual(updatedValue);
            }
            return false;
        }
        public bool? CompareDictionary(Dictionary<ContactType, int>? originalValue, Dictionary<ContactType, int>? updatedValue, bool? IsLeadFormUpdated = null)
        {
            if (!IsLeadFormUpdated ?? false)
            {
                return !originalValue?.SequenceEqual(updatedValue ?? new());
            }
            else if (updatedValue != null)
            {
                return !originalValue?.SequenceEqual(updatedValue);
            }
            return false;
        }

        public async Task<Guid> GetAutoRetentionAssignmentId(Domain.Entities.Lead lead, Guid statusId)
        {
            Guid newAssignmentId = Guid.Empty;
            var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamByLeadStatusIdSpecs(statusId));
            var currentWeek = DateTime.UtcNow.DayOfWeek;
            var teamconfig = team?.Configurations?.FirstOrDefault(i => i.IsForRetention == true);
            if ((DateTime.UtcNow.TimeOfDay > teamconfig?.EndTime)) return newAssignmentId;
            if (team != null && (team?.Configurations?.FirstOrDefault(i => i.IsForRetention == true)?.DayOfWeeks?.Contains(currentWeek) ?? false) && (team.UserIds?.Any() ?? false))
            {
                List<string>? groupUserIds = (team.UserIds.Select(i => i.ToString())).ToList();

                var userIds = await _userService.GetListOfUsersByIdsAsync(groupUserIds, default);

                var activeUser = userIds.Where(i => i.IsActive).Select(i => i.Id).ToList();

                var tracker = await _rotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerForRetentionSpecs(lead.Id));

                if (tracker != null && (tracker.AssignedUsers?.Any() ?? false))
                {
                    var firstAssignedUser = tracker.AssignedUsers?.OrderBy(i => i.Key).Select(i => i.Value).FirstOrDefault();

                    var notAssignedUser = activeUser.IndexOf(firstAssignedUser ?? Guid.Empty);
                    if (notAssignedUser < (activeUser.Count - 1))
                    {
                        var assignTo = activeUser[notAssignedUser + 1];
                        newAssignmentId = assignTo;
                    }
                    else
                    {
                        var assignTo = activeUser[0];
                        newAssignmentId = assignTo;
                    }
                }
                else
                {
                    if (activeUser.Any(i => i == lead.AssignTo))
                    {
                        newAssignmentId = lead.AssignTo;
                    }
                    else
                    {
                        var assignTo = activeUser.FirstOrDefault();
                        newAssignmentId = assignTo;
                    }
                }
                team.Configurations.FirstOrDefault(i => i.IsForRetention == true).LastAssignedUser = newAssignmentId;
                try
                {
                    await _teamRepo.UpdateAsync(team);
                }
                catch(Exception e) { }
            }
            return newAssignmentId;
        }
        protected async Task SetLeadStatusAsync(Domain.Entities.Lead lead, Guid statusId, CancellationToken cancellationToken = default,List<CustomMasterLeadStatus>? allStatuses = null)
        {
            try
            {
                if (statusId == default)
                {
                    throw new ArgumentException("The Status Id is not valid");
                }
                var childStatuses = allStatuses?.Where(i => i.BaseId == statusId).ToList();
                if (childStatuses?.Any() ?? false)
                {
                    throw new ArgumentException("Please provide child status id.");
                }
                var leadStatus = allStatuses?.FirstOrDefault(i => i.Id == statusId);

                if (leadStatus == null)
                {
                    throw new ArgumentException("The Status Id is not valid.");
                }
                lead.CustomLeadStatus = leadStatus;
                //lead.Status = await _leadStatusRepo.FirstOrDefaultAsync(new GetMasterLeadStatusByIdSpec(leadStatus.MasterLeadStatusBaseId ?? Guid.Empty), cancellationToken);
                if (leadStatus != null && leadStatus.BaseId == Guid.Parse("023fcb89-037e-42f4-bcc2-647bb4e95c99"))
                {
                    lead.ScheduledDate = null;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetLeadAppointmentAsync(Domain.Entities.Lead lead, LeadAppointment appointment, AppointmentType appointmentType, List<LeadDocument>? documentsToAdd = null, CancellationToken cancellationToken = default,Guid? currentUser = null)
        {
            try
            {
                var address = appointment.Location != null ? await CreateAddressAsync(appointment.Location.Adapt<AddressDto>(), cancellationToken) : null;


                if (appointmentType != AppointmentType.None)
                {
                    appointment.Id = Guid.Empty;
                    appointment.UserId = lead.AssignTo;
                    switch (appointmentType)
                    {
                        case AppointmentType.Meeting:
                            lead.IsMeetingDone = appointment.IsDone;
                            appointment.Type = AppointmentType.Meeting;
                            break;
                        case AppointmentType.SiteVisit:
                            lead.IsSiteVisitDone = appointment.IsDone;
                            appointment.Type = AppointmentType.SiteVisit;
                            break;
                        default:
                            break;
                    }
                    if (address != null)
                    {
                        lead.MeetingLocation = address.Id;
                        appointment.Location = address;
                    }
                    if (documentsToAdd != null && documentsToAdd.Any())
                    {
                        var docType = appointmentType == AppointmentType.Meeting ? Domain.Enums.LeadDocumentType.Meeting : appointmentType == AppointmentType.SiteVisit ? Domain.Enums.LeadDocumentType.SiteVisit : Domain.Enums.LeadDocumentType.None;
                        var documents = await CreateTrueLeadDocsAsync(documentsToAdd, docType, currentUser);
                        appointment.ImagesWithName = documents;
                    }
                    if (lead.Appointments?.Any() ?? false)
                    {
                        lead.Appointments.Add(appointment);
                    }
                    else
                    {
                        lead.Appointments = new List<LeadAppointment>() { appointment };
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<Address?> CreateAddressAsync(AddressDto? addressDto, CancellationToken cancellationToken = default)
        {
            try
            {
                Address? address = null;
                if (addressDto?.LocationId != null && addressDto?.LocationId != Guid.Empty)
                {
                    address = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { addressDto?.LocationId ?? Guid.Empty }), cancellationToken);
                    if (address == null)
                    {
                        var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addressDto?.LocationId ?? Guid.Empty), cancellationToken);
                        if (location != null)
                        {
                            address = MapToAddress(location);
                            if (address != null)
                            {
                                address.Id = Guid.Empty;
                                address = await _addressRepo.AddAsync(address);
                            }
                        }
                    }
                }
                else if (!string.IsNullOrWhiteSpace(addressDto?.PlaceId) && address == null)
                {
                    address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(addressDto.PlaceId), cancellationToken)).FirstOrDefault();
                    if (address == null)
                    {
                        try
                        {
                            address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(addressDto.PlaceId))?.Adapt<Address>() ?? null;
                        }
                        catch (Exception ex)
                        {
                            await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                        }
                        if (address != null)
                        {
                            address = await _addressRepo.AddAsync(address);
                            await MapAddressToLocationAndSaveAsync(address);
                        }
                    }
                }
                else if (double.TryParse(addressDto?.Longitude ?? "0", out double lng) && lng > 0 && double.TryParse(addressDto?.Latitude ?? "0", out double lat) && lat > 0)
                {
                    var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
                    var place = places.FirstOrDefault();
                    if (place != null && place.PlaceId != null)
                    {
                        address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                        if (address == null)
                        {
                            try
                            {
                                address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(place.PlaceId))?.Adapt<Address>() ?? null;
                            }
                            catch (Exception ex)
                            {
                                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                            }
                            if (address != null)
                            {
                                address = await _addressRepo.AddAsync(address);
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                    }
                    else if (place != null)
                    {
                        address = place.Adapt<Address>();
                        address = await _addressRepo.AddAsync(address);
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                else if (address == null && (addressDto?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                {
                    //if (newAddress != null)
                    //{
                    //    address = await _addressRepo.AddAsync(newAddress ?? new());
                    //    await MapAddressToLocationAndSaveAsync(address);
                    //}
                    if (newAddress != null)
                    {
                        var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                        if (existingAddress == null)
                        {
                            address = await _addressRepo.AddAsync(newAddress);
                        }
                        else
                        {
                            address = existingAddress;
                        }
                        await MapAddressToLocationAndSaveAsync(address);
                    }
                }
                return address;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }

        }
        protected async Task<List<Address>?> CreateAddressesAsync(List<AddressDto>? addressDtos, CancellationToken cancellationToken = default)
        {
            try
            {
                List<Address?> addresses = new List<Address?>();
                if (addressDtos != null)
                {
                    foreach (var addressDto in addressDtos)
                    {
                        Address? address = null;

                        if (addressDto?.LocationId != null && addressDto?.LocationId != Guid.Empty)
                        {
                            address = await _addressRepo.FirstOrDefaultAsync(new AddressByLocationIdSpec(new() { addressDto?.LocationId ?? Guid.Empty }), cancellationToken);
                            if (address == null)
                            {
                                var location = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(addressDto?.LocationId ?? Guid.Empty), cancellationToken);
                                if (location != null)
                                {
                                    address = location.MapToAddress();
                                    if (address != null)
                                    {
                                        address.Id = Guid.Empty;
                                        address = await _addressRepo.AddAsync(address);
                                    }
                                }
                            }
                        }
                        else if (!string.IsNullOrWhiteSpace(addressDto?.PlaceId) && address == null)
                        {
                            address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(addressDto.PlaceId), cancellationToken)).FirstOrDefault();
                            if (address == null)
                            {
                                try
                                {
                                    address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(addressDto.PlaceId))?.Adapt<Address>() ?? null;
                                }
                                catch (Exception ex)
                                {
                                    await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                                }
                                if (address != null)
                                {
                                    address = await _addressRepo.AddAsync(address);
                                    await MapAddressToLocationAndSaveAsync(address);
                                }
                            }
                        }
                        else if (double.TryParse(addressDto?.Longitude ?? "0", out double lng) && lng > 0 && double.TryParse(addressDto?.Latitude ?? "0", out double lat) && lat > 0)
                        {
                            var places = await _googlePlacesService.GetPlaceDetailsByCoordinatesAsync(lat, lng);
                            var place = places.FirstOrDefault();
                            if (place != null && place.PlaceId != null)
                            {
                                address = (await _addressRepo.ListAsync(new GetAddressByPlaceIdSpec(place.PlaceId), cancellationToken)).FirstOrDefault();
                                if (address == null)
                                {
                                    try
                                    {
                                        address = (await _googlePlacesService.GetPlaceDetailsByPlaceIdAsync(place.PlaceId))?.Adapt<Address>() ?? null;
                                    }
                                    catch (Exception ex)
                                    {
                                        await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                                    }
                                    if (address != null)
                                    {
                                        address = await _addressRepo.AddAsync(address);
                                        await MapAddressToLocationAndSaveAsync(address);
                                    }
                                }
                            }
                            else if (place != null)
                            {
                                address = place.Adapt<Address>();
                                address = await _addressRepo.AddAsync(address);
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                        else if (address == null && (addressDto?.Adapt<Address>()?.ValidateAddress(out Address? newAddress) ?? false))
                        {
                            if (newAddress != null)
                            {
                                var existingAddress = await _addressRepo.GetByIdAsync(newAddress.Id);
                                if (existingAddress == null)
                                {
                                    address = await _addressRepo.AddAsync(newAddress);
                                }
                                else
                                {
                                    address = existingAddress;
                                }
                                await MapAddressToLocationAndSaveAsync(address);
                            }
                        }
                        if (address != null)
                        {
                            addresses.Add(address);
                        }
                    }
                }
                return addresses;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        private async Task MapAddressToLocationAndSaveAsync(Address address)
        {
            try
            {
                var location =  MapToLocationRequest(address);
                if (location != null)
                {
                    var locationRes = await _mediator.Send(location.Adapt<AddLocationRequest>());
                    var createdLocation = await _locationRepo.FirstOrDefaultAsync(new LocationByIdSpec(locationRes.Data), default);
                    address.Location = createdLocation;
                    if (address.Location != null)
                    {
                        await _addressRepo.UpdateAsync(address);
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        public Address MapToAddress(Domain.Entities.Location location)
        {
            var newAddress = new Address()
            {
                SubLocality = location.Locality,
                District = location.District,
                City = location?.City?.Name,
                State = location.State?.Name,
                Country = location.Country?.Name,
                Longitude = location.Longitude,
                Latitude = location.Latitude,
                PostalCode = location.PostalCode,
                IsGoogleMapLocation = location.IsGoogleMapLocation,
                PlaceId = location.PlaceId,
                Location = location,
                LocationId = location.Id
            };
            return newAddress;
        }
        public CreateLocationDto? MapToLocationRequest(Address address)
        {
            return address?.SubLocality != null || address?.Locality != null ?
             new()
             {
                 Locality = address.SubLocality ?? address.Locality ?? string.Empty,
                 City = address.City ?? string.Empty,
                 State = address.State ?? string.Empty,
                 Country = address.Country ?? string.Empty,
                 PostalCode = address.PostalCode ?? string.Empty,
                 Longitude = address.Longitude ?? string.Empty,
                 Latitude = address.Latitude ?? string.Empty,
                 IsGoogleMapLocation = address.IsGoogleMapLocation,
                 District = address.District ?? string.Empty,
                 PlaceId = address.PlaceId ?? string.Empty,
             } : null;
        }
        private async Task<List<LeadDocument>> CreateTrueLeadDocsAsync(List<LeadDocument> documentsToAdd, Domain.Enums.LeadDocumentType documentType,Guid? currentUser = null)
        {
            try
            {
                var currentUserId = currentUser ?? _currentUserRepo.GetUserId();
                List<LeadDocument> documents = new();
                foreach (var document in documentsToAdd)
                {
                    document.Id = Guid.NewGuid();
                    document.DocumentName = document.DocumentName;
                    document.UploadedOn = DateTime.UtcNow;
                    document.CreatedBy = currentUserId;
                    document.LastModifiedBy = currentUserId;
                    document.CreatedOn = DateTime.UtcNow;
                    document.LastModifiedOn = DateTime.UtcNow;
                    document.LeadDocumentType = documentType;
                    documents.Add(document);
                }
                return documents;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetBookedDetailsAsync(Domain.Entities.Lead lead, UpdateBulkLeadStatusDto request, CancellationToken cancellationToken = default)
        {
            var userName = await _userService.GetAsync(lead.BookedBy.ToString() ?? string.Empty, cancellationToken);
            var bookedDetailInfo = await _leadBookedDetailRepo.FirstOrDefaultAsync(new GetBookedDetailsByIdSpec(lead.Id), cancellationToken);
            if (bookedDetailInfo != null)
            {
                try
                {
                    bookedDetailInfo.BookedDate = lead?.BookedDate;
                    bookedDetailInfo.BookedBy = lead?.BookedBy;
                    bookedDetailInfo.BookedByUser = userName?.FirstName ?? string.Empty + " " + userName?.LastName ?? string.Empty;
                    bookedDetailInfo.BookedUnderName = lead?.BookedUnderName;
                    bookedDetailInfo.UserId = lead?.AssignTo ?? Guid.Empty;
                    bookedDetailInfo.SoldPrice = request?.SoldPrice;
                    bookedDetailInfo.Notes = request?.Notes;
                    bookedDetailInfo.ProjectsList = request?.ProjectsList;
                    bookedDetailInfo.PropertiesList = request?.PropertiesList;
                    bookedDetailInfo.AgreementValue = request?.AgreementValue ?? default;
                    bookedDetailInfo.Properties = await _propertyRepository.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                    bookedDetailInfo.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                    bookedDetailInfo.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                    bookedDetailInfo.Currency = request?.Currency ?? default;
                    bookedDetailInfo.UnitType = await _unitType.GetBySpecAsync(new GetUnitInfoSpecs(request?.UnitTypeId ?? Guid.Empty)) ?? null;
                    bookedDetailInfo.IsBookingCompleted = request?.IsBookingCompleted ?? bookedDetailInfo.IsBookingCompleted;
                    bookedDetailInfo.LastModifiedBy = request?.CurrentUserId ?? bookedDetailInfo.LastModifiedBy;
                    await _leadBookedDetailRepo.UpdateAsync(bookedDetailInfo);
                    //if (request?.AssignTo != null)
                    //{
                    //    lead.AssignTo = request?.AssignTo ?? lead.AssignTo;
                    //    await _leadRepo.UpdateAsync(lead);
                    //}
                }
                catch (Exception ex)
                {
                    await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                    throw;
                }

            }
            else
            {
                if (lead != null)
                {
                    try
                    {
                        LeadBookedDetail bookedDetail = new();
                        bookedDetail.LeadId = lead.Id;
                        bookedDetail.BookedDate = lead?.BookedDate;
                        bookedDetail.BookedBy = lead?.BookedBy;
                        bookedDetail.BookedByUser = userName?.FirstName ?? string.Empty + " " + userName?.LastName ?? string.Empty;
                        bookedDetail.BookedUnderName = lead?.BookedUnderName;
                        bookedDetail.UserId = lead?.AssignTo ?? Guid.Empty;
                        bookedDetail.SoldPrice = request?.SoldPrice;
                        bookedDetail.Notes = request?.Notes;
                        bookedDetail.ProjectsList = request?.ProjectsList;
                        bookedDetail.PropertiesList = request?.PropertiesList;
                        bookedDetail.AgreementValue = request?.AgreementValue ?? default;
                        bookedDetail.Properties = await _propertyRepository.ListAsync(new GetPropertiesByIdspecs(request?.PropertyIds ?? new()));
                        bookedDetail.Projects = await _projectRepo.ListAsync(new ProjectByIdsSpec(request?.ProjectIds ?? new()), cancellationToken);
                        bookedDetail.IsChoosenProperty = request?.IsChoosenProperty ?? false;
                        bookedDetail.Currency = request?.Currency ?? default;
                        bookedDetail.UnitType = await _unitType.GetBySpecAsync(new GetUnitInfoSpecs(request?.UnitTypeId ?? Guid.Empty)) ?? null;
                        bookedDetail.IsBookingCompleted = request?.IsBookingCompleted ?? false;
                        bookedDetail.LastModifiedBy = request?.CurrentUserId ?? bookedDetail.LastModifiedBy;
                        lead?.BookedDetails?.Add(bookedDetail);
                        await _leadBookedDetailRepo.AddAsync(bookedDetail);
                        //if (request?.AssignTo != null)
                        //{
                        //    lead.AssignTo = request?.AssignTo ?? lead.AssignTo;
                        //    await _leadRepo.UpdateAsync(lead);
                        //}

                    }


                    catch (Exception ex)
                    {
                        await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                        throw;
                    }
                }
            }
        }
        protected async Task SetLeadPropertiesAsync(Domain.Entities.Lead lead, List<string>? propertyList, CancellationToken cancellationToken = default, Domain.Entities.GlobalSettings? globalSettings = null, Guid? currentUser = null)
        {
            try
            {
                List<Domain.Entities.Property>? properties = new();
               // Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                propertyList = (propertyList?.Any() ?? false) ? propertyList.Where(i => !string.IsNullOrWhiteSpace(i))?.ToList()?.ConvertAll(i => i.ToLower().Trim()) : null;
                if (propertyList?.Any() ?? false)
                {
                    foreach (var newProperty in propertyList)
                    {
                        var existingProperty = await _propertyRepository.FirstOrDefaultAsync(new GetPropertyByTitleSpec(newProperty), cancellationToken);
                        if (existingProperty != null)
                        {
                            var existingLeadProperty = (lead?.Properties?.FirstOrDefault(i => i.Id == existingProperty.Id));
                            if ((existingLeadProperty == null) && (lead?.Properties?.Any() ?? false))
                            {
                                lead?.Properties?.Add(existingProperty);
                            }
                            else if(existingLeadProperty == null)
                            {
                                 properties.Add(existingProperty);
                            }
                        }
                        else
                        {
                            Domain.Entities.Property property = new()
                            {
                                Title = newProperty,
                                MonetaryInfo = new PropertyMonetaryInfo
                                {
                                    Currency = globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                },
                                CreatedBy = currentUser ?? _currentUserRepo.GetUserId(),
                                LastModifiedBy = currentUser ?? _currentUserRepo.GetUserId()
                            };
                            property = await _propertyRepository.AddAsync(property, cancellationToken);
                            if (lead?.Properties?.Any() ?? false)
                            {
                                lead?.Properties?.Add(property);
                            }
                            else
                            {

                              properties.Add(property); 
                            }
                        }
                    }
                    if (properties?.Any() ?? false)
                    {
                        lead.Properties = properties;
                    }
                }
                else if ((lead?.Properties?.Any() ?? false) && propertyList == null)
                {
                    lead.Properties = properties;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task SetLeadProjectsAsync(Domain.Entities.Lead lead, List<string>? projectList, CancellationToken cancellationToken = default, Domain.Entities.GlobalSettings? globalSettings = null,Guid? currentUser = null)
        {
            try
            {
                List<Lrb.Domain.Entities.Project>? projects = new();
               // Domain.Entities.GlobalSettings? globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                projectList = (projectList?.Any() ?? false) ? projectList.Where(i => !string.IsNullOrWhiteSpace(i)).ToList() : null;
                if (projectList?.Any() ?? false)
                {
                    foreach (var newProject in projectList)
                    {
                        Lrb.Domain.Entities.Project? existingProject = (await _projectRepo.FirstOrDefaultAsync(new GetNewProjectsByIdV2Spec(newProject), cancellationToken));
                        if (existingProject != null)
                        {
                            var existingLeadProject = (lead?.Projects?.FirstOrDefault(i => i.Id == existingProject.Id));
                            if (existingLeadProject == null && (lead?.Projects?.Any() ?? false))
                            {
                                lead?.Projects?.Add(existingProject);
                            }
                            else if(existingLeadProject == null)
                            {
                                projects.Add(existingProject);
                            }
                        }
                        else
                        {
                            Lrb.Domain.Entities.Project project = new()
                            {
                                Name = newProject,
                                MonetaryInfo = new ProjectMonetaryInfo
                                {
                                    Currency = globalSettings?.Countries?.FirstOrDefault()?.DefaultCurrency ?? "INR"
                                },
                                CreatedBy = currentUser ?? _currentUserRepo.GetUserId(),
                                LastModifiedBy = currentUser ?? _currentUserRepo.GetUserId()
                            };
                            project = await _projectRepo.AddAsync(project, cancellationToken);
                            if (lead?.Projects?.Any() ?? false)
                            {
                                lead?.Projects?.Add(project);
                            }
                            else
                            {
                                projects.Add(project);
                            }
                        }
                    }
                    if (projects?.Any() ?? false)
                    {
                        lead.Projects = projects;
                    }
                }
                else if ((lead?.Projects?.Any() ?? false) && projectList == null)
                {
                    lead.Projects = projects;
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<(Domain.Entities.LeadHistory, Domain.Entities.LeadHistory)> UpdateLeadHistoryAsync(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool isLeadUpdateRequest = false)
        {
            Domain.Entities.LeadHistory? existingHistory = null;
            Domain.Entities.LeadHistory? newHistory = null;
            try
            {
                var userId = currentUserId ?? _currentUserRepo.GetUserId();
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken,currentUserId:currentUserId);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = userId;
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest);
                            existingHistory = history;
                        }
                        else
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest);
                            existingHistory = history;
                        }
                    }
                    else
                    {
                        newHistory = leadHistory;
                       // await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest);
                            existingHistory = history;
                            //await _leadHistoryRepo.UpdateAsync(, cancellationToken);
                        }
                        else
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest);
                            existingHistory = history;
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory);
                            existingHistory = history;
                        }

                        else
                        {
                            newHistory = leadHistory;
                        }
                    }
                }
                return new(existingHistory, newHistory);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<(Domain.Entities.LeadHistory, Domain.Entities.LeadHistory)> UpdateLeadHistoryV1Async(Domain.Entities.Lead lead, ViewLeadDto? leadDto = null, AppointmentType? appointmentType = null, CancellationToken cancellationToken = default, bool? shouldUpdateContactRecord = null, Guid? currentUserId = null, bool isLeadUpdateRequest = false)
        {
            Domain.Entities.LeadHistory? oldLeadHistory = null;
            Domain.Entities.LeadHistory? newLeadHistory = null;
            try
            {
                var userId = currentUserId ?? _currentUserRepo.GetUserId();
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken, currentUserId: currentUserId);
                var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto, appointmentType, shouldUpdateContactRecord: shouldUpdateContactRecord);
                if (userId != Guid.Empty && lead.AssignTo == Guid.Empty && userId != lead.AssignTo)
                {
                    leadHistory.UserId = userId;
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, userId));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest);
                            oldLeadHistory = history;
                        }
                        else
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest);
                            oldLeadHistory = history;
                        }
                    }
                    else
                    {
                        newLeadHistory = leadHistory;
                        // await _leadHistoryRepo.AddAsync(leadHistory, cancellationToken);
                    }
                }
                else
                {
                    var existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignTo));
                    if (existingLeadHistory != null)
                    {
                        if (appointmentType != null)
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, appointmentType, shouldUpdateNotes: isLeadUpdateRequest);
                            oldLeadHistory = history;
                            //await _leadHistoryRepo.UpdateAsync(, cancellationToken);
                        }
                        else
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory, shouldUpdateNotes: isLeadUpdateRequest);
                            oldLeadHistory = history;
                        }
                    }
                    else
                    {
                        existingLeadHistory = await _leadHistoryRepo.FirstOrDefaultAsync(new LeadHistorySpec(lead.Id, lead.AssignedFrom ?? Guid.Empty));
                        if (existingLeadHistory != null)
                        {
                            var history = LeadHistoryHelper.GetUpdatedLeadHistory(existingLeadHistory, leadHistory);
                            oldLeadHistory = history;
                        }

                        else
                        {
                            newLeadHistory = leadHistory;
                        }
                    }
                }
                return new(oldLeadHistory, newLeadHistory);
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<ViewLeadDto> GetFullLeadDtoAsync(Domain.Entities.Lead fullLead, CancellationToken cancellationToken = default,Guid? currentUserId = null)
        {
            try
            {
                //var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("Lead not found by the provided id.");
                var leadDto = fullLead.Adapt<ViewLeadDto>();
                if (leadDto.Address != null)
                {
                    leadDto.Address.Id = Guid.NewGuid();
                }
                if (leadDto.ChannelPartners?.Any() ?? false)
                {
                    leadDto.ChannelPartners.ForEach(cp => cp.Id = Guid.NewGuid());
                }
                await SetUsersInViewLeadDtoAsync(leadDto,_userService, cancellationToken,currentUserId:currentUserId);
                return leadDto;
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        public  async Task<ViewLeadDto?> SetUsersInViewLeadDtoAsync( ViewLeadDto? leadDto, IUserService _userService, CancellationToken cancellationToken, Guid? currentUserId = null, LeadSource? source = null)
        {
            if (leadDto == null) { return null; }
            var userIds = new List<string>()
            {
                leadDto?.AssignTo?.ToString() ?? string.Empty,
                leadDto?.SecondaryUserId?.ToString() ?? string.Empty,
                leadDto?.LastModifiedBy.ToString() ?? string.Empty,
                leadDto?.AssignedFrom.ToString() ?? string.Empty,
                leadDto?.SourcingManager?.ToString() ?? string.Empty,
                leadDto?.ClosingManager?.ToString() ?? string.Empty,
                leadDto?.BookedBy?.ToString() ?? string.Empty,
                currentUserId?.ToString() ?? string.Empty,
                leadDto?.SecondaryFromUserId?.ToString() ?? string.Empty,
            };
            var users = await _userService.GetListOfUsersByIdsAsync(userIds?.Distinct()?.ToList() ?? new List<string>(), cancellationToken);
            UserDetailsDto? assignedUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignTo ?? Guid.Empty));
            //try
            //{
            //    assignedUser = await _userService.GetAsync(leadDto?.AssignTo?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (assignedUser != null)
            {
                leadDto.AssignedUser = assignedUser?.Adapt<UserDto>();
                leadDto.AssignedUser.Name = assignedUser?.FirstName + " " + assignedUser?.LastName;

            }
            UserDetailsDto? secondaryUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryUserId ?? Guid.Empty));
            //try
            //{
            //    secondaryUser = await _userService.GetAsync(leadDto?.SecondaryUserId?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (secondaryUser != null)
            {
                leadDto.SecondaryUser = secondaryUser?.Adapt<UserDto>();
                leadDto.SecondaryUser.Name = secondaryUser?.FirstName + " " + secondaryUser?.LastName;

            }
            UserDetailsDto? lastModifiedByUser = users?.FirstOrDefault(i => i.Id == (currentUserId ?? leadDto?.LastModifiedBy));
            //try
            //{
            //    lastModifiedByUser = await _userService.GetAsync(leadDto?.LastModifiedBy.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (lastModifiedByUser != null)
            {
                leadDto.LastModifiedByUser = lastModifiedByUser?.Adapt<UserDto>();
                leadDto.LastModifiedByUser.Name = lastModifiedByUser?.FirstName + " " + lastModifiedByUser?.LastName;
            }
            else if (source.ToString() != null)
            {
                leadDto.LastModifiedByUser = new UserDto();
                leadDto.LastModifiedByUser.Name = source.ToString();
            }

            UserDetailsDto? secondaryFromUser = users?.FirstOrDefault(i => i.Id == (leadDto?.SecondaryFromUserId ?? Guid.Empty));
            if (secondaryFromUser != null)
            {
                leadDto.SecondaryFromUser = secondaryFromUser?.Adapt<UserDto>();
                leadDto.SecondaryFromUser.Name = secondaryFromUser?.FirstName + " " + secondaryFromUser?.LastName;
            }

            UserDetailsDto? assignedFromUser = users?.FirstOrDefault(i => i.Id == (leadDto?.AssignedFrom ?? Guid.Empty));
            //try
            //{
            //    assignedFromUser = await _userService.GetAsync(leadDto?.AssignedFrom.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (assignedFromUser != null)
            {
                leadDto.AssignedFromUser = assignedFromUser?.Adapt<UserDto>();
                leadDto.AssignedFromUser.Name = assignedFromUser?.FirstName + " " + assignedFromUser?.LastName;
            }
            UserDetailsDto? sourcingManager = users?.FirstOrDefault(i => i.Id == (leadDto?.SourcingManager ?? Guid.Empty));
            //try
            //{
            //    sourcingManager = await _userService.GetAsync(leadDto?.SourcingManager?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (sourcingManager != null)
            {
                leadDto.SourcingManagerUser = sourcingManager?.Adapt<UserDto>();
                leadDto.SourcingManagerUser.Name = sourcingManager?.FirstName + " " + sourcingManager?.LastName;
            }
            UserDetailsDto? closingManager = users?.FirstOrDefault(i => i.Id == (leadDto?.ClosingManager ?? Guid.Empty));
            //try
            //{
            //    closingManager = await _userService.GetAsync(leadDto?.ClosingManager?.ToString() ?? string.Empty, cancellationToken);
            //}
            //catch (NotFoundException e) { }
            if (closingManager != null)
            {
                leadDto.ClosingManagerUser = closingManager?.Adapt<UserDto>();
                leadDto.ClosingManagerUser.Name = closingManager?.FirstName + " " + closingManager?.LastName;
            }
            UserDetailsDto? bookedByUser = users?.FirstOrDefault(i => i.Id == (leadDto?.BookedBy ?? Guid.Empty));
            //try
            //{
            //    bookedByUser = await _userService.GetAsync(leadDto?.BookedBy?.ToString() ?? string.Empty, cancellationToken);

            //}
            //catch (NotFoundException e) { }
            if (bookedByUser != null)
            {
                leadDto.BookedByUser = bookedByUser?.Adapt<UserDto>();
                leadDto.BookedByUser.Name = bookedByUser?.FirstName + " " + bookedByUser?.LastName;
            }
            return leadDto;
        }
        protected async Task SendLeadAppointmentNotificationAsync(Domain.Entities.Lead lead, AppointmentType appointmentType, bool isDone, CancellationToken cancellationToken = default,Guid? currentUser = null,Domain.Entities.GlobalSettings? globalSettings = null,ViewLeadDto? leadDto = null,List<UserDetailsDto>? allUsersDetails = null, List<NotificationContent>? contents = null)
        {
            try
            {
                //var globalSettings = await _globalsettingRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                //var fullLead = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(lead.Id), cancellationToken) ?? throw new NotFoundException("Lead not found by the provided id.");
                leadDto ??= await GetFullLeadDtoAsync(lead, cancellationToken);

                try
                {
                    await SendLeadStatusChangeNotificationsAsync(lead, leadDto, _notificationSenderService,_notificationService ,globalSettings, currentUser ?? _currentUserRepo.GetUserId(), allUserDetails: allUsersDetails,contents:contents);
                }
                catch
                {

                }
                Domain.Enums.Event? @event = null;
                if (appointmentType != AppointmentType.None)
                {
                    switch (appointmentType)
                    {
                        case AppointmentType.SiteVisit:
                            switch (isDone)
                            {
                                case true:
                                    @event = Domain.Enums.Event.LeadSiteVisitDone;
                                    break;
                                case false:
                                    @event = Domain.Enums.Event.LeadSiteVisitNotDone;
                                    break;
                            }
                            break;
                        case AppointmentType.Meeting:
                            switch (isDone)
                            {
                                case true:
                                    @event = Domain.Enums.Event.LeadMeetingDone;
                                    break;
                                case false:
                                    @event = Domain.Enums.Event.LeadMeetingNotDone;
                                    break;
                            }
                            break;
                    }
                    if (@event != null)
                    {
                        Domain.Enums.Event updatedEvent = (Domain.Enums.Event)@event;
                        try
                        {
                            if (lead.AssignTo != Guid.Empty)
                            {
                                var userDetails = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                                if (userDetails.Id != (currentUser ?? _currentUserRepo.GetUserId()))
                                {
                                    await _notificationSenderService.ScheduleNotificationsAsync(updatedEvent, lead, userDetails.Id, userDetails.FirstName + " " + userDetails.LastName);
                                }
                            }
                        }
                        catch
                        {

                        }

                        try
                        {
                            await _notificationSenderService.SendWhatsAppNotificationAsync(lead, updatedEvent, null, globalSettings ?? new(), true);
                        }
                        catch (Exception ex)
                        {
                            _logger.Information($"SendLeadAppointmentNotificationAsync -> Error While Sending WhatsApp Notification! {ex}");
                        }


                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        public static async Task<bool> SendLeadStatusChangeNotificationsAsync(Domain.Entities.Lead lead, ViewLeadDto leadDto, INotificationSenderService _notificationSenderService, Application.Common.PushNotification.INotificationService _notificationService, Domain.Entities.GlobalSettings? globalSettings, Guid currentUserId = default, List<UserDetailsDto>? allUserDetails = null, List<NotificationContent>? contents = null)
        {
            try
            {
                bool isSent = false;

                switch (leadDto.Status?.Status)
                {
                    case "callback":
                        var response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                        {
                            List<string> callbackStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadStatusToCallback, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() },currentUserId:currentUserId, globalSettings:globalSettings,allUserDetails:allUserDetails,contents:contents);
                        }
                        List<string> callbackStatusEvent2jobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.CallbackReminder, lead, leadDto?.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        isSent = true;
                        break;
                    case "dropped":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (currentUserId != leadDto.AssignTo)
                        {
                            List<string> dropStatusEventJobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadSatusToDropped, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        }
                        isSent = true;
                        break;
                    case "booked":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (currentUserId != leadDto.AssignTo)
                        {
                            List<string> bookedStatusEventJobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadStatusToBook, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        }
                        isSent = true;
                        break;
                    case "not_interested":
                        response = await _notificationSenderService.DeleteScheduledNotificationsAsync(lead);
                        if (currentUserId != leadDto.AssignTo)
                        {
                            List<string> notInterestedStatusEventJobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadStatusToNotInterest, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        }
                        isSent = true;
                        break;
                    case "site_visit_scheduled":
                        if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                        {
                            List<string> siteVisitScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadStatusToScheduleSiteVisit, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        }
                        List<string> siteVisitScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.ScheduleSiteVisitReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        isSent = true;
                        break;
                    case "meeting_scheduled":
                        if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                        {
                            List<string> meetingScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadStatusToScheduleMeeting, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        }
                        List<string> meetingScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.ScheduleMeetingReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                        isSent = true;
                        break;
                    default:
                        break;
                }
                if ((leadDto?.Status?.ShouldUseForMeeting == true) && (!isSent))
                {
                    if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                    {
                        List<string> meetingScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadStatusToScheduleMeeting, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                    }
                    List<string> meetingScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.ScheduleMeetingReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                }
                else if ((leadDto?.Status?.ShouldUseForMeeting == false) && (!isSent))
                {
                    if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                    {
                        List<string> siteVisitScheduledStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadStatusToScheduleSiteVisit, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                    }
                    List<string> siteVisitScheduledStatusEvent2JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.ScheduleSiteVisitReminder, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                }
                else if ((leadDto?.Status?.IsScheduled ?? false) && (!isSent))
                {
                    if (leadDto.AssignTo != Guid.Empty && leadDto.AssignTo != currentUserId)
                    {
                        List<string> callbackStatusEvent1JobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadStatusToCallback, lead, leadDto.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                    }
                    List<string> callbackStatusEvent2jobIds = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.CallbackReminder, lead, leadDto?.AssignTo, leadDto?.AssignedUser?.Name ?? string.Empty, topics: new List<string> { lead.AssignTo.ToString(), lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, currentUserId: currentUserId, globalSettings: globalSettings, allUserDetails: allUserDetails, contents: contents);
                }
                else if ((leadDto?.Status?.WhatsAppTemplateInfoIds?.Any() ?? false))
                {
                    await _notificationSenderService.SendWhatsAppNotificationAsync(lead, leadDto?.Status?.WhatsAppTemplateInfoIds ?? new(), null, globalSettings, leadDto: leadDto);
                }
                else if ((leadDto?.Status?.ChildType?.WhatsAppTemplateInfoIds?.Any() ?? false))
                {
                    await _notificationSenderService.SendWhatsAppNotificationAsync(lead, leadDto?.Status?.ChildType?.WhatsAppTemplateInfoIds ?? new(), null, globalSettings, leadDto: leadDto);
                }
                else
                {
                    var eventIds = new List<Guid>
                    {
                        leadDto?.Status?.Id ?? Guid.Empty,
                        leadDto?.Status?.ChildType?.Id ?? Guid.Empty
                    };

                    await _notificationSenderService.SendTemplateNotificationAsync(lead, eventIds, null);
                    if (globalSettings?.IsEngageToEnabled ?? false)
                    {
                        await _notificationService.SendLeadUpdateToEngageto(lead, CancellationToken.None);
                    }
                    
                }

                return true;
            }
            catch { return false; }
        }
        protected async Task SendLeadAssignmentNotificationsAsync(Domain.Entities.Lead lead, int leadCount, CancellationToken cancellationToken = default,string? tenantId = null, Guid? currentUser = null,List<Guid>? adminIds = null, UserDetailsDto? assignedUser = null, List<UserDetailsDto>? allUsersDetails = null, List<NotificationContent>? contents = null,Domain.Entities.GlobalSettings? globalSetting = null)
        {
            try
            {
                if (lead != null && lead.AssignTo == Guid.Empty)
                {
                    adminIds = adminIds?.Where(i => (currentUser != null && i != currentUser))?.ToList() ?? (await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? _currentUserRepo.GetTenant() ?? string.Empty)).Where(i => i != (currentUser ?? _currentUserRepo.GetUserId())).ToList();
                    if (adminIds?.Any() ?? false)
                    {
                        //foreach (var adminId in adminIds)
                        //{
                        //    //var adminDetails = await _userService.GetAsync(adminId.ToString(), cancellationToken);
                        //    var adminDetails = (await _userService.GetListOfUsersByIdsAsync(new List<string>() { adminId.ToString() }, cancellationToken)).FirstOrDefault();
                        //    if (adminDetails != null)
                        //    {
                        //        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadMovedToUnassigned, lead, adminId, null, noOfEntities: leadCount);
                        //    }
                        //}
                        List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadMovedToUnassigned, lead, null, noOfEntities: leadCount, userIds: adminIds,currentUserId:currentUser,contents:contents);
                    }
                }
                else
                {
                    //var assignedUser = await _userService.GetAsync(lead.AssignTo.ToString() ?? string.Empty, cancellationToken);
                    assignedUser??= (await _userService.GetListOfUsersByIdsAsync(new List<string>() { lead?.AssignTo.ToString() ?? string.Empty }, cancellationToken)).FirstOrDefault();
                    if (assignedUser != null && (currentUser ?? _currentUserRepo.GetUserId()) != assignedUser.Id)
                    {
                        //if (leadCount > 1)
                        //{
                        //    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.MultipleLeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, null,leadCount, currentUserId: currentUser, allUserDetails: allUsersDetails, contents:contents);

                        //}
                        //else
                        //{
                        //    List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.LeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName,currentUserId:currentUser,allUserDetails:allUsersDetails, contents: contents);
                        //}
                        var userInfo = (await _dapperRepository.GetUserDetailsAsync(
                            tenantId ?? string.Empty,
                            new List<Guid> { assignedUser.Id }
                        ))?.FirstOrDefault();
                        var notificationSetting = JsonConvert.DeserializeObject<NotificationSettings>(globalSetting?.NotificationSettings ?? string.Empty) ?? new();
                        if (leadCount > 1)
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, null, leadCount, globalSettings: globalSetting, currentUserId: currentUser, allUserDetails: allUsersDetails, contents: contents);
                            if ((notificationSetting != null) && notificationSetting.IsAdminEnabled && (adminIds?.Any() ?? false))
                            {
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignmentNotificationForAll, lead, null, noOfEntities: leadCount, userIds: adminIds, globalSettings: globalSetting, currentUserId: currentUser, allUserDetails: allUsersDetails, contents: contents);
                            }
                            if ((notificationSetting != null) && notificationSetting.IsManagerEnabled && (userInfo?.ReportsTo != null) && (userInfo.ReportsTo.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.ReportsTo.Id == i)) && (userInfo.ReportsTo.Id != currentUser))
                            {
                                List<Guid> userIds = new() { userInfo.ReportsTo.Id };
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignmentNotificationForAll, lead, null, noOfEntities: leadCount, userIds: userIds, globalSettings: globalSetting, currentUserId: currentUser, allUserDetails: allUsersDetails, contents: contents);
                            }
                            if ((notificationSetting != null) && notificationSetting.IsGeneralManagerEnabled  && (userInfo?.GeneralManager != null) && (userInfo.GeneralManager.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo?.GeneralManager?.Id == i)) && (userInfo.GeneralManager.Id != currentUser))
                            {
                                List<Guid> userIds = new() { userInfo.GeneralManager.Id };
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Event.MultipleLeadAssignmentNotificationForAll, lead, null, noOfEntities: leadCount, userIds: userIds, globalSettings: globalSetting, currentUserId: currentUser, allUserDetails: allUsersDetails, contents: contents);
                            }
                        }
                        else
                        {
                            List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadAssignment, lead, lead.AssignTo, assignedUser.FirstName + " " + assignedUser.LastName, globalSettings: globalSetting, currentUserId: currentUser, allUserDetails: allUsersDetails, contents: contents);
                            if ((notificationSetting != null) && notificationSetting.IsAdminEnabled && (adminIds?.Any() ?? false))
                            {
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, assignedUser.FirstName + " " + assignedUser.LastName, globalSettings: globalSetting, userIds: adminIds, allUserDetails: allUsersDetails, contents: contents);
                            }
                            if ((notificationSetting != null) && notificationSetting.IsManagerEnabled && ((userInfo?.ReportsTo != null) && (userInfo.ReportsTo.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.ReportsTo.Id == i)) && (userInfo?.ReportsTo.Id != currentUser)))
                            {
                                List<Guid> userIds = new() { userInfo.ReportsTo.Id };
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, assignedUser.FirstName + " " + assignedUser.LastName, globalSettings: globalSetting, userIds: userIds, allUserDetails: allUsersDetails, contents: contents);
                            }
                            if ((notificationSetting != null) && notificationSetting.IsGeneralManagerEnabled && (userInfo?.GeneralManager != null) && (userInfo.GeneralManager.Id != Guid.Empty) && !(adminIds != null && adminIds.Any(i => userInfo.GeneralManager.Id == i)) && (userInfo.GeneralManager.Id != currentUser))
                            {
                                List<Guid> userIds = new() { userInfo.GeneralManager.Id };
                                List<string> notificationSchduleResponses = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, assignedUser.FirstName + " " + assignedUser.LastName, globalSettings: globalSetting, userIds: userIds, allUserDetails: allUsersDetails, contents: contents);

                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        // Dreamyard and kroft are the only tenants that currently use this feature
        protected async Task AutoReassignmentHandler(Domain.Entities.Lead lead, CancellationToken cancellationToken, string? tenantId = null)
        {
            var tenant = tenantId ?? _currentUserRepo.GetTenant();
            List<Guid> leadIds = new() { lead.Id };

            if (tenant == "dreamyard" || tenant == "kroft" || tenant == "custom")  
            {
                var team = await _teamRepo.FirstOrDefaultAsync(new GetTeamByLeadStatusId(lead?.CustomLeadStatus?.Id ?? Guid.Empty), cancellationToken);
                if (team != null)
                {
                    var tracker = await _rotationTrackerRepo.FirstOrDefaultAsync(new GetLeadRotationTrackerSpecs(lead?.Id ?? Guid.Empty), cancellationToken);
                    if (tracker == null)
                    {
                        #region Add Lead Rotation Tracker
                        var newTracker = new LeadRotationTracker();
                        newTracker.LeadId = lead?.Id;
                        newTracker.NoOfRotation = team.NoOfReassignment;
                        await _rotationTrackerRepo.AddAsync(newTracker);
                        #endregion
                    }

                    AutoReassignmentForDreamyardandKroft entity = new()
                    {
                        LeadIds = leadIds
                    };
                    InputPayloadV2 payload = new(tenant, entity);
                    //var messageBody = JsonConvert.SerializeObject(payload, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented });
                    await _serviceBus.RunLeadRotationHttpTriggerJobAsync(payload);
                }
            }
        }

        public async Task ScheduleLeadRetentionRotation(List<Domain.Entities.Lead> leads, CancellationToken cancellationToken)
        {
                await _leadRotationService.ScheduleTeamRetentionRotation(leads);
        }
        protected async Task UpdateLeadSourceInfoAsync(LeadEnquiry leadEnquiry, CancellationToken cancellationToken = default,List<SourceDto>? allSources = null)
        {
            try
            {
                if (!string.IsNullOrEmpty(leadEnquiry.SubSource) &&
                leadEnquiry.LeadSource != LeadSource.Facebook &&
                leadEnquiry.LeadSource != LeadSource.GoogleAds &&
                leadEnquiry.LeadSource != LeadSource.Gmail)
                {
                    var sourceDtos = allSources?.Where(i => i.SubSource != null &&
                    i.LeadSource != LeadSource.Facebook && i.LeadSource != LeadSource.GoogleAds && i.LeadSource != LeadSource.Gmail && i.LeadSource == leadEnquiry.LeadSource).ToList();
                    if (sourceDtos != null && !sourceDtos.Any(i => i.SubSource?.ToLower()?.Trim()?.Contains(leadEnquiry.SubSource?.ToLower()?.Trim() ?? "Invalid") ?? false))
                    {
                        try
                        {
                            await _mediator.Send(new CreateIntegrationRequest()
                            {
                                AccountName = leadEnquiry.SubSource,
                                Source = leadEnquiry.LeadSource
                            });
                        }
                        catch (Exception ex)
                        {
                            await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                await AddLrbErrorAsync(ex, $"{_className} - {_methodName}()");
                throw;
            }
        }
        protected async Task<bool> CreateLeadAssignmentsHistory(List<Domain.Entities.Lead> leads, LeadAssignmentType assignmentType, LeadAssignmentDto? assignmentDto = null, CancellationToken cancellationToken = default, bool isDuplicate = default)
        {
            List<LeadAssignment> leadAssignemnts = new();
            foreach (var lead in leads)
            {
                var leadAssignmentHistories = await _leadAssignmentRepo.ListAsync(new GetLeadAssignmentsByIdSpecs(lead.Id));
                if (leadAssignmentHistories == null)
                {
                    var assignment = new LeadAssignment()
                    {
                        AssignTo = lead.AssignTo,
                        AssignedFrom = lead.AssignedFrom,
                        Notes = lead.Notes,
                        LeadId = lead.Id,
                        UserId = lead.AssignTo,
                        LeadAssignmentType = assignmentType,
                        AssignmentDate = DateTime.UtcNow,
                        LastModifiedBy = lead.LastModifiedBy,
                        CreatedBy = lead.LastModifiedBy
                    };
                    if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignTo = lead.SecondaryUserId;
                    }
                    if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                    {
                        assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                    }
                    if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                    {
                        assignment.ProjectName = assignmentDto?.Projects?.ToString();
                        assignment.SourceName = assignmentDto?.LeadSource.ToString();
                    }
                    if (isDuplicate)
                    {
                        assignment.IsDuplicate = true;
                    }
                    leadAssignemnts.Add(assignment);
                }
                else
                {
                    var leadLastAssignment = leadAssignmentHistories?.LastOrDefault();
                    if (leadLastAssignment?.AssignTo != lead?.AssignTo)
                    {
                        var assignment = new LeadAssignment()
                        {
                            AssignTo = lead.AssignTo,
                            AssignedFrom = lead.AssignedFrom,
                            Notes = lead.Notes,
                            LeadId = lead.Id,
                            UserId = lead.AssignTo,
                            LeadAssignmentType = assignmentType,
                            AssignmentDate = DateTime.UtcNow,
                            LastModifiedBy = lead.LastModifiedBy,
                            CreatedBy = lead.LastModifiedBy
                        };
                        if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignTo = lead.SecondaryUserId;
                        }
                        if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                        }
                        if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                        {
                            assignment.ProjectName = assignmentDto?.Projects?.ToString();
                            assignment.SourceName = assignmentDto?.LeadSource.ToString();
                        }
                        if (isDuplicate)
                        {
                            assignment.IsDuplicate = true;
                        }
                        leadAssignemnts.Add(assignment);
                    }
                    else if (lead?.SecondaryUserId != null && leadLastAssignment?.SecondaryAssignTo != lead.SecondaryUserId)
                    {
                        var assignment = new LeadAssignment()
                        {
                            AssignTo = lead.AssignTo,
                            AssignedFrom = lead.AssignedFrom,
                            Notes = lead.Notes,
                            LeadId = lead.Id,
                            UserId = lead.AssignTo,
                            LeadAssignmentType = assignmentType,
                            AssignmentDate = DateTime.UtcNow,
                            LastModifiedBy = lead.LastModifiedBy,
                            CreatedBy = lead.LastModifiedBy
                        };
                        if (lead.SecondaryUserId != null || lead.SecondaryUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignTo = lead.SecondaryUserId;
                        }
                        if (lead.SecondaryFromUserId != null || lead.SecondaryFromUserId != Guid.Empty)
                        {
                            assignment.SecondaryAssignFrom = lead.SecondaryFromUserId;
                        }
                        if (assignmentType == LeadAssignmentType.WithHistory && assignmentDto != null)
                        {
                            assignment.ProjectName = assignmentDto?.Projects?.ToString();
                            assignment.SourceName = assignmentDto?.LeadSource.ToString();
                        }
                        if (isDuplicate)
                        {
                            assignment.IsDuplicate = true;
                        }
                        leadAssignemnts.Add(assignment);
                    }
                }
            }
            await _leadAssignmentRepo.AddRangeAsync(leadAssignemnts, cancellationToken);
            return true;
        }
        protected async Task SetLeadDocsAsync(Domain.Entities.Lead lead, List<LeadDocument>? documentsToAdd, Domain.Enums.LeadDocumentType documentType, CancellationToken cancellationToken = default)
        {
            try
            {
                if (documentsToAdd != null && documentsToAdd.Any())
                {
                    var documents = await CreateTrueLeadDocsAsync(documentsToAdd, documentType);
                    if (lead.Documents != null)
                    {
                        lead.Documents.AddRange(documents);
                    }
                    else
                    {
                        lead.Documents = documents;
                    }
                }
            }
            catch (Exception ex)
            {
                
            }
        }
    }
}
