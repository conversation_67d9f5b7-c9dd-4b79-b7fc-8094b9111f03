﻿using Lrb.Application.ListingManagement.Web.Dtos;
using Lrb.Application.ListingManagement.Web.Specs;

namespace Lrb.Application.ListingManagement.Web.Requests
{
    public class GetAllListingSiteTrackerRequest : PaginationFilter, IRequest<PagedResponse<ViewListingSiteTrackerDto, string>>
    {
    }

    public class GetAllListingSiteTrackerRequestHandler : IRequestHandler<GetAllListingSiteTrackerRequest, PagedResponse<ViewListingSiteTrackerDto, string>>
    {
        private readonly IRepositoryWithEvents<ListingSiteTracker> _repo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllListingSiteTrackerRequestHandler(IRepositoryWithEvents<ListingSiteTracker> repo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _repo = repo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<ViewListingSiteTrackerDto, string>> Handle(GetAllListingSiteTrackerRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _repo.ListAsync(new GetAllListingSiteTrackerSpecs(request, subIds), cancellationToken);
            var count = await _repo.CountAsync(new GetAllListingSiteTrackerCountSpecs(subIds));
            var trackerDtos = trackers.Adapt<List<ViewListingSiteTrackerDto>>();
            return new(trackerDtos, count);
        }
    }
}
