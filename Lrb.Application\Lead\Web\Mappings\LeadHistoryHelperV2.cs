using Lrb.Application.Agency.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web.Dtos;
using Lrb.Application.Property.Web;
using Lrb.Application.Team.Web;
using Lrb.Domain.Entities.MasterData;
using System.Reflection;
using System.Text.RegularExpressions;

namespace Lrb.Application.Lead.Web.Mappings
{
    public static class LeadHistoryHelperV2
    {
        public static async Task<List<LeadHistoryHot>> V2CreateLeadtHistoryForVM(ViewLeadDto newLead, ViewLeadDto? oldLead, int version, List<CustomMasterLeadStatus> statuses, List<MasterPropertyType> propertiesType, IUserService userService, CancellationToken cancellationToken)
        {
            List<LeadHistoryHot> leadHistories = new();
            PropertyInfo[]? propertyInfo = newLead.GetType().GetProperties();
            var groupKey = Guid.NewGuid();
            var modifiedBy = newLead.LastModifiedBy;
            UserDetailsDto? user = null;
            try
            {
                user = await userService.GetAsync(modifiedBy.ToString(), cancellationToken);
            }
            catch
            {
            }
            foreach (var property in propertyInfo)
            {
                if (property != null && ShouldIncludePropperty(property) && property.GetValue(newLead) != null)
                {
                    if (IsComplexTypeProperty(property.PropertyType))
                    {
                        var complexItems = await GetLeadRelatedChildEntityItemsForVM(property, newLead ?? new(), oldLead, statuses, propertiesType, groupKey, version, userService, cancellationToken, user: user);
                        if (complexItems?.Any() ?? false)
                        {
                            leadHistories.AddRange(complexItems);
                        }
                    }
                    else
                    {
                        if (!string.IsNullOrEmpty(property?.GetValue(newLead)?.ToString()) && property?.GetValue(newLead)?.ToString() != "0" && property?.GetValue(newLead)?.ToString() != "None" && property?.GetValue(newLead)?.ToString() != "00000000-0000-0000-0000-000000000000")
                        {
                            LeadHistoryHot leadHistory = new();
                            leadHistory.LeadId = newLead?.Id ?? Guid.Empty;
                            if (newLead?.LastModifiedBy != null)
                            {
                                try
                                {
                                    leadHistory.ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);
                                }
                                catch (NotFoundException ex) { }
                                leadHistory.LastModifiedById = user?.Id ?? Guid.Empty;
                            }
                            leadHistory.ModifiedOn = newLead?.LastModifiedOn;
                            leadHistory.GroupKey = groupKey;
                            leadHistory.Version = version;
                            leadHistory.FieldName = Regex.Replace(property?.Name ?? string.Empty, "([A-Z])", " $1").Trim();
                            leadHistory.FieldType = property?.PropertyType.Name.ToString() ?? string.Empty;
                            if (leadHistory.FieldType.Contains("Nullable"))
                            {
                                var type = Nullable.GetUnderlyingType(property.PropertyType)?.Name;
                                leadHistory.FieldType = type ?? string.Empty;
                            }
                            leadHistory.NewValue = (property?.GetValue(newLead))?.ToString() ?? null;
                            leadHistory.OldValue = default;
                            leadHistories.Add(leadHistory);
                        }
                    }
                }
            }
            if (oldLead != null)
            {
                var userName = string.Empty;
                try
                {
                    userName = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);
                }
                catch (NotFoundException ex)
                {
                }
                AssignAuditStamps(leadHistories, userName, version);
            }
            return leadHistories;
        }

        #region Set User View
        public static async Task<ViewLeadDto> SetUserViewForProspectV1(ViewLeadDto lead, List<UserDetailsDto> users, CancellationToken cancellationToken)
        {
            if (lead == null)
            {
                return null;
            }
            try
            {
                var assignedUser = users.FirstOrDefault(i => i.Id == lead.AssignTo);
                if (assignedUser != null)
                {
                    lead.AssignedUser = assignedUser?.Adapt<UserDto>();
                    lead.AssignedUser.Name = assignedUser?.FirstName + " " + assignedUser?.LastName;
                }
            }
            catch (NotFoundException) { }
            try
            {
                var lastModifiedByUser = users.FirstOrDefault(i => i.Id == lead.LastModifiedBy);
                if (lastModifiedByUser != null)
                {
                    lead.LastModifiedByUser = lastModifiedByUser?.Adapt<UserDto>();
                    lead.LastModifiedByUser.Name = lastModifiedByUser?.FirstName + " " + lastModifiedByUser?.LastName;
                }
            }
            catch (NotFoundException e) { }

            try
            {
                var assignedFromUser = users.FirstOrDefault(i => i.Id == lead.AssignedFrom);
                if (assignedFromUser != null)
                {
                    lead.AssignedFromUser = assignedFromUser?.Adapt<UserDto>();
                    lead.AssignedFromUser.Name = assignedFromUser?.FirstName + " " + assignedFromUser?.LastName;
                }
            }
            catch (NotFoundException e) { }


            try
            {
                var sourcingManager = users.FirstOrDefault(i => i.Id == lead?.SourcingManager);

                if (sourcingManager != null)
                {
                    lead.SourcingManagerUser = sourcingManager?.Adapt<UserDto>();
                    lead.SourcingManagerUser.Name = sourcingManager?.FirstName + " " + sourcingManager?.LastName;
                }
            }

            catch (NotFoundException e) { }
            try
            {
                var closingManager = users.FirstOrDefault(i => i.Id == lead?.ClosingManager);

                if (closingManager != null)
                {
                    lead.ClosingManagerUser = closingManager?.Adapt<UserDto>();
                    lead.ClosingManagerUser.Name = closingManager?.FirstName + " " + closingManager?.LastName;
                }
            }
            catch (NotFoundException e) { }

            return lead;
        }
        #endregion

        #region Get Lead Child Entity
        private static async Task<List<LeadHistoryHot>> GetLeadRelatedChildEntityItemsForVM(PropertyInfo property, ViewLeadDto newLead, ViewLeadDto? oldLead, List<CustomMasterLeadStatus> statuses, List<MasterPropertyType> propertyTypes, Guid groupKey, int version, IUserService userService, CancellationToken cancellationToken, UserDetailsDto? user = null)
        {
            var items = new List<LeadHistoryHot>();
            var modifiedBy = newLead.LastModifiedBy;
            //UserDetailsDto? user = null;
            try
            {
                user ??= await userService.GetAsync(modifiedBy.ToString(), cancellationToken);
            }
            catch (NotFoundException ex) { }
            if (property != null)
            {
                var type = property.PropertyType;
                var typeName = type?.Name ?? default;
                if (typeName == typeof(CustomMasterLeadStatus).Name)
                {
                    var oldStatus = oldLead?.Status;
                    var newStatus = newLead?.Status;
                    if (oldStatus != null && newStatus != null)
                    {
                        var oldParentStatus = statuses?.FirstOrDefault(i => i.Id == (oldStatus?.BaseId ?? default));
                        var newParentStatus = statuses?.FirstOrDefault(i => i.Id == (newStatus?.BaseId ?? default));
                        if (oldParentStatus != null && newParentStatus != null)
                        {
                            if (oldParentStatus.Status != newParentStatus.Status)
                            {
                                var parentStatusItems = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldParentStatus?.DisplayName ?? default,
                                    NewValue = newParentStatus?.DisplayName ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(parentStatusItems);
                                if (newParentStatus?.Level > 0)
                                {
                                    var childStatusItems = new LeadHistoryHot()
                                    {
                                        FieldName = "Reason",
                                        OldValue = oldStatus?.DisplayName ?? default,
                                        NewValue = default,
                                        FieldType = property.PropertyType.Name,
                                        LeadId = newLead?.Id ?? Guid.Empty,
                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                        ModifiedOn = newLead?.LastModifiedOn ?? default,
                                        GroupKey = groupKey,
                                        Version = version
                                    };
                                    items.Add(childStatusItems);
                                }
                            }
                        }
                        else
                        {
                            if (oldStatus != null && newStatus != null)
                            {
                                if (oldStatus.Status != newStatus.Status)
                                {

                                    var parentStatusItems = new LeadHistoryHot()
                                    {
                                        FieldName = property.Name,
                                        OldValue = oldStatus?.DisplayName ?? default,
                                        NewValue = newStatus?.DisplayName ?? default,
                                        FieldType = property.PropertyType.Name,
                                        LeadId = newLead?.Id ?? Guid.Empty,
                                        ModifiedOn = newLead?.LastModifiedOn ?? default,
                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                        GroupKey = groupKey,
                                        Version = version
                                    };
                                    items.Add(parentStatusItems);
                                }
                            }
                            else if (newStatus != null)
                            {

                                var parentStatusItems = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = default,
                                    NewValue = newStatus?.DisplayName ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(parentStatusItems);
                            }
                            else if (oldStatus != null)
                            {

                                var parentStatusItems = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldStatus?.DisplayName ?? default,
                                    NewValue = default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(parentStatusItems);
                            }
                        }
                    }
                }
                else if (typeName == typeof(AddressDto).Name)
                {
                    var oldAddress = oldLead?.Address;
                    var newAddress = newLead?.Address;
                    var addressProperties = typeof(AddressDto).GetProperties();
                    foreach (var addressProperty in addressProperties)
                    {
                        if (addressProperty != null)
                        {
                            if (oldAddress != null && newAddress != null)
                            {
                                if (addressProperty?.GetValue(newAddress)?.ToString() != addressProperty?.GetValue(oldAddress)?.ToString())
                                {
                                    if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country"
                                         || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                         || addressProperty?.Name == "SubLocality")
                                    {

                                        var addressItem = new LeadHistoryHot()
                                        {
                                            FieldName = $"Customer Address {addressProperty?.Name}",
                                            OldValue = addressProperty?.GetValue(oldAddress)?.ToString() ?? default,
                                            NewValue = addressProperty?.GetValue(newAddress)?.ToString() ?? default,
                                            FieldType = property.PropertyType.Name,
                                            LeadId = newLead?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newLead?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version
                                        };
                                        items.Add(addressItem);
                                    }
                                }
                            }
                            else
                            {
                                if (addressProperty?.Name == "City" || addressProperty?.Name == "State" || addressProperty?.Name == "Country"
                                       || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                       || addressProperty?.Name == "SubLocality")
                                {
                                    if (addressProperty.GetValue(newAddress) != null)
                                    {

                                        var addressItem = new LeadHistoryHot()
                                        {
                                            FieldName = $"Customer Address {addressProperty?.Name}",
                                            OldValue = default,
                                            NewValue = addressProperty?.GetValue(newAddress)?.ToString() ?? default,
                                            FieldType = property.PropertyType.Name,
                                            LeadId = newLead?.Id ?? Guid.Empty,
                                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                            LastModifiedById = user?.Id ?? Guid.Empty,
                                            ModifiedOn = newLead?.LastModifiedOn ?? default,
                                            GroupKey = groupKey,
                                            Version = version
                                        };
                                        items.Add(addressItem);
                                    }
                                }
                            }
                        }
                    }
                }
                else if (typeName == typeof(UserDto).Name)
                {

                    if (property.Name == "AssignedUser")
                    {
                        if (oldLead?.AssignedUser != null && newLead?.AssignedUser != null)
                        {
                            var newUser = newLead.AssignedUser;
                            var oldUser = oldLead.AssignedUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var assignedUser = new LeadHistoryHot()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(assignedUser);
                            }
                        }
                        else if (newLead?.AssignedUser != null)
                        {
                            var newUser = newLead.AssignedUser;
                            var assignedUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(assignedUser);
                        }
                        else if (oldLead?.AssignedUser != null)
                        {
                            var oldUser = oldLead.AssignedUser;
                            var assignedUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(assignedUser);
                        }
                    }
                    if (property.Name == "AssignedFromUser")
                    {
                        if (oldLead?.AssignedFromUser != null && newLead?.AssignedFromUser != null)
                        {
                            var newUser = newLead.AssignedFromUser;
                            var oldUser = oldLead.AssignedFromUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var assignedFromUser = new LeadHistoryHot()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(assignedFromUser);
                            }
                        }
                        else if (newLead?.AssignedFromUser != null)
                        {
                            var newUser = newLead.AssignedFromUser;
                            var assignedFromUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(assignedFromUser);
                        }
                        else if (oldLead?.AssignedFromUser != null)
                        {
                            var oldUser = oldLead.AssignedFromUser;
                            var assignedFromUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(assignedFromUser);
                        }
                    }
                    else if (property.Name == "LastModifiedByUser")
                    {
                        if (oldLead?.LastModifiedByUser != null && newLead?.LastModifiedByUser != null)
                        {
                            var newUser = newLead.LastModifiedByUser;
                            var oldUser = oldLead.LastModifiedByUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var lastModifiedUser = new LeadHistoryHot()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(lastModifiedUser);
                            }
                        }
                        else if (newLead?.LastModifiedByUser != null)
                        {
                            var newUser = newLead.LastModifiedByUser;
                            var lastModifiedUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(lastModifiedUser);
                        }
                        else if (oldLead?.LastModifiedByUser != null)
                        {
                            var oldUser = oldLead.LastModifiedByUser;
                            var lastModifiedUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(lastModifiedUser);
                        }
                    }
                    else if (property.Name == "SourcingManagerUser")
                    {
                        if (oldLead?.SourcingManagerUser != null && newLead?.SourcingManagerUser != null)
                        {
                            var newUser = newLead.SourcingManagerUser;
                            var oldUser = oldLead.SourcingManagerUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var sourcingManagerUser = new LeadHistoryHot()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(sourcingManagerUser);
                            }
                        }
                        else if (newLead?.SourcingManagerUser != null)
                        {
                            var newUser = newLead.SourcingManagerUser;
                            var sourcingManagerUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(sourcingManagerUser);
                        }
                        else if (oldLead?.SourcingManagerUser != null)
                        {
                            var oldUser = oldLead.SourcingManagerUser;
                            var sourcingManagerUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(sourcingManagerUser);
                        }
                    }
                    else if (property.Name == "ClosingManagerUser")
                    {
                        if (oldLead?.ClosingManagerUser != null && newLead?.ClosingManagerUser != null)
                        {
                            var newUser = newLead.ClosingManagerUser;
                            var oldUser = oldLead.ClosingManagerUser;
                            if (newUser.Id != oldUser.Id)
                            {
                                var closingManagerUser = new LeadHistoryHot()
                                {
                                    FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                    OldValue = oldUser.Name ?? string.Empty,
                                    NewValue = newUser.Name ?? string.Empty,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(closingManagerUser);
                            }
                        }
                        else if (newLead?.ClosingManagerUser != null)
                        {
                            var newUser = newLead.ClosingManagerUser;
                            var closingManagerUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = default,
                                NewValue = newUser.Name ?? string.Empty,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(closingManagerUser);
                        }
                        else if (oldLead?.ClosingManagerUser != null)
                        {
                            var oldUser = oldLead.ClosingManagerUser;
                            var closingManagerUser = new LeadHistoryHot()
                            {
                                FieldName = Regex.Replace(property.Name ?? string.Empty, "([A-Z])", " $1").Trim(),
                                OldValue = oldUser.Name ?? string.Empty,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(closingManagerUser);
                        }
                    }
                }
                else if (typeName == typeof(ViewLeadEnquiryDto).Name)
                {
                    var oldPrimaryEnquiry = oldLead?.Enquiry;
                    var newPrimaryEnquiry = newLead?.Enquiry;
                    var enquiryProperties = typeof(ViewLeadEnquiryDto).GetProperties();
                    foreach (var enquiryProperty in enquiryProperties)
                    {

                        if (ShouldIncludePropperty(enquiryProperty))
                        {
                            if (IsComplexTypeProperty(enquiryProperty.PropertyType))
                            {
                                var complexItems = await GetLeadEnquiryRelatedChildEntityItemsForVM(enquiryProperty, newPrimaryEnquiry ?? new(), oldPrimaryEnquiry, propertyTypes, groupKey, version, newLead?.LastModifiedOn ?? DateTime.UtcNow, newLead?.Id ?? Guid.Empty, userService, cancellationToken, user: user);
                                if (complexItems?.Any() ?? false)
                                {
                                    items.AddRange(complexItems);
                                }
                            }
                            else
                            {
                                if ((enquiryProperty.GetValue(newPrimaryEnquiry) != null && enquiryProperty.GetValue(newPrimaryEnquiry) != default) || (oldPrimaryEnquiry != null && enquiryProperty.GetValue(oldPrimaryEnquiry) != null && enquiryProperty.GetValue(oldPrimaryEnquiry) != default))
                                {
                                    if (!string.IsNullOrEmpty(enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString()) && enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "0" && enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "None" && enquiryProperty?.GetValue(newPrimaryEnquiry)?.ToString() != "00000000-0000-0000-0000-000000000000")
                                    {
                                        LeadHistoryHot leadHistory = new();
                                        leadHistory.LeadId = newLead?.Id ?? Guid.Empty;
                                        if (newLead?.LastModifiedBy != null)
                                        {
                                            try
                                            {
                                                leadHistory.ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty);
                                            }
                                            catch (NotFoundException ex) { }
                                            leadHistory.LastModifiedById = user?.Id ?? Guid.Empty;
                                        }
                                        leadHistory.ModifiedOn = newLead?.LastModifiedOn;
                                        leadHistory.GroupKey = groupKey;
                                        leadHistory.Version = version;
                                        leadHistory.FieldName = Regex.Replace(enquiryProperty?.Name ?? string.Empty, "([A-Z])", " $1").Trim();
                                        leadHistory.FieldType = enquiryProperty?.PropertyType.Name.ToString() ?? string.Empty;
                                        if (leadHistory.FieldType.Contains("Nullable"))
                                        {
                                            var enquiryType = Nullable.GetUnderlyingType(enquiryProperty.PropertyType)?.Name;
                                            leadHistory.FieldType = enquiryType ?? string.Empty;
                                        }
                                        leadHistory.NewValue = (enquiryProperty?.GetValue(newPrimaryEnquiry))?.ToString() ?? null;
                                        leadHistory.OldValue = default;
                                        items.Add(leadHistory);
                                    }
                                }
                            }
                        }
                    }
                }
                else if (typeName == typeof(PropertyDto).Name || property.Name == "Properties")
                {

                    var oldPropNames = string.Join(",", oldLead?.Properties?.Select(i => i.Title)?.ToList() ?? new());
                    var newPropNames = string.Join(",", newLead.Properties?.Select(i => i.Title)?.ToList() ?? new());
                    if (newPropNames != null && oldPropNames != null)
                    {
                        if (newPropNames != oldPropNames)
                        {
                            var propItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldPropNames ?? default,
                                NewValue = newPropNames ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(propItem);
                        }
                    }
                    else
                    {
                        var propItem = new LeadHistoryHot()
                        {
                            FieldName = property.Name,
                            OldValue = oldPropNames ?? default,
                            NewValue = newPropNames ?? default,
                            FieldType = property.PropertyType.Name,
                            LeadId = newLead?.Id ?? Guid.Empty,
                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                            LastModifiedById = user?.Id ?? Guid.Empty,
                            ModifiedOn = newLead?.LastModifiedOn ?? default,
                            GroupKey = groupKey,
                            Version = version
                        };
                        items.Add(propItem);
                    }
                }
                else if (typeName == typeof(ChannelPartnerDto).Name)
                {
                    var oldChannelPartnerNames = string.Join(",", oldLead?.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                    var newChannelPartnerNames = string.Join(",", newLead.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                    if (oldChannelPartnerNames != null && newChannelPartnerNames != null)
                    {
                        if (oldChannelPartnerNames != newChannelPartnerNames)
                        {
                            var channelPartnerItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldChannelPartnerNames ?? default,
                                NewValue = newChannelPartnerNames ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(channelPartnerItem);
                        }
                    }
                    else
                    {
                        var channelPartnerItem = new LeadHistoryHot()
                        {
                            FieldName = property.Name,
                            OldValue = default,
                            NewValue = newChannelPartnerNames ?? default,
                            FieldType = property.PropertyType.Name,
                            LeadId = newLead?.Id ?? Guid.Empty,
                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                            LastModifiedById = user?.Id ?? Guid.Empty,
                            ModifiedOn = newLead?.LastModifiedOn ?? default,
                            GroupKey = groupKey,
                            Version = version
                        };
                        items.Add(channelPartnerItem);
                    }
                }
                else if (typeName == typeof(ProjectDto).Name || property.Name == "Projects")
                {

                    var oldProjNames = string.Join(",", oldLead?.Projects?.Select(i => i.Name)?.ToList() ?? new());
                    var newProjNames = string.Join(",", newLead.Projects?.Select(i => i.Name)?.ToList() ?? new());
                    if (oldProjNames != null && newProjNames != null)
                    {
                        if (oldProjNames != newProjNames)
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldProjNames ?? default,
                                NewValue = newProjNames ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    else
                    {
                        var projItem = new LeadHistoryHot()
                        {
                            FieldName = property.Name,
                            OldValue = oldProjNames ?? default,
                            NewValue = newProjNames ?? default,
                            FieldType = property.PropertyType.Name,
                            LeadId = newLead?.Id ?? Guid.Empty,
                            ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                            LastModifiedById = user?.Id ?? Guid.Empty,
                            ModifiedOn = newLead?.LastModifiedOn ?? default,
                            GroupKey = groupKey,
                            Version = version
                        };
                        items.Add(projItem);
                    }

                }
                else if (type != null && type.IsGenericType)
                {
                    if (type.FullName == typeof(IList<ProjectDto>).FullName || type.FullName == typeof(List<ProjectDto>).FullName)
                    {
                        var oldProjNames = string.Join(",", oldLead?.Projects?.Select(i => i.Name)?.ToList() ?? new());
                        var newProjNames = string.Join(",", newLead.Projects?.Select(i => i.Name)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldProjNames) && !string.IsNullOrEmpty(newProjNames))
                        {
                            if (oldProjNames != newProjNames)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldProjNames ?? default,
                                    NewValue = newProjNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newProjNames))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newProjNames ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<AgencyDto>).FullName || type.FullName == typeof(List<AgencyDto>).FullName)
                    {

                        var oldAgencyNames = string.Join(",", oldLead?.Agencies?.Select(i => i.Name)?.ToList() ?? new());
                        var newAgencyNames = string.Join(",", newLead.Agencies?.Select(i => i.Name)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldAgencyNames) && !string.IsNullOrEmpty(newAgencyNames))
                        {
                            if (oldAgencyNames != newAgencyNames)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldAgencyNames ?? default,
                                    NewValue = newAgencyNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newAgencyNames))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newAgencyNames ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                        else if (!string.IsNullOrEmpty(oldAgencyNames))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldAgencyNames,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<PropertyDto>).FullName || type.FullName == typeof(List<PropertyDto>).FullName)
                    {

                        var oldPropNames = string.Join(",", oldLead?.Properties?.Select(i => i.Title)?.ToList() ?? new());
                        var newPropNames = string.Join(",", newLead.Properties?.Select(i => i.Title)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(newPropNames) && !string.IsNullOrEmpty(oldPropNames))
                        {
                            if (newPropNames != oldPropNames)
                            {
                                var propItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldPropNames ?? default,
                                    NewValue = newPropNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(propItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newPropNames))
                        {
                            var propItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newPropNames ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(propItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<ChannelPartnerDto>).FullName || type.FullName == typeof(List<ChannelPartnerDto>).FullName)
                    {

                        var oldChannelPartnerNames = string.Join(",", oldLead?.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                        var newChannelPartnerNames = string.Join(",", newLead.ChannelPartners?.Select(i => i.FirmName)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldChannelPartnerNames) && !string.IsNullOrEmpty(newChannelPartnerNames))
                        {
                            if (oldChannelPartnerNames != newChannelPartnerNames)
                            {
                                var channelPartnerItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldChannelPartnerNames ?? default,
                                    NewValue = newChannelPartnerNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(channelPartnerItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newChannelPartnerNames))
                        {
                            var channelPartnerItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newChannelPartnerNames ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(channelPartnerItem);
                        }
                        else if (!string.IsNullOrEmpty(oldChannelPartnerNames))
                        {
                            var channelPartnerItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldChannelPartnerNames ?? default,
                                NewValue = newChannelPartnerNames ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(channelPartnerItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<CampaignDto>).FullName || type.FullName == typeof(List<CampaignDto>).FullName)
                    {
                        var oldCampaignNames = string.Join(",", oldLead?.Campaigns?.Select(i => i.Name)?.ToList() ?? new());
                        var newCampaignNames = string.Join(",", newLead.Campaigns?.Select(i => i.Name)?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldCampaignNames) && !string.IsNullOrEmpty(newCampaignNames))
                        {
                            if (oldCampaignNames != newCampaignNames)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldCampaignNames ?? default,
                                    NewValue = newCampaignNames ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newCampaignNames))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newCampaignNames ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                        else if (!string.IsNullOrEmpty(oldCampaignNames))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldCampaignNames,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(Dictionary<ContactType, int>).FullName || type.FullName == typeof(Dictionary<ContactType, int>).FullName)
                    {
                        var oldContactType = oldLead?.ContactRecords?.LastOrDefault().Key.ToString();
                        var newContactType = newLead.ContactRecords?.LastOrDefault().Key.ToString();

                        if (newContactType != null && oldContactType != null && (oldLead?.ContactRecords?.Any() ?? false))
                        {
                            var changedValues = newLead?.ContactRecords?.Where(newEntry => oldLead.ContactRecords.ContainsKey(newEntry.Key)
                              && oldLead.ContactRecords[newEntry.Key] != newEntry.Value)
                            .Select(newEntry => new LeadHistoryHot
                            {
                                FieldName = property.Name,
                                OldValue = null,
                                NewValue = newEntry.Key.ToString(),
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            });
                            items.AddRange(changedValues.ToList());
                            if (!changedValues?.Any() ?? false)
                            {
                                var newEntries = newLead?.ContactRecords?
                                .Where(newEntry => oldLead?.ContactRecords == null ||
                                                   !oldLead.ContactRecords.ContainsKey(newEntry.Key))
                                .Select(newEntry => new LeadHistoryHot
                                {
                                    FieldName = property.Name,
                                    OldValue = null,
                                    NewValue = newEntry.Key.ToString(),
                                    FieldType = property.PropertyType.Name,
                                    LeadId = newLead?.Id ?? Guid.Empty,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = newLead?.LastModifiedOn ?? default,
                                    GroupKey = groupKey,
                                    Version = version
                                });
                                items.AddRange(newEntries.ToList());
                            }
                        }
                        else
                        {
                            var contactTypeItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = null,
                                NewValue = newContactType ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = newLead?.Id ?? Guid.Empty,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = newLead?.LastModifiedOn ?? default,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(contactTypeItem);
                        }
                    }
                }
            }
            return items;
        }

        public static (string, string, string, string) GetPropertiesForProspectHistory(ViewLeadEnquiryDto oldLeadEnquiryDto, ViewLeadEnquiryDto newLeadEnquiryDto, string propertyName)
        {
            string updatedPropertyName = string.Empty;
            string updatedPropertyType = string.Empty;
            string oldProperty = string.Empty;
            string newProperty = string.Empty;
            if (propertyName == "SubLocality")
            {
                updatedPropertyName = "Enquiry SubLocalities";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubLocality)).Select(i => i.SubLocality).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubLocality)).Select(i => i.SubLocality).Distinct().ToList() ?? new());
            }
            if (propertyName == "City")
            {
                updatedPropertyName = "Enquiry Cities";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.City)).Select(i => i.City).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.City)).Select(i => i.City).Distinct().ToList() ?? new());
            }
            if (propertyName == "State")
            {
                updatedPropertyName = "Enquiry States";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.State)).Select(i => i.State).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.State)).Select(i => i.State).Distinct().ToList() ?? new());
            }
            if (propertyName == "Country")
            {
                updatedPropertyName = "Enquiry Countries";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Country)).Select(i => i.Country).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Country)).Select(i => i.Country).Distinct().ToList() ?? new());
            }
            if (propertyName == "BHKTypes")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of BHKTypes";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.BHKTypes?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.BHKTypes?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "Beds")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of Beds";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Beds?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Beds?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "Baths")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of Baths";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Baths?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Baths?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "Floors")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of Floors";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Floors?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Floors?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "BHKs")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of BHKs";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.BHKs?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.BHKs?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "EnquiryTypes")
            {
                updatedPropertyName = propertyName;
                updatedPropertyType = "List of EnquiryTypes";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.EnquiryTypes?.Select(i => i).ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.EnquiryTypes?.Select(i => i).ToList() ?? new());
            }
            if (propertyName == "TowerName")
            {
                updatedPropertyName = "Enquiry TowerName";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.TowerName)).Select(i => i.TowerName).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.TowerName)).Select(i => i.TowerName).Distinct().ToList() ?? new());
            }
            if (propertyName == "SubCommunity")
            {
                updatedPropertyName = "Enquiry SubCommunity";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubCommunity)).Select(i => i.SubCommunity).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.SubCommunity)).Select(i => i.SubCommunity).Distinct().ToList() ?? new());
            }
            if (propertyName == "Community")
            {
                updatedPropertyName = "Enquiry Community";
                updatedPropertyType = "List of Address";
                oldProperty = string.Join(",", oldLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Community)).Select(i => i.Community).Distinct().ToList() ?? new());
                newProperty = string.Join(",", newLeadEnquiryDto?.Addresses?.Where(i => !string.IsNullOrWhiteSpace(i.Community)).Select(i => i.Community).Distinct().ToList() ?? new());
            }


            return (updatedPropertyName, updatedPropertyType, oldProperty, newProperty);
        }
        #endregion

        #region Get Lead Enqiry Child Entity
        private static async Task<List<LeadHistoryHot>> GetLeadEnquiryRelatedChildEntityItemsForVM(PropertyInfo property, ViewLeadEnquiryDto newLeadEnquiry, ViewLeadEnquiryDto? oldLeadEnqyiry, List<MasterPropertyType> propertyTypes, Guid groupKey, int version, DateTime lastModifiedOn, Guid leadId, IUserService userService, CancellationToken cancellationToken, UserDetailsDto? user = null)
        {
            var items = new List<LeadHistoryHot>();
            if (property != null)
            {
                var type = property.PropertyType;
                var typeName = type?.Name ?? default;
                if (type != null && type.IsGenericType)
                {
                    if (type.FullName == typeof(IList<EnquiryType>).FullName || type.FullName == typeof(List<EnquiryType>).FullName)
                    {
                        var oldEnquiries = string.Join(",", oldLeadEnqyiry?.EnquiryTypes?.ToList() ?? new());
                        var newEnquiries = string.Join(",", newLeadEnquiry.EnquiryTypes?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var EnquiryItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(EnquiryItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var EnquiryItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(EnquiryItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<BHKType>).FullName || type.FullName == typeof(List<BHKType>).FullName)
                    {
                        var oldEnquiries = string.Join(",", oldLeadEnqyiry?.BHKTypes?.ToList() ?? new());
                        var newEnquiries = string.Join(",", newLeadEnquiry?.BHKTypes?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    else if (type.FullName == typeof(IList<double>).FullName || type.FullName == typeof(List<double>).FullName)
                    {
                        var oldEnquiries = string.Join(",", oldLeadEnqyiry?.BHKs?.ToList() ?? new());
                        var newEnquiries = string.Join(",", newLeadEnquiry.BHKs?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    if (type.FullName == typeof(IList<int>).FullName || type.FullName == typeof(List<int>).FullName)
                    {
                        var oldEnquiries = string.Empty;
                        var newEnquiries = string.Empty;
                        if(property.Name == "Baths")
                        {
                            oldEnquiries = string.Join(",", oldLeadEnqyiry?.Baths?.ToList() ?? new());
                            newEnquiries = string.Join(",", newLeadEnquiry?.Baths?.ToList() ?? new());
                        }
                        else if (property.Name == "Beds")
                        {
                            oldEnquiries = string.Join(",", oldLeadEnqyiry?.Beds?.ToList() ?? new());
                            newEnquiries = string.Join(",", newLeadEnquiry?.Beds?.ToList() ?? new());
                        }
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    if (type.FullName == typeof(IList<string>).FullName || type.FullName == typeof(List<string>).FullName)
                    {
                        var oldEnquiries = string.Join(",", oldLeadEnqyiry?.Floors?.ToList() ?? new());
                        var newEnquiries = string.Join(",", newLeadEnquiry?.Floors?.ToList() ?? new());
                        if (!string.IsNullOrEmpty(oldEnquiries) && !string.IsNullOrEmpty(newEnquiries))
                        {
                            if (oldEnquiries != newEnquiries)
                            {
                                var projItem = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldEnquiries ?? default,
                                    NewValue = newEnquiries ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(projItem);
                            }
                        }
                        else if (!string.IsNullOrEmpty(newEnquiries))
                        {
                            var projItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newEnquiries ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(projItem);
                        }
                    }
                    if (type.FullName == typeof(IList<AddressDto>).FullName || type.FullName == typeof(List<AddressDto>).FullName)
                    {
                        var addressProperties = typeof(AddressDto).GetProperties();
                        foreach (var addressProperty in addressProperties)
                        {
                            if (addressProperty.Name == "City" ||
                                       addressProperty.Name == "State" || addressProperty.Name == "SubLocality" || addressProperty.Name == "Country"
                                    || addressProperty?.Name == "Community" || addressProperty?.Name == "SubCommunity" || addressProperty?.Name == "TowerName"
                                    || addressProperty?.Name == "SubLocality")
                            {
                                (string propertyName, string propertyType, string oldValue, string newValue) historyProperties = GetPropertiesForProspectHistory(oldLeadEnqyiry, newLeadEnquiry, addressProperty.Name);
                                if (historyProperties.oldValue != historyProperties.newValue)
                                {
                                    var prospectSource = new LeadHistoryHot()
                                    {
                                        FieldName = historyProperties.propertyName,
                                        OldValue = historyProperties.oldValue,
                                        NewValue = historyProperties.newValue,
                                        FieldType = historyProperties.propertyType,
                                        LeadId = leadId,
                                        ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                        LastModifiedById = user?.Id ?? Guid.Empty,
                                        ModifiedOn = lastModifiedOn,
                                        GroupKey = groupKey,
                                        Version = version
                                    };
                                    items.Add(prospectSource);
                                }
                            }
                        }
                    }
                    if (type.FullName == typeof(IList<PropertyTypeDto>).FullName || type.FullName == typeof(List<PropertyTypeDto>).FullName)
                    {
                        var oldParentPropertyType = propertyTypes?.Where(pt => oldLeadEnqyiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                        var newParentPropertyType = propertyTypes?.Where(pt => newLeadEnquiry?.PropertyTypes?.Any(ope => ope.BaseId == pt.Id || ope.Id == pt.Id) ?? false).Select(pt => pt.DisplayName.ToString()).FirstOrDefault();
                        var childOldType = oldLeadEnqyiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                        var childNewType = newLeadEnquiry?.PropertyTypes?.Select(i => i.ChildType?.DisplayName).ToList();
                        string childOldTypes = null;
                        string childNewTypes = null;
                        if (childOldType != null)
                        {
                            childOldTypes = string.Join(",", childOldType);
                        }
                        if (childNewType != null)
                        {
                            childNewTypes = string.Join(",", childNewType);
                        }
                        if (oldParentPropertyType != null && newParentPropertyType != null)
                        {
                            if (oldParentPropertyType != newParentPropertyType)
                            {
                                var parentPropertyTypeItems = new LeadHistoryHot()
                                {
                                    FieldName = property.Name,
                                    OldValue = oldParentPropertyType ?? default,
                                    NewValue = newParentPropertyType ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(parentPropertyTypeItems);

                                var childPropertyTypeItems = new LeadHistoryHot()
                                {
                                    FieldName = "SubPropertyType",
                                    OldValue = childOldTypes ?? default,
                                    NewValue = childNewTypes ?? default,
                                    FieldType = property.PropertyType.Name,
                                    LeadId = leadId,
                                    ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                    LastModifiedById = user?.Id ?? Guid.Empty,
                                    ModifiedOn = lastModifiedOn,
                                    GroupKey = groupKey,
                                    Version = version
                                };
                                items.Add(childPropertyTypeItems);
                            }
                        }
                        else if (oldParentPropertyType != null)
                        {
                            var parentPropertyTypeItems = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldParentPropertyType ?? default,
                                NewValue = newParentPropertyType ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(parentPropertyTypeItems);
                            var childPropertyTypeItems = new LeadHistoryHot()
                            {
                                FieldName = "SubPropertyType",
                                OldValue = childOldTypes ?? default,
                                NewValue = default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version,
                            };
                            items.Add(childPropertyTypeItems);
                        }
                        else if (newParentPropertyType != null)
                        {
                            var parentPropertyTypeItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = default,
                                NewValue = newParentPropertyType ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(parentPropertyTypeItem);
                            var childPropertyTypeItem = new LeadHistoryHot()
                            {
                                FieldName = "SubPropertyType",
                                OldValue = default,
                                NewValue = childNewTypes ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                            items.Add(childPropertyTypeItem);
                        }
                        else
                        {
                            var childPropertyTypeItem = new LeadHistoryHot()
                            {
                                FieldName = property.Name,
                                OldValue = oldParentPropertyType ?? default,
                                NewValue = newParentPropertyType ?? default,
                                FieldType = property.PropertyType.Name,
                                LeadId = leadId,
                                ModifiedBy = (user?.FirstName ?? string.Empty) + " " + (user?.LastName ?? string.Empty),
                                LastModifiedById = user?.Id ?? Guid.Empty,
                                ModifiedOn = lastModifiedOn,
                                GroupKey = groupKey,
                                Version = version
                            };
                        }
                    }
                }
            }
            return items;
        }
        #endregion

        #region Check If Property is complex type
        public static bool IsComplexTypeProperty(this Type type)
        {
            var data = Nullable.GetUnderlyingType(type)?.Name;
            if (type.Name == typeof(long).Name || type.Name == typeof(int).Name || type.Name == typeof(short).Name || type.Name == typeof(UIntPtr).Name || type?.Name == typeof(uint).Name || type.Name == typeof(byte).Name || type.Name == typeof(char).Name)
            {
                return false;
            }
            else if (type.Name == typeof(Half).Name || type.Name == typeof(float).Name || type.Name == typeof(double).Name || type.Name == typeof(decimal).Name)
            {
                return false;
            }
            else if (type.Name == typeof(bool).Name)
            {
                return false;
            }
            else if (type.Name == typeof(string).Name || type.Name == typeof(Guid).Name || type.Name == typeof(DateTime).Name)
            {
                return false;
            }
            else if (type.IsEnum || type.IsValueType)
            {
                return false;
            }
            else if (type.IsGenericType || type.IsNested || type.IsClass)
            {
                return true;
            }
            return false;
        }
        #endregion

        #region Properties to be Included in Lead History
        private static bool ShouldIncludePropperty(PropertyInfo property)
        {
            List<string> propertiesToKeep = new()
            {
                "LeadId",
                "DomainEvents",
                "AddressId",
                "Id"
            };
            return !propertiesToKeep.Contains(property.Name);
        }
        #endregion

        #region Audit Stamps
        private static void AssignAuditStamps(List<LeadHistoryHot> items, string user, int lastVersion)
        {
            var timeStamp = DateTime.UtcNow;
            var newVersion = lastVersion + 1;
            items.ForEach(item => { item.Version = newVersion; item.ModifiedOn = timeStamp; item.ModifiedBy = user; });
        }
        #endregion
    }
}
