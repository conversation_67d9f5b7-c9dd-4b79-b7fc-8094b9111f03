﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.PushNotification.Mobile.Requests
{
    public class GetAllUnOpenedPushNotificationsCountByUserRequest : IRequest<Response<long>>
    {

    }
    public class GetAllUnOpenedPushNotificationsCountByUserRequestHandler : IRequestHandler<GetAllUnOpenedPushNotificationsCountByUserRequest, Response<long>>
    {
        private readonly ICurrentUser _currentUserRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<LocalNotification> _localNotificationRepo;
        public GetAllUnOpenedPushNotificationsCountByUserRequestHandler(
                ICurrentUser currentUserRepo,
                IDapperRepository dapperRepository,
                IRepositoryWithEvents<LocalNotification> localNotificationRepo)
        {
            _currentUserRepo = currentUserRepo;
            _dapperRepository = dapperRepository;
            _localNotificationRepo = localNotificationRepo;
        }
        public async Task<Response<long>> Handle(GetAllUnOpenedPushNotificationsCountByUserRequest request, CancellationToken cancellationToken)
        {
            Guid currentUserId = _currentUserRepo.GetUserId();
            var notificationsCount = await _dapperRepository.GetTotalUnOpenedNotificationsCountAsync(currentUserId, _currentUserRepo.GetTenant());
            return new()
            {
                Data = notificationsCount,
                Succeeded = true
            };
        }
    }
}
