﻿using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.ChannelPartner.Web.Request
{

    public class GetAllBulkCpTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
    }
    public class GetAllBulkCpTrackersRequestHandler : IRequestHandler<GetAllBulkCpTrackersRequest, PagedResponse<BulkMarketingAgencyUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> _trackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;

        public GetAllBulkCpTrackersRequestHandler(IRepositoryWithEvents<BulkMarketingAgencyUploadTracker> trackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _trackerRepo = trackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<BulkMarketingAgencyUploadTracker, string>> Handle(GetAllBulkCpTrackersRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _trackerRepo.ListAsync(new ChannelPartnersTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new ChannelPartnersTrackerCountSpec(subIds), cancellationToken);
            return new(trackers, totalCount);
        }
    }
}
