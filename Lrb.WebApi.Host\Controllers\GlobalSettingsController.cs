﻿using Lrb.Application.CuntryInformation.Web.Dto;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.GlobalSettings.Web.Dto;
using Lrb.Application.GlobalSettings.Web.Requests;
using Lrb.Domain.Entities;

namespace Lrb.WebApi.Host.Controllers
{
    [Authorize]
    public class GlobalSettingsController : VersionedApiController
    {
        [HttpPut("notification")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update notification settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateNotificationSettingsRequest request)
        {
            return await Mediator.Send(request);
        }
        [AllowAnonymous]
        [HttpPut("call")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update call settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateCallSettingsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("otp")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update otp settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateOTPSettingsRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update global settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateGlobalSettingsRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.GlobalSettings)]
        [OpenApiOperation("Get global settings.", "")]
        public async Task<Response<ViewGlobalSettingsDto>> GetAsync()
        {
            return await Mediator.Send(new GetGlobalSettingsRequest());
        }
        [HttpPost("DuplicateFeature")]
        [TenantIdHeader]
        //  [MustHavePermission(LrbAction.Create, LrbResource.GlobalSettings)]
        [OpenApiOperation("create duplicate featureInfo.")]

        public async Task<Response<bool>> CreateAsync(CreateDuplecateFeatureInfoRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpPut("DuplicateFeature")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("update duplicate featureInfo.")]

        public async Task<Response<bool>> UpdateAsync(UpdateDuplicateFeatureInfoRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("DuplicateFeature")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.GlobalSettings)]
        [OpenApiOperation("Get duplicate featureInfo.")]

        public async Task<Response<DuplicateFeatureDto>> GetDuplicateInfoAsync()
        {
            return await Mediator.Send(new GetDuplicateFeatureInfoRequest());
        }

        [AllowAnonymous]
        [HttpGet("anonymous")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.View, LrbResource.GlobalSettings)]
        [OpenApiOperation("Get global settings.", "")]
        public async Task<Response<ViewGlobalSettingsDto>> GetWithoutPermissionAsync()
        {
            return await Mediator.Send(new GetGlobalSettingsRequest());
        }
        [HttpPut("notes")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update notes settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateLeadNotesSettingRequest request)
        {
            return await Mediator.Send(request);
        }

        [AllowAnonymous]
        [HttpGet("duplicatefeature/anonymous")]
        [TenantIdHeader]
        //[MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Get duplicate featureInfo.")]

        public async Task<Response<DuplicateFeatureDto>> GetDuplicateInfoWithoutAuthenticationAsync()
        {
            return await Mediator.Send(new GetDuplicateFeatureInfoRequest());
        }

        [HttpGet("dual-ownership-details")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.GlobalSettings)]
        [OpenApiOperation("Get dual ownership details.", "")]
        public async Task<Response<bool>> GetDualOwnershipDetailsAsync()
        {
            return await Mediator.Send(new GetDualOwnershipDetailsRequest());
        }

        [HttpPut("project")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update lead project settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateLeadProjectSettingRequest request)
        {
            return await Mediator.Send(request);
        }

        [HttpPut("property")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update lead propertry settings.", "")]
        public async Task<Response<bool>> UpdateAsync(UpdateLeadPropertySettingRequest request)
        {
            return await Mediator.Send(request);
        }
        [HttpGet("countriesInfo")]
        [TenantIdHeader]
        public async Task<Response<BaseCountryInfoDto>> GetCustomMasterLeadStatuses1()
        {
            return await Mediator.Send(new GetAllCountryInfoRequest());
        }

        [AllowAnonymous]
        [HttpGet("otp/anonymous")]
        [TenantIdHeader]
        [OpenApiOperation("Get OTP settings.", "")]
        public async Task<Response<OTPSettings>> GetOTPSettingsAsync()
        {
            return await Mediator.Send(new GetOTPSettingsRequest());
        }

        [HttpGet("TempVariable")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.View, LrbResource.GlobalSettings)]
        [OpenApiOperation("Get all the Temp Variables")]
        public async Task<Response<TempVariableDto>> GetTempVariablesAsync()
        {
            return await Mediator.Send(new GetAllTempVariablesRequest());
        }

        [HttpPut("TempVariable")]
        [TenantIdHeader]
        [MustHavePermission(LrbAction.Update, LrbResource.GlobalSettings)]
        [OpenApiOperation("Update all the Temp Variables")]
        public async Task<Response<bool>> PutAsync(UpdateTempVariableRequest request)
        {
            return await Mediator.Send(request);
        }
    }
}
