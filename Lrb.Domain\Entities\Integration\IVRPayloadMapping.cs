﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Lrb.Domain.Entities
{
    public class IVRPayloadMapping : AuditableEntity, IAggregateRoot
    {
        [Column(TypeName = "jsonb")]
        public Dictionary<string, string>? PushEndPointMappings { get; set; } //#agentNumber#:"AgentNumber"
        public bool? IsPayloadInForm { get; set; }
        public string? ContentType { get; set; }
        public string? MethodType { get; set; }
        [Column(TypeName = "jsonb")]
        public Dictionary<IVRCallStatus, string>? CallStatusMappings { get; set; }
    }
}