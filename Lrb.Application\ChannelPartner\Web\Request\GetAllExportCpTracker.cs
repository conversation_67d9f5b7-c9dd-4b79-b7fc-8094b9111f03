﻿using Lrb.Application.Identity.Users;
using Lrb.Application.Marketing.Web.Dtos;
using Lrb.Application.Marketing.Web.Specs;
using Lrb.Application.UserDetails.Web;
using Lrb.Domain.Entities.Marketing;

namespace Lrb.Application.ChannelPartner.Web.Request
{
    public class GetAllExportCpTracker : PaginationFilter, IRequest<PagedResponse<ExportMarketingTrackerDto, string>>
    {
    }
    public class GetAllExportCpTrackerHandler : IRequestHandler<GetAllExportCpTracker, PagedResponse<ExportMarketingTrackerDto, string>>
    {
        private readonly IReadRepository<ExportMarketingTracker> _exportCpTrackerRepo;
        public readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllExportCpTrackerHandler(IReadRepository<ExportMarketingTracker> exportCpTrackerRepo,
            IUserService userService, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _exportCpTrackerRepo = exportCpTrackerRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<ExportMarketingTrackerDto, string>> Handle(GetAllExportCpTracker request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var cpTrackers = await _exportCpTrackerRepo.ListAsync(new GetExportCpTrackersSpec(request, subIds), cancellationToken);
            var totalCount = await _exportCpTrackerRepo.CountAsync(new GetExportCpTrackersCountSpec(subIds), cancellationToken);
            var trackersDto = cpTrackers.Adapt<List<ExportMarketingTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(trackersDto.Select(i => i.CreatedBy.ToString()).ToList(), cancellationToken);
            trackersDto.ForEach(i =>
            {
                i.ExportedUser = (users?.FirstOrDefault(j => j.Id == i.CreatedBy))?.Adapt<ViewUserDto>();
            });
            return new(trackersDto, totalCount);
        }
    }
}
