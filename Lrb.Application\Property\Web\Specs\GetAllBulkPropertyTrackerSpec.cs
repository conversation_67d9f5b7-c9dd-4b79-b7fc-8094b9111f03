﻿namespace Lrb.Application.Property.Web.Specs
{
    public class GetAllBulkPropertyTrackerSpec : EntitiesByPaginationFilterSpec<BulkPropertyUploadTracker>
    {
        public GetAllBulkPropertyTrackerSpec(GetAllBulkUploadTrackersRequest filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class GetBulkPropertyTrackerCountSpec : Specification<BulkPropertyUploadTracker>
    {
        public GetBulkPropertyTrackerCountSpec( List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
