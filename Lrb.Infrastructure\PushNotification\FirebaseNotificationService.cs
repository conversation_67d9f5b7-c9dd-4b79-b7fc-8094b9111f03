using Lrb.Application.Common.PushNotification;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Notifications.Dtos;
using Lrb.Application.PushNotification;
using Lrb.Domain.Entities;

namespace Lrb.Infrastructure.PushNotification
{
    /// <summary>
    /// Firebase-only notification service that replaces AWS Pinpoint with Firebase Cloud Messaging (FCM)
    /// Maintains the same interface as the original NotificationService but uses only Firebase services
    /// </summary>
    public class FirebaseNotificationService : INotificationService
    {
        #region Constants and Error Messages
        private const string TOPIC_ATTRIBUTE_KEY = "TOPICS";
        private const string APP_TOKEN_ATTRIBUTE_KEY = "APP_TOKEN";
        private const string USERID_ATTRIBUTE_KEY = "USERID";
        private const string USERNAME_ATTRIBUTE_KEY = "USERNAME";
        private const string EMAIL_ATTRIBUTE_KEY = "EMAIL";
        protected readonly string DeviceUserAlreadyRegisteredException = "Device user already exists";
        private const string ENDPOINT_ACTIVE = "ACTIVE";
        private const string ENDPOINT_INACTIVE = "INACTIVE";
        private const string PLATFROM_ANDROID_VALUE = "Android";
        private const string PLATFROM_iOS_VALUE = "iOS";
        private const string CreatedDateColumnName = "CreatedDate";
        private const string ApplicationName = "ApplicationName";
        private const string AppToken = "Leadrat";
        protected readonly string NotificationMessageBodyORRawContentBothShouldNotBeNull = "MessageBody or RawContent should not be null";
        protected readonly string NotificationMessageBodyAndRawContentExist = "MessageBody and RawContent both should not be part of the payload";
        protected readonly string NotificationScheduleDateShouldNotBeNull = "Date is Required for Schedule Notifications";
        protected readonly string NotificationScheduleTimeShouldNotBeNull = "Time is Required for Schedule Notifications";
        protected readonly string NotificationScheduleTimeZoneShouldNotBeNull = "Time Zone is Required for Schedule Notifications";
        #endregion

        #region Private Fields
        private readonly Serilog.ILogger _logger;
        private readonly IRepositoryWithEvents<Notification> _repository;
        private readonly IRepositoryWithEvents<Device> _deviceRepository;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<PushNotificationRecords> _pushNotificationRecordsRepo;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IRepositoryWithEvents<GlobalSettings> _globalSettingsRepo;
        private readonly ICustomEmailService _customEmailService;
        private readonly IReadRepository<MasterEmailInfo> _masterEmailInfoRepo;
        private readonly IRepositoryWithEvents<WAMessage> _waMessage;
        private readonly IRepositoryWithEvents<WAPayloadMapping> _waPayloadMappingRepo;
        private readonly FirebaseSetting _firebaseSetting;
        private readonly MobileFirebaseSetting _mobileFirebaseSetting;
        private readonly RestClient _restClient;
        private readonly IRepositoryWithEvents<WAApiInfo> _waApiInfo;
        private readonly IUserService _userService;
        #endregion

        #region Constructor
        public FirebaseNotificationService(
            IRepositoryWithEvents<Notification> repository,
            IRepositoryWithEvents<Device> deviceRepository,
            Serilog.ILogger logger,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<PushNotificationRecords> pushNotificationRecordsRepo,
            IGraphEmailService graphEmailService,
            IRepositoryWithEvents<GlobalSettings> globalSettingsRepo,
            ICustomEmailService customEmailService,
            IReadRepository<MasterEmailInfo> masterEmailInfoRepo,
            IRepositoryWithEvents<WAMessage> waMessage,
            IRepositoryWithEvents<WAPayloadMapping> waPayloadMappingRepo,
            IOptions<FirebaseSetting> firebaseSetting,
            IOptions<MobileFirebaseSetting> mobileFirebaseSetting,
            IOptions<RestClientOptions> restClientOptions,
            IRepositoryWithEvents<WAApiInfo> waApiInfo,
            IUserService userService)
        {
            _repository = repository;
            _deviceRepository = deviceRepository;
            _logger = logger;
            _leadRepositoryAsync = leadRepositoryAsync;
            _pushNotificationRecordsRepo = pushNotificationRecordsRepo;
            _graphEmailService = graphEmailService;
            _globalSettingsRepo = globalSettingsRepo;
            _customEmailService = customEmailService;
            _masterEmailInfoRepo = masterEmailInfoRepo;
            _waMessage = waMessage;
            _waPayloadMappingRepo = waPayloadMappingRepo;
            _firebaseSetting = firebaseSetting.Value;
            _mobileFirebaseSetting = mobileFirebaseSetting.Value;
            _restClient = new RestClient(restClientOptions.Value);
            _waApiInfo = waApiInfo;
            _userService = userService;
        }
        #endregion

        #region Public Interface Methods
        public async Task<Guid> CreateNotificationAsync(NotificationDTO notification)
        {
            bool isBodyAvailable = false;

            if (!string.IsNullOrEmpty(notification.MessageBody))
                isBodyAvailable = true;

            if (!string.IsNullOrEmpty(notification.RawContent))
                isBodyAvailable = true;
            if (!string.IsNullOrEmpty(notification.APNsRawContent))
                isBodyAvailable = true;
            if (!string.IsNullOrEmpty(notification.FCMRawContent))
                isBodyAvailable = true;

            if (!isBodyAvailable)
            {
                throw new InvalidOperationException(NotificationMessageBodyAndRawContentExist);
            }

            if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ScheduledDate)))
                throw new InvalidOperationException(NotificationScheduleDateShouldNotBeNull);

            if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ScheduledTime)))
                throw new InvalidOperationException(NotificationScheduleTimeShouldNotBeNull);

            if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ZoneName)))
                throw new InvalidOperationException(NotificationScheduleTimeZoneShouldNotBeNull);

            notification.Created = DateTime.UtcNow;

            // Remove HTML tags
            var regex = new Regex("<[^>]+>", RegexOptions.IgnoreCase);
            notification.MessageBody = string.IsNullOrEmpty(notification.MessageBody) ? string.Empty : WebUtility.HtmlDecode((regex.Replace(notification.MessageBody, "")));
            notification.SentToPinpoint = false; // Keep for backward compatibility, but not used

            await _repository.AddAsync(notification.Adapt<Notification>());
            return notification.Id;
        }

        public async Task<Guid> SendNotificationAsync(NotificationDTO notification)
        {
            try
            {
                _logger.Information($"FirebaseNotificationService::SendNotificationAsync() called, NotificationDTO: {JsonConvert.SerializeObject(notification)}");

                bool isBodyAvailable = false;

                if (!string.IsNullOrEmpty(notification.MessageBody))
                    isBodyAvailable = true;

                if (!string.IsNullOrEmpty(notification.RawContent))
                    isBodyAvailable = true;
                if (!string.IsNullOrEmpty(notification.APNsRawContent))
                    isBodyAvailable = true;
                if (!string.IsNullOrEmpty(notification.FCMRawContent))
                    isBodyAvailable = true;

                if (!isBodyAvailable)
                {
                    throw new InvalidOperationException(NotificationMessageBodyAndRawContentExist);
                }

                if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ScheduledDate)))
                    throw new InvalidOperationException(NotificationScheduleDateShouldNotBeNull);

                if (notification.IsScheduled && string.IsNullOrEmpty(Convert.ToString(notification.ScheduledTime)))
                    throw new InvalidOperationException(NotificationScheduleTimeShouldNotBeNull);

                notification.SendNotificationType = NotificationType.TargetSpecific;
                _logger.Information("notification.SendNotificationType==={0}", notification.SendNotificationType);

                // Use Firebase Cloud Messaging instead of AWS Pinpoint
                notification.UniqueId = Guid.NewGuid();
                var success = await SendFirebaseNotificationsAsync(notification);

                if (success)
                {
                    notification.SentToPinpoint = true; // Keep for backward compatibility
                    var records = await CreateNotificationRecordsAsync(notification);
                    await _pushNotificationRecordsRepo.AddRangeAsync(records);
                }

                var notificationsAdded = await _repository.AddRangeAsync(await GetUpdatedNotificationsAsync(notification, new List<PushNotificationRecords>()));
                var uniqueNotification = notificationsAdded?.FirstOrDefault();

                _logger.Information($"Notification Unique Id -> {uniqueNotification?.UniqueId}");
                return uniqueNotification?.Id ?? default;
            }
            catch (Exception ex)
            {
                _logger.Information($"FirebaseNotificationService::SendNotificationAsync() Exception: {JsonConvert.SerializeObject(ex)}");
                await LogErrorAsync(ex, "SendNotificationAsync");
                throw;
            }
        }

        public async Task<Guid> UpdateAndSendNotificationAsync(NotificationDTO notification)
        {
            try
            {
                // Update existing notification and send via Firebase
                var existingNotification = await _repository.GetByIdAsync(notification.Id);
                if (existingNotification != null)
                {
                    // Update the notification
                    existingNotification.Title = notification.Title;
                    existingNotification.MessageBody = notification.MessageBody;
                    existingNotification.IsScheduled = notification.IsScheduled;
                    existingNotification.ScheduledDate = notification.ScheduledDate;

                    await _repository.UpdateAsync(existingNotification);

                    // Send via Firebase
                    return await SendNotificationAsync(notification);
                }
                else
                {
                    // Create new notification if it doesn't exist
                    return await SendNotificationAsync(notification);
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "UpdateAndSendNotificationAsync");
                throw;
            }
        }

        public async Task<RegisteredDeviceResponseDto> CreateUpdateRegistrationAsync(DeviceRegistrationInfo registrationInfo)
        {
            try
            {
                _logger.Information($"FirebaseNotificationService::CreateUpdateRegistrationAsync() called, DeviceRegistrationInfo: {JsonConvert.SerializeObject(registrationInfo)}");

                if (string.IsNullOrWhiteSpace(registrationInfo.DeviceToken))
                {
                    return new RegisteredDeviceResponseDto() { IsSuccess = false, Error = "DeviceToken is required" };
                }

                // Check if device already exists
                var existingDevice = await _deviceRepository.FirstOrDefaultAsync(new DeviceByTokenSpec(registrationInfo.DeviceToken));

                if (existingDevice != null)
                {
                    // Update existing device
                    existingDevice.UserId = registrationInfo.UserId;
                    existingDevice.Platform = registrationInfo.Platform;
                    existingDevice.DeviceToken = registrationInfo.DeviceToken;
                    existingDevice.IsActive = true;
                    existingDevice.LastUpdated = DateTime.UtcNow;

                    await _deviceRepository.UpdateAsync(existingDevice);

                    return new RegisteredDeviceResponseDto()
                    {
                        IsSuccess = true,
                        DeviceId = existingDevice.Id,
                        Message = "Device updated successfully"
                    };
                }
                else
                {
                    // Create new device registration
                    var newDevice = new Device
                    {
                        Id = Guid.NewGuid(),
                        UserId = registrationInfo.UserId,
                        Platform = registrationInfo.Platform,
                        DeviceToken = registrationInfo.DeviceToken,
                        IsActive = true,
                        CreatedDate = DateTime.UtcNow,
                        LastUpdated = DateTime.UtcNow,
                        PinpointApplicationId = _firebaseSetting.project_id // Use Firebase project ID instead
                    };

                    await _deviceRepository.AddAsync(newDevice);

                    return new RegisteredDeviceResponseDto()
                    {
                        IsSuccess = true,
                        DeviceId = newDevice.Id,
                        Message = "Device registered successfully"
                    };
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "CreateUpdateRegistrationAsync");
                return new RegisteredDeviceResponseDto() { IsSuccess = false, Error = ex.Message };
            }
        }

        public async Task<bool> DeleteRegistrationAsync(Guid id)
        {
            try
            {
                var device = await _deviceRepository.GetByIdAsync(id);
                if (device != null)
                {
                    await _deviceRepository.DeleteAsync(device);
                    return true;
                }
                return false;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "DeleteRegistrationAsync");
                return false;
            }
        }

        public async Task<bool> SendTestNotificationAsync(List<string> deviceTokens, Application.PushNotification.SendPushNotificationRequestHandler.GcmMessage gcmMessage, List<Guid> deviceIds)
        {
            try
            {
                if (deviceTokens?.Any() != true)
                    return false;

                var app = InitializeFirebaseApp();
                var messaging = FirebaseMessaging.GetMessaging(app);

                foreach (var token in deviceTokens)
                {
                    if (!IsValidFCMToken(token))
                        continue;

                    var message = new Message()
                    {
                        Token = token,
                        Notification = new FirebaseAdmin.Messaging.Notification()
                        {
                            Title = gcmMessage.Title,
                            Body = gcmMessage.Body
                        },
                        Data = gcmMessage.Data ?? new Dictionary<string, string>()
                    };

                    try
                    {
                        await messaging.SendAsync(message);
                    }
                    catch (FirebaseMessagingException ex)
                    {
                        _logger.Warning($"Failed to send test notification to token {token}: {ex.MessagingErrorCode}");
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SendTestNotificationAsync");
                return false;
            }
        }

        public async Task<bool> SendTestNotificationAsync(List<Guid> userIds, Application.PushNotification.SendPushNotificationRequestHandler.GcmMessage gcmMessage, List<Guid> deviceIds = null)
        {
            try
            {
                if (userIds?.Any() != true)
                    return false;

                // Get device tokens for the specified users
                var devices = await _deviceRepository.ListAsync(new DevicesByUserIdsSpec(userIds));
                var deviceTokens = devices.Where(d => d.IsActive && !string.IsNullOrWhiteSpace(d.DeviceToken))
                                         .Select(d => d.DeviceToken)
                                         .ToList();

                return await SendTestNotificationAsync(deviceTokens, gcmMessage, deviceIds);
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SendTestNotificationAsync with userIds");
                return false;
            }
        }

        public async Task<bool> SendEmailNotification(EmailSenderDto? emailSenderDto)
        {
            _logger.Information($"FirebaseNotificationService -> SendEmailNotification -> emailSenderDto : {JsonConvert.SerializeObject(emailSenderDto, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");

            try
            {
                var globalSettings = (await _globalSettingsRepo.ListAsync()).FirstOrDefault();
                _logger.Information($"FirebaseNotificationService -> SendEmailNotification -> GlobalSettings : {JsonConvert.SerializeObject(globalSettings, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented })}");

                if (globalSettings != null)
                {
                    var notificationSettings = GetNotificationSettings(globalSettings);
                    if ((notificationSettings?.ChannelSettings?.IsEmailNotificationEnabled ?? false) && emailSenderDto != null)
                    {
                        if (globalSettings.IsGraphEmailEnabled)
                        {
                            return await _graphEmailService.SendEmailAsync(emailSenderDto);
                        }
                        else
                        {
                            return await _customEmailService.SendEmailAsync(emailSenderDto);
                        }
                    }
                }
                return false;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SendEmailNotification");
                return false;
            }
        }

        public async Task<bool> SendWATemplateNotification(string phoneNumber, WATemplate template, List<string> bodyValues, string? headerValue = null, bool isSavedMessage = false, Guid? leadId = null, string? leadName = null, string? campaignName = null, ViewLeadDto? leadDto = null, UserDetailsDto? assignUser = null, bool isLeadNotification = false, Guid userId = default)
        {
            try
            {
                // WhatsApp functionality remains the same as it doesn't depend on AWS Pinpoint
                // Implementation would be similar to the original service
                _logger.Information($"FirebaseNotificationService -> SendWATemplateNotification called for phone: {phoneNumber}");

                // This would use the existing WhatsApp service implementation
                // The actual implementation depends on your WhatsApp service setup
                return true; // Placeholder - implement based on your WhatsApp service
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SendWATemplateNotification");
                return false;
            }
        }

        public async Task<bool> SendWebNotificationAsync(NotificationDTO notification, string token)
        {
            try
            {
                if (string.IsNullOrEmpty(notification?.MessageBody))
                {
                    return false;
                }

                if (string.IsNullOrEmpty(token))
                {
                    return false;
                }

                // Validate token before sending notification
                if (!IsValidFCMToken(token))
                {
                    return false;
                }

                // Initialize Firebase Admin SDK
                var app = InitializeFirebaseApp();
                var messaging = FirebaseMessaging.GetMessaging(app);

                // Create FCM message
                var message = new Message()
                {
                    Token = token,
                    Notification = new FirebaseAdmin.Messaging.Notification()
                    {
                        Title = notification.Title,
                        Body = notification.MessageBody
                    }
                };

                // Send message using Firebase Admin SDK
                var response = await messaging.SendAsync(message);
                return !string.IsNullOrEmpty(response);
            }
            catch (FirebaseMessagingException ex)
            {
                // Handle specific Firebase messaging errors
                if (ex.MessagingErrorCode == MessagingErrorCode.Unregistered)
                {
                    // Token is invalid/unregistered - this is expected and should not be logged as error
                    return false;
                }
                else
                {
                    _logger.Error(ex, $"Firebase messaging error: {ex.MessagingErrorCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Exception in SendWebNotificationAsync: {ex.Message}");
                return false;
            }
        }

        public async Task<bool> SendCallNotificationAsync(NotificationDTO notification, string token)
        {
            try
            {
                if (string.IsNullOrEmpty(notification?.MessageBody))
                {
                    return false;
                }

                if (string.IsNullOrEmpty(token))
                {
                    return false;
                }

                // Validate token before sending notification
                if (!IsValidFCMToken(token))
                {
                    return false;
                }

                // Initialize Mobile Firebase Admin SDK
                var app = InitializeMobileFirebaseApp();
                var messaging = FirebaseMessaging.GetMessaging(app);

                // Create FCM message for mobile/call notification
                var message = new Message()
                {
                    Token = token,
                    Notification = new FirebaseAdmin.Messaging.Notification()
                    {
                        Title = notification.Title,
                        Body = notification.MessageBody
                    },
                    Data = new Dictionary<string, string>
                    {
                        ["title"] = notification.Title ?? "",
                        ["message"] = notification.MessageBody ?? "",
                        ["imageIconUrl"] = "https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/ic_launcher_round.png",
                        ["imageSmallIconUrl"] = "https://leadrat-resources-blob.s3.ap-south-1.amazonaws.com/images/ic_launcher_round.png",
                        ["deeplinkUrl"] = notification.FCMDeepLinkUrl ?? "",
                        ["NotificationUniqueId"] = notification.Id.ToString()
                    }
                };

                // Send message using Firebase Admin SDK
                var response = await messaging.SendAsync(message);
                return !string.IsNullOrEmpty(response);
            }
            catch (FirebaseMessagingException ex)
            {
                // Handle specific Firebase messaging errors
                if (ex.MessagingErrorCode == MessagingErrorCode.Unregistered)
                {
                    // Token is invalid/unregistered - this is expected and should not be logged as error
                    return false;
                }
                else
                {
                    _logger.Error(ex, $"Firebase messaging error in SendCallNotificationAsync: {ex.MessagingErrorCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex, $"Exception in SendCallNotificationAsync: {ex.Message}");
                return false;
            }
        }

        public async Task SendLeadUpdateToEngageto<T>(T entity, CancellationToken cancellationToken)
        {
            try
            {
                // Implementation for Engageto integration
                // This would remain the same as it doesn't depend on AWS Pinpoint
                _logger.Information($"FirebaseNotificationService -> SendLeadUpdateToEngageto called");

                // Placeholder implementation - implement based on your Engageto service
                await Task.CompletedTask;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SendLeadUpdateToEngageto");
            }
        }
        #endregion

        #region Private Helper Methods
        private async Task<bool> SendFirebaseNotificationsAsync(NotificationDTO notification)
        {
            try
            {
                // Get target devices based on notification criteria
                var targetDevices = await GetTargetDevicesAsync(notification);

                if (!targetDevices.Any())
                {
                    _logger.Information("No target devices found for notification");
                    return false;
                }

                var app = InitializeFirebaseApp();
                var messaging = FirebaseMessaging.GetMessaging(app);

                var successCount = 0;
                var totalCount = targetDevices.Count;

                foreach (var device in targetDevices)
                {
                    if (string.IsNullOrWhiteSpace(device.DeviceToken) || !IsValidFCMToken(device.DeviceToken))
                        continue;

                    try
                    {
                        var message = CreateFirebaseMessage(notification, device.DeviceToken);
                        var response = await messaging.SendAsync(message);

                        if (!string.IsNullOrEmpty(response))
                        {
                            successCount++;
                        }
                    }
                    catch (FirebaseMessagingException ex)
                    {
                        if (ex.MessagingErrorCode == MessagingErrorCode.Unregistered)
                        {
                            // Mark device as inactive
                            device.IsActive = false;
                            await _deviceRepository.UpdateAsync(device);
                        }
                        _logger.Warning($"Failed to send notification to device {device.Id}: {ex.MessagingErrorCode}");
                    }
                }

                _logger.Information($"Sent notifications to {successCount}/{totalCount} devices");
                return successCount > 0;
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "SendFirebaseNotificationsAsync");
                return false;
            }
        }

        private Message CreateFirebaseMessage(NotificationDTO notification, string deviceToken)
        {
            var message = new Message()
            {
                Token = deviceToken,
                Notification = new FirebaseAdmin.Messaging.Notification()
                {
                    Title = notification.Title,
                    Body = notification.MessageBody
                }
            };

            // Add custom data if available
            if (!string.IsNullOrEmpty(notification.FCMRawContent))
            {
                try
                {
                    var customData = JsonConvert.DeserializeObject<Dictionary<string, string>>(notification.FCMRawContent);
                    if (customData != null)
                    {
                        message.Data = customData;
                    }
                }
                catch (Exception ex)
                {
                    _logger.Warning($"Failed to parse FCMRawContent: {ex.Message}");
                }
            }

            return message;
        }

        private async Task<List<Device>> GetTargetDevicesAsync(NotificationDTO notification)
        {
            try
            {
                if (notification.UserIds?.Any() == true)
                {
                    // Target specific users
                    return await _deviceRepository.ListAsync(new DevicesByUserIdsSpec(notification.UserIds));
                }
                else if (notification.Topics?.Any() == true)
                {
                    // Target users by topics - this would need to be implemented based on your topic system
                    // For now, return empty list as topic-based targeting needs custom implementation
                    return new List<Device>();
                }
                else
                {
                    // Broadcast to all active devices
                    return await _deviceRepository.ListAsync(new ActiveDevicesSpec());
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "GetTargetDevicesAsync");
                return new List<Device>();
            }
        }

        private async Task<List<PushNotificationRecords>> CreateNotificationRecordsAsync(NotificationDTO notification)
        {
            var records = new List<PushNotificationRecords>();

            try
            {
                if (notification.UserIds?.Any() == true)
                {
                    foreach (var userId in notification.UserIds)
                    {
                        records.Add(new PushNotificationRecords
                        {
                            Id = Guid.NewGuid(),
                            NotificationId = notification.Id,
                            UserId = userId,
                            CampaignId = notification.UniqueId?.ToString() ?? string.Empty,
                            CreatedDate = DateTime.UtcNow,
                            Status = "Sent"
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "CreateNotificationRecordsAsync");
            }

            return records;
        }

        private async Task<List<Notification>> GetUpdatedNotificationsAsync(NotificationDTO notificationDto, List<PushNotificationRecords> records)
        {
            var notifications = new List<Notification>();

            try
            {
                var userIds = notificationDto.UserIds ?? new List<Guid>();
                if (!userIds.Any() && records.Any())
                {
                    userIds = records.Select(r => r.UserId).ToList();
                }

                foreach (var userId in userIds)
                {
                    var notification = notificationDto.Adapt<Notification>();
                    notification.Id = Guid.NewGuid();
                    notification.UserId = userId;
                    notification.UniqueId = notificationDto.UniqueId;
                    notification.SentToPinpoint = true; // Keep for backward compatibility
                    notification.Created = DateTime.UtcNow;

                    notifications.Add(notification);
                }

                if (!notifications.Any())
                {
                    // Create at least one notification record
                    var notification = notificationDto.Adapt<Notification>();
                    notification.Id = Guid.NewGuid();
                    notification.UniqueId = notificationDto.UniqueId;
                    notification.SentToPinpoint = true;
                    notification.Created = DateTime.UtcNow;
                    notifications.Add(notification);
                }
            }
            catch (Exception ex)
            {
                await LogErrorAsync(ex, "GetUpdatedNotificationsAsync");
            }

            return notifications;
        }

        private bool IsValidFCMToken(string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                return false;

            // Basic FCM token validation
            if (token.Length < 140 || token.Length > 200)
                return false;

            // FCM tokens should contain only alphanumeric characters, hyphens, underscores, and colons
            if (!System.Text.RegularExpressions.Regex.IsMatch(token, @"^[a-zA-Z0-9_:-]+$"))
                return false;

            return true;
        }

        private FirebaseApp InitializeFirebaseApp()
        {
            // Check if web app already exists
            var existingApp = FirebaseApp.GetInstance("web-app");
            if (existingApp != null)
            {
                return existingApp;
            }

            // App doesn't exist, create it
            var firebaseFile = JsonConvert.SerializeObject(_firebaseSetting);
            using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(firebaseFile));

            var credential = GoogleCredential.FromStream(memoryStream);
            var options = new AppOptions()
            {
                Credential = credential,
                ProjectId = _firebaseSetting.project_id
            };

            return FirebaseApp.Create(options, "web-app");
        }

        private FirebaseApp InitializeMobileFirebaseApp()
        {
            // Check if mobile app already exists
            var existingApp = FirebaseApp.GetInstance("mobile-app");
            if (existingApp != null)
            {
                return existingApp;
            }

            // App doesn't exist, create it
            var firebaseFile = JsonConvert.SerializeObject(_mobileFirebaseSetting);
            using var memoryStream = new MemoryStream(Encoding.UTF8.GetBytes(firebaseFile));

            var credential = GoogleCredential.FromStream(memoryStream);
            var options = new AppOptions()
            {
                Credential = credential,
                ProjectId = _mobileFirebaseSetting.project_id
            };

            return FirebaseApp.Create(options, "mobile-app");
        }

        private NotificationSettings GetNotificationSettings(GlobalSettings globalSettings)
        {
            return new NotificationSettings
            {
                ChannelSettings = new ChannelSettings
                {
                    IsEmailNotificationEnabled = globalSettings.IsEmailNotificationEnabled,
                    IsWebNotificationEnabled = globalSettings.IsWebNotificationEnabled,
                    IsPushNotificationEnabled = globalSettings.IsPushNotificationEnabled,
                    IsSMSNotificationEnabled = globalSettings.IsSMSNotificationEnabled,
                    IsWhatsAppNotificationEnabled = globalSettings.IsWhatsAppNotificationEnabled
                }
            };
        }

        private async Task LogErrorAsync(Exception ex, string context)
        {
            try
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = $"FirebaseNotificationService -> {context}"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
                _logger.Error(ex, $"FirebaseNotificationService -> {context}: {ex.Message}");
            }
            catch
            {
                // Fallback logging if repository fails
                _logger.Error(ex, $"FirebaseNotificationService -> {context}: {ex.Message}");
            }
        }
        #endregion
        public Task<Guid> CreateNotificationAsync(NotificationDTO notification)
        {
            throw new NotImplementedException();
        }

        public Task<RegisteredDeviceResponseDto> CreateUpdateRegistrationAsync(DeviceRegistrationInfo registrationInfo)
        {
            throw new NotImplementedException();
        }

        public Task<bool> DeleteRegistrationAsync(Guid id)
        {
            throw new NotImplementedException();
        }

        public Task<bool> SendCallNotificationAsync(NotificationDTO notification, string token)
        {
            throw new NotImplementedException();
        }

        public Task<bool> SendEmailNotification(EmailSenderDto? emailSenderDto)
        {
            throw new NotImplementedException();
        }

        public Task SendLeadUpdateToEngageto<T>(T entity, CancellationToken cancellationToken)
        {
            throw new NotImplementedException();
        }

        public Task<Guid> SendNotificationAsync(NotificationDTO notification)
        {
            throw new NotImplementedException();
        }

        public Task<bool> SendTestNotificationAsync(List<string> deviceToken, SendPushNotificationRequestHandler.GcmMessage gcmMessage, List<Guid> deviceIds)
        {
            throw new NotImplementedException();
        }

        public Task<bool> SendTestNotificationAsync(List<Guid> userIds, SendPushNotificationRequestHandler.GcmMessage gcmMessage, List<Guid> deviceIds = null)
        {
            throw new NotImplementedException();
        }

        public Task<bool> SendWATemplateNotification(string phoneNumber, WATemplate template, List<string> bodyValues, string? headerValue = null, bool isSavedMessage = false, Guid? leadId = null, string? leadName = null, string? campaignName = null, ViewLeadDto? leadDto = null, UserDetailsDto? assignUser = null, bool isLeadNotification = false, Guid userId = default)
        {
            throw new NotImplementedException();
        }

        public Task<bool> SendWebNotificationAsync(NotificationDTO notification, string token)
        {
            throw new NotImplementedException();
        }

        public Task<Guid> UpdateAndSendNotificationAsync(NotificationDTO notification)
        {
            throw new NotImplementedException();
        }
    }
}
