﻿using Amazon.Pinpoint.Model;
using Lrb.Application.Email.Web.Dtos;
using Lrb.Application.Identity.Users;
using Lrb.Application.Lead.Web;
using Lrb.Application.Notifications.Dtos;

namespace Lrb.Application.Common.PushNotification
{
    public interface INotificationService
    {
        Task<Guid> CreateNotificationAsync(NotificationDTO notification);
        Task<Guid> SendNotificationAsync(NotificationDTO notification);
        Task<Guid> UpdateAndSendNotificationAsync(NotificationDTO notification);
        Task<RegisteredDeviceResponseDto> CreateUpdateRegistrationAsync(DeviceRegistrationInfo registrationInfo);
        Task<bool> DeleteRegistrationAsync(Guid id);
        Task<bool> SendTestNotificationAsync(List<string> deviceToken, Application.PushNotification.SendPushNotificationRequestHandler.GcmMessage gcmMessage, List<Guid> deviceIds);
        Task<bool> SendTestNotificationAsync(List<Guid> userIds, Application.PushNotification.SendPushNotificationRequestHandler.GcmMessage gcmMessage, List<Guid> deviceIds = null);
        public Task<bool> SendEmailNotification(EmailSenderDto? emailSenderDto);
        Task<bool> SendWATemplateNotification(string phoneNumber, WATemplate template, List<string> bodyValues, string? headerValue = null, bool isSavedMessage = false, Guid? leadId = null, string? leadName = null, string? campaignName = null, ViewLeadDto? leadDto = null, Application.Identity.Users.UserDetailsDto? assignUser = null, bool isLeadNotification = false, Guid userId = default);
        //Task<bool> SendWATemplateNotification(string phoneNumber, WATemplate template, List<string> bodyValues, string? headerValue = null, bool isSavedMessage = false, Guid? leadId = null, string? leadName = null, string? campaignName = null, ViewLeadDto? leadDto = null, Application.Identity.Users.UserDetailsDto? assignUser = null);
        Task<bool> SendWebNotificationAsync(NotificationDTO notification, string token);
        Task<bool> SendCallNotificationAsync(NotificationDTO notification, string token);
    }
}
