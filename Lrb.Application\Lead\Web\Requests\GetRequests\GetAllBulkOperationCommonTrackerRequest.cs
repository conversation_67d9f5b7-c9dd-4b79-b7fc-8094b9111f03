﻿using Lrb.Application.Lead.Web.Specs;

namespace Lrb.Application.Lead.Web
{
    public class GetAllBulkOperationCommonTrackerRequest : PaginationFilter, IRequest<PagedResponse<BulkOperationCommonTrackerDto, string>>
    {
        public string? Type { get; set; }
    }

    public class GetAllBulkOperationCommonTrackerRequestHandler : IRequestHandler<GetAllBulkOperationCommonTrackerRequest, PagedResponse<BulkOperationCommonTrackerDto, string>>
    {
        private readonly IRepositoryWithEvents<BulkCommonTracker> _commonTrackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllBulkOperationCommonTrackerRequestHandler(IRepositoryWithEvents<BulkCommonTracker> commonTrackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _commonTrackerRepo = commonTrackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<BulkOperationCommonTrackerDto, string>> Handle(GetAllBulkOperationCommonTrackerRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var allLeadTrackers = await _commonTrackerRepo.ListAsync(new GetAllBulkOperationCommonTrackerSpecs(request, subIds), cancellationToken);
            var count = await _commonTrackerRepo.CountAsync(new GetAllBulkOperationCommonTrackerCountSpecs(request.Type, subIds));
            var allLeadtrackerDtos = allLeadTrackers.Adapt<List<BulkOperationCommonTrackerDto>>();
            return new(allLeadtrackerDtos, count);
        }
    }
}
