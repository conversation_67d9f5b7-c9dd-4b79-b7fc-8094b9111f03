﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Lrb.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.AutoDialer
{
    public class AutoDialerConfigurationConfig : IEntityTypeConfiguration<Domain.Entities.AutoDialerConfiguration>
    {
        public void Configure(EntityTypeBuilder<Domain.Entities.AutoDialerConfiguration> builder)
        {
            builder.IsMultiTenant();
        }
    }
}
