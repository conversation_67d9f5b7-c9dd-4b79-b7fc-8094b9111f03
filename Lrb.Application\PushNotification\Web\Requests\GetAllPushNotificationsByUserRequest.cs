﻿using Lrb.Application.PushNotification.Web.Dtos;
using Lrb.Application.PushNotification.Web.Specs;

namespace Lrb.Application.PushNotification.Web.Requests
{
    public class GetAllPushNotificationsByUserRequest : PaginationFilter, IRequest<Response<PushNotificationWrapperDto>>
    {

    }
    public class GetAllPushNotificationsByUserIdRequestHandler : IRequestHandler<GetAllPushNotificationsByUserRequest, Response<PushNotificationWrapperDto>>
    {
        private readonly IRepositoryWithEvents<Notification> _notificationRepo;
        private readonly IRepositoryWithEvents<PushNotificationRecords> _pushNotificationRecordsRepo;
        private readonly ICurrentUser _currentUserRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<LocalNotification> _localNotificationRepo;
        public GetAllPushNotificationsByUserIdRequestHandler(IRepositoryWithEvents<Notification> notificationRepo,
                                                     IRepositoryWithEvents<PushNotificationRecords> pushNotificationRecordsRepo,
                                                     ICurrentUser currentUserRepo,
                                                     IDapperRepository dapperRepository,
                                                     IRepositoryWithEvents<LocalNotification> localNotificationRepo)
        {
            _notificationRepo = notificationRepo;
            _pushNotificationRecordsRepo = pushNotificationRecordsRepo;
            _currentUserRepo = currentUserRepo;
            _dapperRepository = dapperRepository;
            _localNotificationRepo = localNotificationRepo;
        }
        public async Task<Response<PushNotificationWrapperDto>> Handle(GetAllPushNotificationsByUserRequest request, CancellationToken cancellationToken)
        {
            Guid currentUserId = _currentUserRepo.GetUserId();
            var tenantId = _currentUserRepo.GetTenant();
            List<PushNotificationRecordsDto> records = new();
            List<NotificationDto> notifications = await _notificationRepo.ListAsync(new GetNotificationsByUserIdSpecV1(request, currentUserId), cancellationToken);
            var notificationsCount = await _dapperRepository.GetTotalNotificationsCountAsync(currentUserId, tenantId);
            int notOpenedNotificationsCount = await _dapperRepository.GetTotalUnOpenedNotificationsCountAsync(currentUserId, tenantId);
            records = await _pushNotificationRecordsRepo.ListAsync(new GetPushNotificationRecordsWithoutPaginationSpecV1(notifications.Select(i => i.UniqueId ?? Guid.Empty).ToList(), currentUserId), cancellationToken);
            records = records.DistinctBy(i => i.NotificationUniqueId).ToList();
            //List<LocalNotification>? localNotifications = await _localNotificationRepo.ListAsync(new GetLocalNotificationsByUserSpec(currentUserId), cancellationToken);
            if ((!records?.Any() ?? true))
            {
                return new()
                {
                    Message = "No Notification Records Found!",
                    Succeeded = true,
                };
            }
            List<PushNotificationDto> dtos = new();
            List<PushNotificationDto> notificationDtos = new();
            if ((notifications?.Any() ?? false) && (records?.Any() ?? false))
            {
                PushNotificationDto dto = new();
                foreach (var record in records)
                {
                    var notification = notifications.Where(i => i.UniqueId == record.NotificationUniqueId).FirstOrDefault();
                    var deepLinkUrl = notification?.FCMDeepLinkUrl ?? string.Empty;
                    Guid guidLeadId = GetGuidFromDeepLinkUrl(deepLinkUrl);
                    dtos.Add(new PushNotificationDto()
                    {
                        DeliveredTime = record.DeliveredAt,
                        IsOpened = record.IsOpened,
                        Description = notification?.MessageBody ?? string.Empty,
                        FCMDeepLinkUrl = notification?.FCMDeepLinkUrl ?? string.Empty,
                        Title = notification?.Title ?? string.Empty,
                        LeadId = guidLeadId,
                        NotificationUniqueid = notification?.UniqueId ?? Guid.Empty,
                        IsDelivered = record.IsDelivered,
                        CreatedOn = record.CreatedOn,
                        IsLocalNotification = false
                    });
                }
            }
            //if (localNotifications?.Any() ?? false)
            //{
            //    List<PushNotificationDto>? localPushNotificationDtos = localNotifications.Adapt<List<PushNotificationDto>>();
            //    localPushNotificationDtos.ForEach(i => i.IsLocalNotification = true);
            //    dtos.AddRange(localPushNotificationDtos);
            //    dtos = dtos.OrderByDescending(i => i.CreatedOn).ToList();
            //    notificationsCount = localPushNotificationDtos.Any() ? (notificationsCount + localPushNotificationDtos.Count) : notificationsCount;
            //}
            if (dtos.Any())
            {
                return new()
                {
                    Data = new()
                    {
                        Items = dtos,
                        TotalCount = notificationsCount,
                        NotOpenedCount = notOpenedNotificationsCount
                    },
                    Succeeded = true,
                };
            }
            else
            {
                return new()
                {
                    Succeeded = false,
                    Message = "No Notifications Found!",
                };
            }
        }
        public static Guid GetGuidFromDeepLinkUrl(string? deepLinkUrl)
        {
            Guid guidLeadId = default;
            if (!string.IsNullOrWhiteSpace(deepLinkUrl) && deepLinkUrl.Contains("id"))
            {
                var leadId = deepLinkUrl.Split('&').Where(i => i.Contains("id=")).FirstOrDefault();
                leadId = leadId != null ? leadId.Substring(3) : leadId;
                guidLeadId = !string.IsNullOrWhiteSpace(leadId) ? Guid.TryParse(leadId, out Guid result) ? result : Guid.Empty : Guid.Empty;
            }
            return guidLeadId;
        }
    }
}
