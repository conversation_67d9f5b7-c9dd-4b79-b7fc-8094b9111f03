﻿using Lrb.Application.ExportTemplate;
using Lrb.Application.Identity.Users;
using Lrb.Application.Property.Web.Dtos;
using Lrb.Application.Property.Web.Requests;
using Lrb.Application.Property.Web.Specs;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Entities.User;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetUserReportTrackerRequest : PaginationFilter, IRequest<PagedResponse<ExportUserTrackerDto, string>>
    {
        
    }
    public class GetUserReportTrackerRequesthandler : IRequestHandler<GetUserReportTrackerRequest, PagedResponse<ExportUserTrackerDto, string>>
    {
        private readonly IReadRepository<ExportUserTracker> _exportUserTrackerRepo;
        public readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetUserReportTrackerRequesthandler(IReadRepository<ExportUserTracker> exportUserTrackerRepo,
            IUserService userService, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _exportUserTrackerRepo = exportUserTrackerRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<ExportUserTrackerDto, string>> Handle(GetUserReportTrackerRequest request, CancellationToken cancellationToken) 
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var userTracker = await _exportUserTrackerRepo.ListAsync(new ExportUseTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _exportUserTrackerRepo.CountAsync(new GetExportUserTrackersCountSpec(subIds), cancellationToken);
            var UserTrackerDto = userTracker.Adapt<List<ExportUserTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(UserTrackerDto.Select(i => i.CreatedBy.ToString()).ToList(), cancellationToken);
            foreach (var userTrackers in UserTrackerDto)
            {
                userTrackers.ExportTemplate = (JsonConvert.DeserializeObject<ViewExportTemplateDto>(userTrackers?.Template ?? string.Empty) ?? null);
                userTrackers.ExportedUser = users?.FirstOrDefault(i => userTrackers.CreatedBy == i.Id)?.Adapt<Team.Web.UserDto>();
            }
            return new(UserTrackerDto, totalCount);
        }
    }
 }