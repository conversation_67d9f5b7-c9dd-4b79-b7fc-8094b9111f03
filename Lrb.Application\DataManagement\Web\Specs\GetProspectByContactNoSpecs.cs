﻿using DocumentFormat.OpenXml.Office2010.Excel;

namespace Lrb.Application.DataManagement.Web.Specs
{
    public class GetProspectByContactNoSpecs : Specification<Lrb.Domain.Entities.Prospect>
    {
        public GetProspectByContactNoSpecs(string contactNo)
        {
            Query.Where(i => i.ContactNo.Contains(contactNo) && !i.IsArchived && !i.Is<PERSON>eleted);
        }

        public GetProspectByContactNoSpecs(string contactNo, Guid id)
        {
            Query.Where(i => i.ContactNo.Contains(contactNo) && !i.IsArchived && !i.IsDeleted);
        }
        public GetProspectByContactNoSpecs(string contactNo, string countryCode)
        {
            string formattedContactNo = contactNo.StartsWith(countryCode) ? contactNo.Substring(countryCode.Length) : countryCode + contactNo;

            Query.Where(i => !i.IsDeleted &&
                            !i.IsArchived && 
                            (i.ContactNo != null && ( i.ContactNo.Contains(formattedContactNo)))
                            || 
                            (i.AlternateContactNo != null && (i.AlternateContactNo.Contains(formattedContactNo))));
        }
        public GetProspectByContactNoSpecs(List<string> contactNos)
        {
            Query.Where(i => !i.IsDeleted &&
                            !i.IsArchived &&
                            (i.ContactNo != null && (contactNos.Contains(i.ContactNo)))
                            ||
                            (i.AlternateContactNo != null && (contactNos.Contains(i.AlternateContactNo))));
        }
        public class GetDuplicateProspectByContactNoSpecs : Specification<Lrb.Domain.Entities.Prospect>
        {
            public GetDuplicateProspectByContactNoSpecs(string contactNo)
            {
                Query.Where(i => i.ContactNo == contactNo).Where(i => !i.IsDeleted)
                .Where(i => !i.IsArchived)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.Addresses)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyType)
                .Include(i => i.Projects)
                .Include(i => i.Properties)
                .Include(i => i.Agencies)
                .Include(i => i.Enquiries)
                .ThenInclude(i => i.PropertyTypes);
            }
        }
        public class GetLeadByContactNoSpecs : Specification<Domain.Entities.Lead>
        {
            public GetLeadByContactNoSpecs(string contactNo, string countryCode)
            {
                string formattedContactNo = contactNo.StartsWith(countryCode) ? contactNo.Substring(countryCode.Length) : countryCode + contactNo;

                Query.Where(i => !i.IsDeleted
                    && !i.IsArchived
                    && ((i.ContactNo != null && (i.ContactNo.Contains(contactNo) || i.ContactNo.Contains(formattedContactNo)))
                            ||
                            (i.AlternateContactNo != null && (i.AlternateContactNo.Contains(contactNo) || i.AlternateContactNo.Contains(formattedContactNo)))
                       ));
            }
        }

        
    }
}
