﻿using Lrb.Application.Property.Web.Specs;

namespace Lrb.Application.Property.Web
{
    public class GetAllBulkUploadTrackersRequest : PaginationFilter, IRequest<PagedResponse<BulkPropertyUploadTracker, string>>
    {
    }
    public class GetAllBulkUploadTrackersRequestHandler : IRequestHandler<GetAllBulkUploadTrackersRequest, PagedResponse<BulkPropertyUploadTracker, string>>
    {
        private readonly IRepositoryWithEvents<BulkPropertyUploadTracker> _trackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetAllBulkUploadTrackersRequestHandler(IRepositoryWithEvents<BulkPropertyUploadTracker> trackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _trackerRepo = trackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<BulkPropertyUploadTracker, string>> Handle(GetAllBulkUploadTrackersRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var trackers = await _trackerRepo.ListAsync(new GetAllBulkPropertyTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new GetBulkPropertyTrackerCountSpec(subIds), cancellationToken);
            return new(trackers, totalCount);
        }
    }
}
