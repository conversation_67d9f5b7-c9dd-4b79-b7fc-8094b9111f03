﻿using Lrb.Application.Property.Web.Specs;
using Lrb.Application.Property.Web.V2.Dtos;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.MasterData;

namespace Lrb.Application.Property.Web.V2.Requests
{
    public class GetPropertyListingByIdRequestV2 : IRequest<Response<ViewPropertyDtoV2>>
    {
        public Guid Id { get; set; }
        public GetPropertyListingByIdRequestV2(Guid id)
        {
            Id = id;
        }
    }

    public class GetPropertyListingByIdRequestV2Handler : IRequestHandler<GetPropertyListingByIdRequestV2, Response<ViewPropertyDtoV2>>
    {
        private readonly IReadRepository<Domain.Entities.Property> _propertyRepo;
        private readonly IReadRepository<CustomMasterAttribute> _masterAttributeRepo;
        private readonly IReadRepository<PropertyAmenity> _amenityRepo;
        public GetPropertyListingByIdRequestV2Handler(
            IReadRepository<Domain.Entities.Property> propertyRepo,  
            IReadRepository<CustomMasterAttribute> masterAttributeRepo,
            IReadRepository<PropertyAmenity> amenityRepo)
        {
            _propertyRepo = propertyRepo;
            _masterAttributeRepo = masterAttributeRepo;
            _amenityRepo = amenityRepo;
        }

        public async Task<Response<ViewPropertyDtoV2>> Handle(GetPropertyListingByIdRequestV2 request, CancellationToken cancellationToken)
        {
            var property = (await _propertyRepo.FirstOrDefaultAsync(new PropertyByIdSpecV2(request.Id), cancellationToken));
            if (property == null) { throw new NotFoundException("No Property found by this Id."); }
            var propertyDto = property.Adapt<ViewPropertyDtoV2>();
            var amenities = await _amenityRepo.ListAsync(new PropertyAmenitiesByIdSpecV2(request.Id), cancellationToken);
            if (property != null && (amenities?.Any() ?? false))
            {
                propertyDto.Amenities = amenities.Select(i => i.MasterPropertyAmenityId).ToList();
            }
            if (property?.Attributes?.Any() ?? false)
            {
                List<Guid> attributeIds = property.Attributes.Select(i => i.MasterPropertyAttributeId).ToList();
                var masterAttributes = await _masterAttributeRepo.ListAsync(new CustomMasterAttributesByIdsSpec(attributeIds), cancellationToken);
                propertyDto = await UpdateMasterAttributes(propertyDto, masterAttributes);
            }
            return new(propertyDto);
        }
        private async static Task<ViewPropertyDtoV2> UpdateMasterAttributes(ViewPropertyDtoV2 propertyDto, List<CustomMasterAttribute> masterAttributes)
        {
            try
            {
                var attributes = propertyDto.Attributes?.ToList();
                attributes?.ForEach(i =>
                {
                    var matchingAttribute = masterAttributes.FirstOrDefault(j => j.Id == i.MasterPropertyAttributeId);
                    i.AttributeDisplayName = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeDisplayName;
                    i.AttributeName = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeName;
                    i.AttributeType = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.AttributeType;
                    i.ActiveImageURL = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.ActiveImageURL;
                    i.IsActive = masterAttributes.Where(j => j.Id == i.MasterPropertyAttributeId)?.FirstOrDefault()?.IsActive;

                });
                propertyDto.Attributes = attributes;
                return propertyDto;
            }
            catch
            {
                return propertyDto;
            }
        }
    }
}
