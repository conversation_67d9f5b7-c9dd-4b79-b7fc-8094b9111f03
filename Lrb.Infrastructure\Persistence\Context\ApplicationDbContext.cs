using Dapper;
using Finbuckle.MultiTenant;
using Lrb.Application.Common.Events;
using Lrb.Application.Common.Interfaces;
using Lrb.Domain.Common.Contracts;
using Lrb.Domain.Entities;
using Lrb.Domain.Entities.AmenitiesAttributes;
using Lrb.Domain.Entities.Api_Metrics;
using Lrb.Domain.Entities.Attendance;
using Lrb.Domain.Entities.Automation;
using Lrb.Domain.Entities.CustomAddress;
using Lrb.Domain.Entities.CustomEmail;
using Lrb.Domain.Entities.DataManagement;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.Icons;
using Lrb.Domain.Entities.Integration;
using Lrb.Domain.Entities.Integration.GoogleSheet;
using Lrb.Domain.Entities.LeadGenRequests;
using Lrb.Domain.Entities.Marketing;
using Lrb.Domain.Entities.MasterData;
using Lrb.Domain.Entities.MasterData.Location;
using Lrb.Domain.Entities.NewFolder;
using Lrb.Domain.Entities.Templates.QRForm;
using Lrb.Domain.Entities.User;
using Lrb.Domain.Entities.WhatsApp;
using Lrb.Domain.Enums;
using Lrb.Infrastructure.Auditing;
using Lrb.Infrastructure.Identity;
using Lrb.Infrastructure.Multitenancy;
using Lrb.Infrastructure.OpenApi;
using Lrb.Infrastructure.Persistence.Configuration;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Options;
using Newtonsoft.Json;
using Npgsql;

namespace Lrb.Infrastructure.Persistence.Context;

public class ApplicationDbContext : BaseDbContext
{
    private TenantDbContext _tenanatDbContext;
    private bool _mobileCatalogDetailProcessed = false;
    public ApplicationDbContext(ITenantInfo currentTenant, DbContextOptions<ApplicationDbContext> options, ICurrentUser currentUser, ISerializerService serializer, IOptions<DatabaseSettings> dbSettings, IEventPublisher events, IServiceProvider serviceProvider, TenantDbContext tenanatDbContext)
        : base(currentTenant, options, currentUser, serializer, dbSettings, events, serviceProvider)
    {
        _tenanatDbContext = tenanatDbContext;
    }
    #region MasterData
    public DbSet<MasterAreaUnit> MasterAreaUnits => Set<MasterAreaUnit>();
    public DbSet<MasterBuilderInfo> MasterBuilderInfos => Set<MasterBuilderInfo>();
    public DbSet<MasterLeadSource> MasterLeadSources => Set<MasterLeadSource>();
    public DbSet<MasterLeadStatus> MasterLeadStatuses => Set<MasterLeadStatus>();
    public DbSet<MasterPropertyAmenity> MasterPropertyAmenities => Set<MasterPropertyAmenity>();
    public DbSet<MasterPropertyAttribute> MasterPropertyAttributes => Set<MasterPropertyAttribute>();
    public DbSet<MasterPropertyType> MasterPropertyTypes => Set<MasterPropertyType>();
    public DbSet<MasterUserService> MasterUserServices => Set<MasterUserService>();
    public DbSet<MasterWhatsAppTemplate> MasterWhatsAppTemplates => Set<MasterWhatsAppTemplate>();
    public DbSet<CustomMasterLeadStatus> CustomMasterLeadStatuses => Set<CustomMasterLeadStatus>();
    public DbSet<MasterProspectSource> MasterProspectSources => Set<MasterProspectSource>();
    public DbSet<MasterProspectStatus> MasterPrsoepctStatuses => Set<MasterProspectStatus>();
    public DbSet<MasterLeadForm> MasterLeadForms => Set<MasterLeadForm>();
    public DbSet<CustomMasterQRform> CustomMasterQRForms => Set<CustomMasterQRform>();
    public DbSet<MasterProjectAmenity> MasterProjectAmenities => Set<MasterProjectAmenity>();
    public DbSet<MasterProjectType> MasterProjectTypes => Set<MasterProjectType>();
    public DbSet<MasterProjectUnitArrtibute> MasterProjectUnitArrtibutes => Set<MasterProjectUnitArrtibute>();
    public DbSet<MasterAssociatedBank> MasterAssociatedBanks => Set<MasterAssociatedBank>();
    public DbSet<MasterEmailInfo> MasterEmailInfos => Set<MasterEmailInfo>();
    public DbSet<MasterAddressDirectory> MasterAddressDirectories => Set<MasterAddressDirectory>();
    public DbSet<CustomMasterAmenity> CustomMasterAmenities => Set<CustomMasterAmenity>();
    public DbSet<CustomMasterAttribute> CustomMasterAttributes => Set<CustomMasterAttribute>();

    #endregion
    #region Common
    public DbSet<Address> Addresses => Set<Address>();
    public DbSet<TempVariable> TempVariables => Set<TempVariable>();
    public DbSet<BulkCommonTracker> BulkCommonTrackers => Set<BulkCommonTracker>();
    #endregion
    #region Property
    public DbSet<Property> Properties => Set<Property>();
    public DbSet<PropertyAmenity> PropertyAmenities => Set<PropertyAmenity>();
    public DbSet<PropertyAttribute> PropertyAttributes => Set<PropertyAttribute>();
    public DbSet<PropertyGallery> PropertyGalleries => Set<PropertyGallery>();
    public DbSet<PropertyDimension> PropertyDimensions => Set<PropertyDimension>();
    public DbSet<PropertyMonetaryInfo> PropertyMonetaryInfo => Set<PropertyMonetaryInfo>();
    public DbSet<PropertyOwnerDetails> PropertyOwnerDetails => Set<PropertyOwnerDetails>();
    public DbSet<PropertyTagInfo> PropertyTagInfo => Set<PropertyTagInfo>();
    public DbSet<BulkPropertyUploadTracker> BulkPropertyUploadTracker => Set<BulkPropertyUploadTracker>();
    public DbSet<ExportPropertyTracker> ExportPropertyTrackers => Set<ExportPropertyTracker>();
    public DbSet<PropertyAssignment> PropertyAssignments => Set<PropertyAssignment>();
    public DbSet<AminityCategory> AminityCategories => Set<AminityCategory>();
    public DbSet<ListingCompliance> ListingCompliances => Set<ListingCompliance>();

    #endregion
    #region Todo
    public DbSet<Todo> Todos => Set<Todo>();
    public DbSet<TodoHistory> TodoHistories => Set<TodoHistory>();
    #endregion
    #region Lead
    public DbSet<Lead> Leads => Set<Lead>();
    public DbSet<LeadEnquiry> LeadEnquiries => Set<LeadEnquiry>();
    public DbSet<LeadTag> LeadTags => Set<LeadTag>();
    public DbSet<LeadHistory> LeadHistories => Set<LeadHistory>();
    public DbSet<BulkLeadUploadTracker> BulkLeadUploadTracker => Set<BulkLeadUploadTracker>();
    public DbSet<LeadCallLog> LeadCallLogs => Set<LeadCallLog>();
    public DbSet<ExportLeadTracker> ExportLeadTracker => Set<ExportLeadTracker>();
    public DbSet<LeadAppointment> LeadAppointments => Set<LeadAppointment>();
    public DbSet<DuplicateLeadFeatureInfo> DuplicateFeatureInfo => Set<DuplicateLeadFeatureInfo>();
    public DbSet<LeadGenRequest> LeadGenRequests => Set<LeadGenRequest>();
    public DbSet<ValidatedExcel> ValidatedExcels => Set<ValidatedExcel>();
    public DbSet<LeadCommunication> LeadCommunications => Set<LeadCommunication>();
    public DbSet<LeadMigrateTracker> LeadMigrateTracker => Set<LeadMigrateTracker>();
    public DbSet<LeadBookedDetail> LeadBookedDetails => Set<LeadBookedDetail>();
    public DbSet<LeadsAssignRotationInfo> LeadsAssignRotationInfo => Set<LeadsAssignRotationInfo>();
    public DbSet<LeadRotationConfiguration> LeadRotationConfigurations => Set<LeadRotationConfiguration>();
    public DbSet<LeadRotationTracker> LeadRotationTrackers => Set<LeadRotationTracker>();

    public DbSet<LeadBrokerageInfo> LeadBrokerageInfo => Set<LeadBrokerageInfo>();
    public DbSet<Document> Documents => Set<Document>();
    public DbSet<LeadAssignment> LeadAssignments => Set<LeadAssignment>();
    public DbSet<MobileCatalogDetail> MobileCatalogDetails => Set<MobileCatalogDetail>();

    #endregion
    #region Team 
    public DbSet<Team> Teams => Set<Team>();
    public DbSet<UserTeam> UserTeams => Set<UserTeam>();
    public DbSet<TeamConfiguration> TeamConfigurations => Set<TeamConfiguration>();
    #endregion
    #region Integration
    public DbSet<FacebookAdLeadgenInfo> FacebookAdLeadgenInfo => Set<FacebookAdLeadgenInfo>();
    public DbSet<FacebookAccountsInfo> FacebookAccountsInfo => Set<FacebookAccountsInfo>();
    public DbSet<FacebookFormInfo> FacebookFormInfo => Set<FacebookFormInfo>();
    public DbSet<GmailHistoryDateTime> GmailHistoryDateTime => Set<GmailHistoryDateTime>();
    public DbSet<GmailIntegrationData> GmailIntegrationData => Set<GmailIntegrationData>();
    public DbSet<IntegrationAccountInfo> IntegrationAccountInfo => Set<IntegrationAccountInfo>();
    public DbSet<IntegrationLeadInfo> IntegrationLeadInfo => Set<IntegrationLeadInfo>();
    public DbSet<IVRCallLog> IVRCallLogs => Set<IVRCallLog>();
    public DbSet<IVRCallLogHistory> IVRCallLogHistories => Set<IVRCallLogHistory>();
    public DbSet<ServetelCallLog> ServetelCallLogs => Set<ServetelCallLog>();
    public DbSet<ServetelCallLogHistory> ServetelCallLogHistories => Set<ServetelCallLogHistory>();
    public DbSet<FacebookAuthResponse> FacebookAuthResponses => Set<FacebookAuthResponse>();
    public DbSet<FacebookConnectedPageAccount> FacebookConnectedPageAccount => Set<FacebookConnectedPageAccount>();
    public DbSet<FacebookLeadGenForm> FacebookLeadGenForm => Set<FacebookLeadGenForm>();
    public DbSet<GoogleAdLeadFormIntegrationInfo> GoogleAdLeadFormIntegrationInfo => Set<GoogleAdLeadFormIntegrationInfo>();
    public DbSet<GoogleAdLeadFormData> GoogleAdLeadFormData => Set<GoogleAdLeadFormData>();
    public DbSet<FacebookAdsInfo> FacebookAdsInfo => Set<FacebookAdsInfo>();
    public DbSet<MyOperatorCallLog> MyOperatorCallLogs => Set<MyOperatorCallLog>();
    public DbSet<FacebookBulkLeadFetchTracker> FacebookBulkLeadFetchTracker => Set<FacebookBulkLeadFetchTracker>();
    public DbSet<CronberryCallLog> CronberryCallLogs => Set<CronberryCallLog>();
    public DbSet<IntegrationAssignmentInfo> IntegrationAssignmentInfo => Set<IntegrationAssignmentInfo>();
    public DbSet<IntegrationAssignment> IntegrationAssignments => Set<IntegrationAssignment>();
    public DbSet<ExportFacebookBulkLeadsTracker> ExportFacebookBulkLeadsTracker => Set<ExportFacebookBulkLeadsTracker>();
    public DbSet<CommonWebhookLeadInfo> CommonWebhookLeadInfo => Set<CommonWebhookLeadInfo>();
    public DbSet<ExotelCallLog> ExotelCallLogs => Set<ExotelCallLog>();
    public DbSet<IntegrationAccountAdditionalInfo> IntegrationAccountAdditionalInfos => Set<IntegrationAccountAdditionalInfo>();
    public DbSet<VoicePanelCallLog> VoicePanelCallLogs => Set<VoicePanelCallLog>();
    public DbSet<FacebookLeadInfo> FacebookLeadInfo => Set<FacebookLeadInfo>();
    public DbSet<IVRAssignment> IVRAssignments => Set<IVRAssignment>();
    public DbSet<IVRCommonCallLog> IVRCommonCallLogs => Set<IVRCommonCallLog>();
    public DbSet<IVRApiConfiguration> IVRApiConfigurations => Set<IVRApiConfiguration>();
    public DbSet<QKonnectCallLog> QKonnectCallLogs => Set<QKonnectCallLog>();
    public DbSet<IntegrationFilterInfo> IntegrationFilterInfos => Set<IntegrationFilterInfo>();
    public DbSet<TataTeleBusinessCallLog> TataTeleBusinessCallLogs => Set<TataTeleBusinessCallLog>();
    public DbSet<FreJunCallLog> FreJunCallLogs => Set<FreJunCallLog>();
    public DbSet<MCubeCallLog> MCubeCallLogs => Set<MCubeCallLog>();
    public DbSet<KommunoCallLog> KommunoCallLogs => Set<KommunoCallLog>();
    public DbSet<IVROutboundConfiguration> IVROutboundConfigurations => Set<IVROutboundConfiguration>();
    public DbSet<IVRPayloadMapping> IVRPayloadMappings => Set<IVRPayloadMapping>();
    public DbSet<WebhookPayloadMapping> WebhookPayloadMappings => Set<WebhookPayloadMapping>();

    #endregion
    #region User
    public DbSet<UserDetails> UserDetails => Set<UserDetails>();
    public DbSet<Department> Departments => Set<Department>();
    public DbSet<Designation> Designations => Set<Designation>();
    public DbSet<UserDocument> UserDocuments => Set<UserDocument>();
    public DbSet<UserSettings> UserSettings => Set<UserSettings>();
    public DbSet<UserLocation> UserLocations => Set<UserLocation>();
    public DbSet<DeletedUser> DeletedUsers => Set<DeletedUser>();
    #endregion
    #region Profile
    public DbSet<Profile> Profiles => Set<Profile>();
    public DbSet<Testimonial> Testimonials => Set<Testimonial>();
    public DbSet<Recognition> Recognitions => Set<Recognition>();
    public DbSet<SocialMedia> SocialMedias => Set<SocialMedia>();
    public DbSet<Subscription> Subscriptions => Set<Subscription>();
    public DbSet<Payment> Payments => Set<Payment>();
    public DbSet<SubscriptionAddOn> SubscriptionAddOns => Set<SubscriptionAddOn>();
    public DbSet<PaymentGatewayApiConfiguration> PaymentGatewayApiConfigurations => Set<PaymentGatewayApiConfiguration>();
    public DbSet<TransactionInfo> TransactionInfos => Set<TransactionInfo>();
    #endregion
    #region Project
    public DbSet<Project> Projects => Set<Project>();
    public DbSet<Block> Blocks => Set<Block>();

    public DbSet<Floor> Floors => Set<Floor>();
    public DbSet<UnitType> UnitTypes => Set<UnitType>();
    public DbSet<UnitTypeAttribute> UnitTypeAttributes => Set<UnitTypeAttribute>();
    public DbSet<ProjectAmenity> ProjectAmenities => Set<ProjectAmenity>();
    public DbSet<ProjectGallery> ProjectGalleries => Set<ProjectGallery>();
    public DbSet<ProjectMonetaryInfo> ProjectMonetaryInfos => Set<ProjectMonetaryInfo>();
    public DbSet<CustomMasterProjectType> CustomMasterProjectTypes => Set<CustomMasterProjectType>();
    public DbSet<AssociatedBank> AssociatedBanks => Set<AssociatedBank>();
    public DbSet<UnitInfoGallery> UnitInfoGalleries => Set<UnitInfoGallery>();
    public DbSet<BulkUnitUploadTracker> BulkUnitUploadTrackers => Set<BulkUnitUploadTracker>();
    public DbSet<ExportProjectTracker> ExportProjectTrackers => Set<ExportProjectTracker>();
    public DbSet<BulkProjectUploadTracker> BulkProjectUploadTracker => Set<BulkProjectUploadTracker>();
    #endregion
    #region PushNotification
    public DbSet<Device> Devices => Set<Device>();
    public DbSet<DeviceRegistrationInfo> DeviceRegistrationInfo => Set<DeviceRegistrationInfo>();
    public DbSet<Notification> Notifications => Set<Notification>();
    public DbSet<NotificationContent> NotificationContents => Set<NotificationContent>();
    public DbSet<NotificationTracker> NotificationTrackers => Set<NotificationTracker>();
    public DbSet<DeviceInfo> DeviceInfos => Set<DeviceInfo>();
    public DbSet<PushNotificationRecords> PushNotificationRecords => Set<PushNotificationRecords>();
    public DbSet<LocalNotification> LocalNotifications => Set<LocalNotification>();
    public DbSet<NotificationInfo> NotificationInfos => Set<NotificationInfo>();
    #endregion
    #region GlobalSettings
    public DbSet<GlobalSettings> GlobalSettings => Set<GlobalSettings>();
    public DbSet<OTPMessage> OTPMessages => Set<OTPMessage>();
    #endregion
    #region Reset Password
    public DbSet<ResetPasswordCredentialStore> ResetPasswordCredentialStore => Set<ResetPasswordCredentialStore>();
    #endregion
    #region PlayStoreVersion
    public DbSet<PlayStoreVersion> PlayStoreVersion => Set<PlayStoreVersion>();
    #endregion
    #region Notification
    public DbSet<NotificationServiceTracker> NotificationServiceTrackers => Set<NotificationServiceTracker>();
    #region SmsService
    public DbSet<MasterSMSService> MasterSMSServices => Set<MasterSMSService>();
    public DbSet<MasterSMSTemplate> MasterSMSTemplates => Set<MasterSMSTemplate>();
    public DbSet<MasterSMSQuota> MasterSMSQuota => Set<MasterSMSQuota>();
    public DbSet<SMSService> SMSServices => Set<SMSService>();
    public DbSet<SMSSettings> SMSSettings => Set<SMSSettings>();
    public DbSet<SMSTemplate> SMSTemplates => Set<SMSTemplate>();
    #endregion
    #region ChannelSelection
    public DbSet<ChannelSelection> ChannelSelections => Set<ChannelSelection>();
    #endregion
    #region EmailService
    public DbSet<EmailApiIntegrationData> EmailApiIntegrationData => Set<EmailApiIntegrationData>();
    public DbSet<EmailConfigurationData> EmailConfigurationData => Set<EmailConfigurationData>();
    public DbSet<EmailServiceProvider> EmailServiceProvider => Set<EmailServiceProvider>();
    public DbSet<EmailSettings> EmailSettings => Set<EmailSettings>();
    public DbSet<EmailTemplates> EmailTemplates => Set<EmailTemplates>();
    public DbSet<MasterEmailTemplates> MasterEmailTemplates => Set<MasterEmailTemplates>();
    public DbSet<MasterEmailApiIntegrationData> MasterEmailApiIntegrationData => Set<MasterEmailApiIntegrationData>();
    public DbSet<MasterEmailConfigurationData> MasterEmailConfigurationData => Set<MasterEmailConfigurationData>();
    public DbSet<MasterEmailServiceProvider> MasterEmailServiceProviders => Set<MasterEmailServiceProvider>();
    #endregion
    #endregion
    #region Temp
    public DbSet<TempProjects> TempProjects => Set<TempProjects>();

    #endregion
    #region DailyStatusUpdate
    public DbSet<DailyStatusUpdate> DailyStatusUpdates => Set<DailyStatusUpdate>();
    #endregion
    #region WhatsApp
    public DbSet<WhatsAppAPIInfo> WhatsAppAPIInfo => Set<WhatsAppAPIInfo>();
    public DbSet<WhatsAppCommunication> WhatsAppCommunications => Set<WhatsAppCommunication>();
    public DbSet<WhatsAppConfigurationByServiceProvider> WhatsAppConfigurationByServiceProvider => Set<WhatsAppConfigurationByServiceProvider>();
    public DbSet<WhatsAppSettings> WhatsAppSettings => Set<WhatsAppSettings>();
    public DbSet<WhatsAppTemplate> WhatsAppTemplates => Set<WhatsAppTemplate>();
    public DbSet<LRWhatsAppApiInfo> LRWhatsAppApiInfo => Set<LRWhatsAppApiInfo>();
    public DbSet<WhatsAppTemplateInfo> WhatsAppTemplateInfos => Set<WhatsAppTemplateInfo>();
    public DbSet<WhatsAppBulkTemplateTracker> WhatsAppBulkTemplateTracker => Set<WhatsAppBulkTemplateTracker>();
    public DbSet<WhatsAppTemplateButtonsInfo> WhatsAppTemplateButtonsInfos => Set<WhatsAppTemplateButtonsInfo>();

    #endregion
    #region ExportTemplate
    public DbSet<ExportTemplate> ExportTemplates => Set<ExportTemplate>();
    #endregion
    #region Template
    public DbSet<Template> Templates => Set<Template>();
    public DbSet<QRFormTemplate> QRFormTemplates => Set<QRFormTemplate>();
    public DbSet<QRFormTemplateDetail> QRFormTemplateDetails => Set<QRFormTemplateDetail>();
    public DbSet<QRAssignment> QRAssignments => Set<QRAssignment>();

    #endregion
    #region Attendance
    public DbSet<AttendanceLog> AttendanceLogs => Set<AttendanceLog>();
    public DbSet<ExportAttendanceTracker> ExportAttendanceTrackers => Set<ExportAttendanceTracker>();
    #endregion
    #region ErrorModule
    public DbSet<LrbError> Errors => Set<LrbError>();
    #endregion
    #region ReportsTracker
    public DbSet<ExportReportsTracker> ExportReportsTracker => Set<ExportReportsTracker>();
    #endregion
    #region ZonewiseLocation
    public DbSet<Country> Countries => Set<Country>();
    public DbSet<State> States => Set<State>();
    public DbSet<City> Cities => Set<City>();
    public DbSet<Zone> Zones => Set<Zone>();
    public DbSet<Location> Locations => Set<Location>();
    #endregion
    #region Automation
    public DbSet<AssignmentModule> AssignmentModules => Set<AssignmentModule>();
    public DbSet<UserAssignment> UserAssignments => Set<UserAssignment>();
    public DbSet<UserAssignmentMetrics> UserAssignmentMetrics => Set<UserAssignmentMetrics>();
    public DbSet<UserAssignmentHistory> UserAssignmentHistories => Set<UserAssignmentHistory>();

    #endregion
    #region UserLocation

    #endregion
    #region GoogleSheets
    public DbSet<GoogleSheetIntegrationData> GoogleSheetIntegrationData => Set<GoogleSheetIntegrationData>();
    public DbSet<Sheet> Sheets => Set<Sheet>();
    public DbSet<SpreadSheet> SpreadSheets => Set<SpreadSheet>();
    public DbSet<GoogleSheetUploadTracker> GoogleSheetUploadTrackers => Set<GoogleSheetUploadTracker>();
    public DbSet<GoogleNotificationChannelData> GoogleNotificationChannelData => Set<GoogleNotificationChannelData>();
    public DbSet<GoogleSpreadSheetTracker> GoogleSpreadSheetTrackers => Set<GoogleSpreadSheetTracker>();
    #endregion
    #region ModifiedDate
    public DbSet<ModifiedDate> ModifiedDates => Set<ModifiedDate>();
    #endregion
    #region ChannelPartner
    public DbSet<ChannelPartner> ChannelPartners => Set<ChannelPartner>();
    public DbSet<BulkChannelPartnerUploadTracker> BulkChannelPartnerUploadTrackers => Set<BulkChannelPartnerUploadTracker>();
    #endregion
    #region DataManagement
    public DbSet<Prospect> Prospects => Set<Prospect>();
    public DbSet<ProspectEnquiry> ProspectEnquiries => Set<ProspectEnquiry>();
    public DbSet<CustomProspectStatus> CustomProspectStatuses => Set<CustomProspectStatus>();
    public DbSet<ProspectCommunication> ProspectCommunications => Set<ProspectCommunication>();
    public DbSet<ProspectHistory> ProspectHistories => Set<ProspectHistory>();
    public DbSet<BulkProspectUploadTracker> BulkProspectUploadTracker => Set<BulkProspectUploadTracker>();
    public DbSet<CustomProspectSource> CustomProspectSources => Set<CustomProspectSource>();
    public DbSet<ExportProspectTracker> ExportProspectTrackers => Set<ExportProspectTracker>();
    public DbSet<ProspectCallLog> ProspectCallLogs => Set<ProspectCallLog>();
    #endregion
    #region Media
    public DbSet<Media> Media => Set<Media>();
    #endregion
    #region Custom
    public DbSet<CustomFilter> CustomFilters => Set<CustomFilter>();
    public DbSet<CustomField> CustomFields => Set<CustomField>();
    public DbSet<Field> Fields => Set<Field>();

    #endregion
    #region Flags
    public DbSet<Flag> Flags => Set<Flag>();
    public DbSet<CustomFlag> CustomFlags => Set<CustomFlag>();
    #endregion
    #region FinWizzWhatsapp
    public DbSet<FinwizzWhatsappChats> FinwizzWhatsappChats => Set<FinwizzWhatsappChats>();
    #endregion
    #region AsputilityWhatsappChats
    public DbSet<AsputilityWhatsappChats> AsputilityWhatsappChats => Set<AsputilityWhatsappChats>();
    #endregion
    #region Permissions
    public DbSet<SeededRolePermission> SeededRolePermissions => Set<SeededRolePermission>();
    #endregion
    #region CustomEmail
    public DbSet<CustomEmailInfo> CustomEmailInfo => Set<CustomEmailInfo>();
    public DbSet<CustomEmailTracker> CustomEmailTrackers => Set<CustomEmailTracker>();

    #endregion
    #region
    public DbSet<ApiLog> ApiMetrics => Set<ApiLog>();
    #endregion
    #region Agency
    public DbSet<Agency> Agencies => Set<Agency>();
    #endregion
    #region Attendence
    public DbSet<UserShiftTiming> UserShiftTimings => Set<UserShiftTiming>();
    public DbSet<AttendenceSettings> AttendenceSettings => Set<AttendenceSettings>();
    public DbSet<UserDeletedTracker> UserDeletedTrackers => Set<UserDeletedTracker>();
    #endregion
    public DbSet<Campaign> Campaigns => Set<Campaign>();
    public DbSet<BulkMarketingAgencyUploadTracker> BulkMarketingAgencyUploadTracker => Set<BulkMarketingAgencyUploadTracker>();

    #region WA
    public DbSet<WAMessage> WAMessages => Set<WAMessage>();
    public DbSet<WATempPayloadParam> TempPayloadParams => Set<WATempPayloadParam>();
    public DbSet<WAApiInfo> WAApiInfos => Set<WAApiInfo>();
    public DbSet<WATemplate> WATemplates => Set<WATemplate>();
    public DbSet<WAButton> WAButtons => Set<WAButton>();
    #endregion
    #region Icons
    public DbSet<Icons> Icons => Set<Icons>();
    #endregion

    #region ListingManagement
    public DbSet<ListingIntegrationInfo> ListingIntegrationInfos => Set<ListingIntegrationInfo>();
    public DbSet<ListingSiteTracker> ListingSiteTrackers => Set<ListingSiteTracker>();
    public DbSet<CustomListingSource> CustomListingSources => Set<CustomListingSource>();
    public DbSet<ListingSourceAddress> ListingSourceAddresses => Set<ListingSourceAddress>();
    public DbSet<BulkListingSourceAddressTracker> ListingSourceAddressTrackers => Set<BulkListingSourceAddressTracker>();
    #endregion
    #region CustomAddress
    public DbSet<CustomAddressDirectory> CustomAddressDirectories => Set<CustomAddressDirectory>();
    public DbSet<CustomAddressTracker> CustomAddressTrackers => Set<CustomAddressTracker>();
    public DbSet<TowerName> TowerNames => Set<TowerName>();

    public DbSet<SubCommunity> SubCommunities => Set<SubCommunity>();

    public DbSet<Community> Communities => Set<Community>();

    #endregion

    #region Property Refrence Info
    public DbSet<PropertyReferenceInfo> PropertyRefrenceInfos => Set<PropertyReferenceInfo>();
    public DbSet<BulkUploadRefrenceInfoTracker> BulkUploadRefrenceInfoTrackers => Set<BulkUploadRefrenceInfoTracker>();
    #endregion
    #region Source
    public DbSet<Source> Sources => Set<Source>();
    #endregion
    #region GoogleAds
    public DbSet<GoogleAdsAuthResponse> GoogleAdsAuthResponses => Set<GoogleAdsAuthResponse>();
    public DbSet<GoogleAdsAccount> GoogleAdsAccounts => Set<GoogleAdsAccount>();
    public DbSet<GoogleCampaign> GoogleCampaigns => Set<GoogleCampaign>();
    public DbSet<GoogleAdsInfo> GoogleAdsInfos => Set<GoogleAdsInfo>();
    public DbSet<GoogleAdsBulkLeadFetchTracker> GoogleAdsBulkLeadFetchTrackers => Set<GoogleAdsBulkLeadFetchTracker>();
    #endregion

    #region AutoDialer
    public DbSet<AutoDialerAudit> AutoDialerAudits => Set<AutoDialerAudit>();
    public DbSet<AutoDialerConfiguration> AutoDialerConfigurations => Set<AutoDialerConfiguration>();

    #endregion

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);
        modelBuilder.HasDefaultSchema(SchemaNames.LeadratBlack);

        modelBuilder.Entity<UserView>().ToView(ViewNames.VWUserInfo);
        modelBuilder.Entity<FullUserView>().ToView(ViewNames.VWFullUserInfo);

        modelBuilder.Entity<ApiLog>().ToTable(nameof(ApiLog), SchemaNames.ApiLogs);
    }
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = new CancellationToken())
    {
        try
        {
            _mobileCatalogDetailProcessed = false;
            await AddLeadDisplayIndex();

            await AddPropertyDisplayIndex();

            await AddProjectDisplayIndex();

            var tenantKeys = GetIntegrationAccountId();

            var auditEntries = HandleAuditingBeforeSaveChanges(_currentUser.GetUserId());

            int result = await base.SaveChangesAsync(cancellationToken);

            ProcessTenantResolutionKeys(tenantKeys);

            await HandleAuditingAfterSaveChangesAsync(auditEntries, cancellationToken);

            await SendDomainEventsAsync();

            return result;
        }
        catch (Exception ex)
        {
            var error = new LrbError()
            {
                ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                ErrorSource = ex?.Source,
                StackTrace = ex?.StackTrace,
                InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                ErrorModule = "ApplicationDbContext -> SaveChangesAsync()"
            };
            //Errors.Add(error);
            throw new Exception($"Error occurred in BaseDbContext.SaveChangesAsync() \nMessage: {ex.Message}, InnerException.Message: {ex?.InnerException?.Message ?? string.Empty}");
        }

    }

    private List<AuditTrail> HandleAuditingBeforeSaveChanges(Guid userId)
    {
        foreach (var entry in ChangeTracker.Entries<IAuditableEntity>().ToList())
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedBy = entry.Entity.CreatedBy == default ? userId : entry.Entity.CreatedBy;
                    entry.Entity.LastModifiedBy = entry.Entity.LastModifiedBy == default ? userId : entry.Entity.LastModifiedBy;
                    if ((entry.Entity is LeadAppointment) || (entry.Entity is LeadCommunication))
                    {
                        entry.Entity.CreatedOn = entry.Entity.CreatedOn != default ? entry.Entity.CreatedOn : DateTime.UtcNow;
                        entry.Entity.LastModifiedOn = entry.Entity.LastModifiedOn != default ? entry.Entity.LastModifiedOn : DateTime.UtcNow;
                    }
                    else
                    {
                        entry.Entity.CreatedOn = DateTime.UtcNow;
                        entry.Entity.LastModifiedOn = DateTime.UtcNow;
                    }
                    if (entry.Entity is Lead lead)
                    {
                        var name = entry.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                        lead.Name = string.IsNullOrEmpty(name) ? "Unknown" : name;
                    }
                    if (entry.Entity is IntegrationLeadInfo leadInfo)
                    {
                        var name = entry.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                        leadInfo.Name = string.IsNullOrEmpty(name) ? "Unknown" : name;
                    }
                    break;

                case EntityState.Modified:
                    if (entry.Entity is ISoftDelete delete)
                    {
                        if (delete.IsDeleted)
                        {
                            delete.DeletedBy = userId;
                            delete.DeletedOn = DateTime.UtcNow;
                        }
                    }
                    if (entry.Entity is IPickedDate pickedDate)
                    {
                        if (pickedDate.ShouldUpdatePickedDate)
                        {
                            pickedDate.PickedDate = DateTime.UtcNow;
                            pickedDate.IsPicked = true;
                        }
                    }
                    entry.Entity.LastModifiedOn = DateTime.UtcNow;
                    entry.Entity.LastModifiedBy = userId != Guid.Empty ? userId : entry.Entity.LastModifiedBy;
                    break;

                case EntityState.Deleted:
                    if (entry.Entity is ISoftDelete softDelete)
                    {
                        softDelete.IsDeleted = true;
                        softDelete.DeletedBy = userId;
                        softDelete.DeletedOn = DateTime.UtcNow;
                        entry.State = EntityState.Modified;
                    }

                    break;
            }
        }

        ChangeTracker.DetectChanges();

        var trailEntries = new List<AuditTrail>();
        foreach (var entry in ChangeTracker.Entries<IAuditableEntity>()
            .Where(e => e.State is EntityState.Added or EntityState.Deleted or EntityState.Modified)
            .ToList())
        {
            var trailEntry = new AuditTrail(entry, _serializer)
            {
                TableName = entry.Entity.GetType().Name,
                UserId = userId
            };
            try
            {
                if (Enum.TryParse(trailEntry?.TableName ?? string.Empty, out EntityType entity))
                {
                    UpdateModifiedDate(trailEntry, entity, userId, entry.State);
                }
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex.Message,
                    ErrorSource = ex.Source,
                    StackTrace = ex.StackTrace,
                    ErrorModule = "ApplicationDbContext -> UpdateModifiedDate()"
                };
                //Errors.Add(error);
            }
            trailEntries.Add(trailEntry);
            foreach (var property in entry.Properties)
            {
                if (property.IsTemporary)
                {
                    trailEntry.TemporaryProperties.Add(property);
                    continue;
                }

                string propertyName = property.Metadata.Name;
                if (property.Metadata.IsPrimaryKey())
                {
                    trailEntry.KeyValues[propertyName] = property.CurrentValue;
                    continue;
                }

                switch (entry.State)
                {
                    case EntityState.Added:
                        trailEntry.TrailType = TrailType.Create;
                        trailEntry.NewValues[propertyName] = property.CurrentValue;
                        break;

                    case EntityState.Deleted:
                        trailEntry.TrailType = TrailType.Delete;
                        trailEntry.OldValues[propertyName] = property.OriginalValue;
                        break;

                    case EntityState.Modified:
                        if (property.IsModified && entry.Entity is ISoftDelete && property.OriginalValue == null && property.CurrentValue != null)
                        {
                            trailEntry.ChangedColumns.Add(propertyName);
                            trailEntry.TrailType = TrailType.Delete;
                            trailEntry.OldValues[propertyName] = property.OriginalValue;
                            trailEntry.NewValues[propertyName] = property.CurrentValue;
                        }
                        else if (property.IsModified && property.OriginalValue?.Equals(property.CurrentValue) == false)
                        {
                            trailEntry.ChangedColumns.Add(propertyName);
                            trailEntry.TrailType = TrailType.Update;
                            trailEntry.OldValues[propertyName] = property.OriginalValue;
                            trailEntry.NewValues[propertyName] = property.CurrentValue;
                        }

                        break;
                }
            }
        }

        //foreach (var auditEntry in trailEntries.Where(e => !e.HasTemporaryProperties))
        //{
        //    //AuditTrails.Add(auditEntry.ToAuditTrail());
        //}
        if (trailEntries.Any())
        {
            ProcessAuditTrails(trailEntries.Where(e => !e.HasTemporaryProperties)?.Select(i => i.ToCosmosTrailTrail(TenantInfo?.Id ?? string.Empty))?.ToList());
        }

        return trailEntries.Where(e => e.HasTemporaryProperties).ToList();
    }

    #region ProcessAuditTrails
    private void ProcessAuditTrails(List<CosmosTrail>? trails)
    {
        try
        {
            if (trails?.Any() ?? false)
            {
                //string apiGatewayUrl = $"https://lrb-audit-operation.azurewebsites.net/api/AuditTrails?code=9VjOvbDOBIDt6xRTFHgxV3Gu3AWk7o6zdem3F3sUrIhkAzFu14ZeIw%3D%3D";
                //RestClient client = new(apiGatewayUrl);
                //RestRequest request = new();
                //request.AddBody(trails);
                //Task.Run(() => client.Execute(request));
            }
        }
        catch (Exception ex)
        {
            // ignore
        }
    }
    #endregion

    public void UpdateModifiedDate(AuditTrail? trailEntry, EntityType entity, Guid userId, EntityState state)
    {
        if (trailEntry != null)
        {
            switch (entity)
            {
                case EntityType.Agency:
                    if (_mobileCatalogDetailProcessed)
                    {
                        break;
                    }
                    _mobileCatalogDetailProcessed = true;
                    string agencyNames = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                    var agency = ModifiedDates.Where(i => i.EntityType == EntityType.Agency).FirstOrDefaultAsync().Result;
                    if (agency != null)
                    {
                        if (!string.IsNullOrEmpty(agencyNames ?? string.Empty) && !agencyNames.Equals(agency?.Value ?? string.Empty))
                        {
                            agency.Value = agencyNames;
                            agency.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(agency);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Agency, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.UserDetails:
                    if (_mobileCatalogDetailProcessed)
                    {
                        break;
                    }
                    _mobileCatalogDetailProcessed = true;
                    var userDetails = ModifiedDates.Where(i => i.EntityType == EntityType.UserDetails).FirstOrDefaultAsync().Result;
                    var currentUserDetails = ModifiedDates.Where(i => i.EntityType == EntityType.CurrentUser).FirstOrDefaultAsync().Result;
                    var id = trailEntry?.Entry?.Entity?.TryGetPropertyValue<Guid>("UserId");
                    if (id != null && id == userId)
                    {
                        if (currentUserDetails != null)
                        {
                            currentUserDetails.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(currentUserDetails);
                        }
                        else
                        {
                            var newEntity = GetModifiedDateData(EntityType.CurrentUser, userId);
                            ModifiedDates.Add(newEntity);
                        }

                    }
                    if (userDetails != null)
                    {
                        userDetails.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(userDetails);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.UserDetails, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Profile:
                    var orgProfile = ModifiedDates.Where(i => i.EntityType == EntityType.Profile).FirstOrDefaultAsync().Result;
                    if (orgProfile != null)
                    {
                        orgProfile.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(orgProfile);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Profile, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.WhatsAppTemplate:
                    var whatsAppTemplate = ModifiedDates.Where(i => i.EntityType == EntityType.WhatsAppTemplate).FirstOrDefaultAsync().Result;
                    if (whatsAppTemplate != null)
                    {
                        whatsAppTemplate.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(whatsAppTemplate);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.WhatsAppTemplate, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Template:
                    var template = ModifiedDates.Where(i => i.EntityType == EntityType.Template).FirstOrDefaultAsync().Result;
                    if (template != null)
                    {
                        template.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(template);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Template, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.TempProjects:
                    string name = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                    var tempProjects = ModifiedDates.Where(i => i.EntityType == EntityType.TempProjects).FirstOrDefaultAsync().Result;
                    if (tempProjects != null)
                    {
                        if (!string.IsNullOrEmpty(name ?? string.Empty) && !name.Equals(tempProjects?.Value ?? string.Empty))
                        {
                            tempProjects.Value = name;
                            tempProjects.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(tempProjects);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.TempProjects, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Property:
                    if (_mobileCatalogDetailProcessed)
                    {
                        break;
                    }
                    _mobileCatalogDetailProcessed = true;
                    string title = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Title") ?? string.Empty;
                    var property = ModifiedDates.Where(i => i.EntityType == EntityType.Property).FirstOrDefaultAsync().Result;
                    if (property != null)
                    {
                        if (!string.IsNullOrEmpty(title ?? string.Empty) && !title.Equals(property?.Value ?? string.Empty))
                        {
                            property.Value = title;
                            property.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(property);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Property, userId);
                        ModifiedDates.Add(newEntity);
                    }

                    break;
                case EntityType.UserSettings:
                    var existingModifiedDate = ModifiedDates.Where(i => i.EntityType == EntityType.UserSettings).FirstOrDefaultAsync().Result;
                    if (existingModifiedDate != null)
                    {
                        existingModifiedDate.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(existingModifiedDate);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.UserSettings, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.CustomMasterLeadStatus:
                    var leadStatus = ModifiedDates.Where(i => i.EntityType == EntityType.CustomMasterLeadStatus).FirstOrDefaultAsync().Result;
                    if (leadStatus != null)
                    {
                        leadStatus.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(leadStatus);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.CustomMasterLeadStatus, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.GlobalSettings:
                    var globalSettings = ModifiedDates.Where(i => i.EntityType == EntityType.GlobalSettings).FirstOrDefaultAsync().Result;
                    if (globalSettings != null)
                    {
                        globalSettings.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(globalSettings);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.GlobalSettings, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Flag:
                    var flag = ModifiedDates.Where(i => i.EntityType == EntityType.Flag).FirstOrDefaultAsync().Result;
                    if (flag != null)
                    {
                        flag.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(flag);
                    }
                    else
                    {
                        var newflag = GetModifiedDateData(EntityType.Flag, userId);
                        ModifiedDates.Add(newflag);
                    }
                    break;
                case EntityType.MobileCatalogDetail:
                    if (_mobileCatalogDetailProcessed)
                    {
                        break;
                    }
                    _mobileCatalogDetailProcessed = true;
                    var category = ModifiedDates.Where(i => i.EntityType == EntityType.MobileCatalogDetail).FirstOrDefaultAsync().Result;
                    if (category != null)
                    {
                        category.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(category);
                    }
                    else
                    {
                        var newCategory = GetModifiedDateData(EntityType.MobileCatalogDetail, userId);
                        ModifiedDates.Add(newCategory);
                    }
                    break;
                case EntityType.CustomMasterAmenity:
                    var amenity = ModifiedDates.Where(i => i.EntityType == EntityType.CustomMasterAmenity).FirstOrDefaultAsync().Result;

                    if (amenity != null)
                    {
                        amenity.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(amenity);

                    }
                    else
                    {
                        var newamenity = GetModifiedDateData(EntityType.CustomMasterAmenity, userId);
                        ModifiedDates.Add(newamenity);
                    }
                    break;
                case EntityType.CustomMasterAttribute:
                    var attribute = ModifiedDates.Where(i => i.EntityType == EntityType.CustomMasterAttribute).FirstOrDefaultAsync().Result;

                    if (attribute != null)
                    {
                        attribute.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(attribute);

                    }
                    else
                    {
                        var newattribute = GetModifiedDateData(EntityType.CustomMasterAttribute, userId);
                        ModifiedDates.Add(newattribute);
                    }
                    break;
                case EntityType.Source:
                    var source = ModifiedDates.Where(i => i.EntityType == EntityType.Source).FirstOrDefaultAsync().Result;
                    if (source != null)
                    {
                        source.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(source);
                    }
                    else
                    {
                        var newsource = GetModifiedDateData(EntityType.Source, userId);
                        ModifiedDates.Add(newsource);
                    }
                    break;
                case EntityType.CustomMasterDataStatus:
                    var dataStatus = ModifiedDates.Where(i => i.EntityType == EntityType.CustomMasterDataStatus).FirstOrDefaultAsync().Result;
                    if (dataStatus != null)
                    {
                        dataStatus.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(dataStatus);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.CustomMasterDataStatus, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Project:
                    if (_mobileCatalogDetailProcessed)
                    {
                        break;
                    }
                    _mobileCatalogDetailProcessed = true;
                    var project = ModifiedDates.Where(i => i.EntityType == EntityType.Project).FirstOrDefaultAsync().Result;
                    if (project != null)
                    {
                        project.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(project);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Project, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Campaign:
                    if (_mobileCatalogDetailProcessed)
                    {
                        break;
                    }
                    _mobileCatalogDetailProcessed = true;
                    string campaignName = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                    var campaign = ModifiedDates.Where(i => i.EntityType == EntityType.Campaign).FirstOrDefaultAsync().Result;
                    if (campaign != null)
                    {
                        if (!string.IsNullOrEmpty(campaignName ?? string.Empty) && !campaignName.Equals(campaign?.Value ?? string.Empty))
                        {
                            campaign.Value = campaignName;
                            campaign.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(campaign);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Campaign, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.ChannelPartner:
                    if (_mobileCatalogDetailProcessed)
                    {
                        break;
                    }
                    _mobileCatalogDetailProcessed = true;
                    string channelPartnerName = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("FirmName") ?? string.Empty;
                    var channelPartner = ModifiedDates.Where(i => i.EntityType == EntityType.ChannelPartner).FirstOrDefaultAsync().Result;
                    if (channelPartner != null)
                    {
                        if (!string.IsNullOrEmpty(channelPartnerName ?? string.Empty) && !channelPartnerName.Equals(channelPartner?.Value ?? string.Empty))
                        {
                            channelPartner.Value = channelPartnerName;
                            channelPartner.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(channelPartner);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.ChannelPartner, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Designation:
                    string designationName = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                    var designation = ModifiedDates.Where(i => i.EntityType == EntityType.Designation).FirstOrDefaultAsync().Result;
                    if (designation != null)
                    {
                        if (!string.IsNullOrEmpty(designationName ?? string.Empty) && !designationName.Equals(designation?.Value ?? string.Empty))
                        {
                            designation.Value = designationName;
                            designation.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(designation);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Designation, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Department:
                    string departmentName = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                    var department = ModifiedDates.Where(i => i.EntityType == EntityType.Department).FirstOrDefaultAsync().Result;
                    if (department != null)
                    {
                        if (!string.IsNullOrEmpty(departmentName ?? string.Empty) && !departmentName.Equals(department?.Value ?? string.Empty))
                        {
                            department.Value = departmentName;
                            department.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(department);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Department, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
              
                case EntityType.City:
                    string cityNames = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                    var city = ModifiedDates.Where(i => i.EntityType == EntityType.City).FirstOrDefaultAsync().Result;
                    if (city != null)
                    {
                        if (!string.IsNullOrEmpty(cityNames ?? string.Empty) && !cityNames.Equals(city?.Value ?? string.Empty))
                        {
                            city.Value = cityNames;
                            city.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(city);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.City, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;

                case EntityType.State:
                    string stateNames = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                    var states = ModifiedDates.Where(i => i.EntityType == EntityType.State).FirstOrDefaultAsync().Result;
                    if (states != null)
                    {
                        if (!string.IsNullOrEmpty(stateNames ?? string.Empty) && !stateNames.Equals(states?.Value ?? string.Empty))
                        {
                            states.Value = stateNames;
                            states.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(states);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.State, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;

                case EntityType.Location:
                    string locationNames = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                    var location = ModifiedDates.Where(i => i.EntityType == EntityType.Location).FirstOrDefaultAsync().Result;
                    if (location != null)
                    {
                        if (!string.IsNullOrEmpty(locationNames ?? string.Empty) && !locationNames.Equals(location?.Value ?? string.Empty))
                        {
                            location.Value = locationNames;
                            location.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(location);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Location, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Country:
                    string countryNames = trailEntry?.Entry?.Entity?.TryGetPropertyValue<string>("Name") ?? string.Empty;
                    var country = ModifiedDates.Where(i => i.EntityType == EntityType.Country).FirstOrDefaultAsync().Result;
                    if (country != null)
                    {
                        if (!string.IsNullOrEmpty(countryNames ?? string.Empty) && !countryNames.Equals(country?.Value ?? string.Empty))
                        {
                            country.Value = countryNames;
                            country.LastModifiedOn = DateTime.UtcNow;
                            ModifiedDates.Update(country);
                        }
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Country, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.CustomEmailInfo:
                    var customEmailInfo = ModifiedDates.Where(i => i.EntityType == EntityType.CustomEmailInfo).FirstOrDefaultAsync().Result;
                    if (customEmailInfo != null)
                    {
                        customEmailInfo.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(customEmailInfo);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.CustomEmailInfo, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.AttendenceSettings:
                    var attendenceSettings = ModifiedDates.Where(i => i.EntityType == EntityType.AttendenceSettings).FirstOrDefaultAsync().Result;
                    if (attendenceSettings != null)
                    {
                        attendenceSettings.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(attendenceSettings);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.AttendenceSettings, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Roles:
                    var roles = ModifiedDates.Where(i => i.EntityType == EntityType.Roles).FirstOrDefaultAsync().Result;
                    if (roles != null)
                    {
                        roles.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(roles);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Roles, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.CountryInfo:
                    var countryInfo = ModifiedDates.Where(i => i.EntityType == EntityType.CountryInfo).FirstOrDefaultAsync().Result;
                    if (countryInfo != null)
                    {
                        countryInfo.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(countryInfo);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.CountryInfo, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Currencies:
                    var currencies = ModifiedDates.Where(i => i.EntityType == EntityType.Currencies).FirstOrDefaultAsync().Result;
                    if (currencies != null)
                    {
                        currencies.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(currencies);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Currencies, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Subscription:
                    var subscription = ModifiedDates.Where(i => i.EntityType == EntityType.Subscription).FirstOrDefaultAsync().Result;
                    if (subscription != null)
                    {
                        subscription.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(subscription);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Subscription, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.Filter:
                    var filter = ModifiedDates.Where(i => i.EntityType == EntityType.Filter).FirstOrDefaultAsync().Result;
                    if (filter != null)
                    {
                        filter.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(filter);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.Filter, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.UserShiftTiming:
                    var userShiftTiming = ModifiedDates.Where(i => i.EntityType == EntityType.UserShiftTiming).FirstOrDefaultAsync().Result;
                    if (userShiftTiming != null)
                    {
                        userShiftTiming.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(userShiftTiming);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.UserShiftTiming, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                case EntityType.DuplicateLeadFeatureInfo:
                    var duplicateLeadFeatureInfo = ModifiedDates.Where(i => i.EntityType == EntityType.GlobalSettings).FirstOrDefaultAsync().Result;
                    if (duplicateLeadFeatureInfo != null)
                    {
                        duplicateLeadFeatureInfo.LastModifiedOn = DateTime.UtcNow;
                        ModifiedDates.Update(duplicateLeadFeatureInfo);
                    }
                    else
                    {
                        var newEntity = GetModifiedDateData(EntityType.GlobalSettings, userId);
                        ModifiedDates.Add(newEntity);
                    }
                    break;
                default:
                    break;
            }
        }
    }

    #region ProcessTenantResolutionKeys
    private (Guid?, string?)? GetIntegrationAccountId()
    {
        try
        {
            var result = ChangeTracker.Entries<IAuditableEntity>().FirstOrDefault(e => e.State is EntityState.Added && (e.Entity.GetType().Name == nameof(IntegrationAccountInfo)));
            var fbConnectedPage = ChangeTracker.Entries<IAuditableEntity>().FirstOrDefault(e => e.State is EntityState.Added && (e.Entity.GetType().Name == nameof(FacebookConnectedPageAccount)));
            return (result?.Entity?.TryGetPropertyValue<Guid>("Id"), fbConnectedPage?.Entity?.TryGetPropertyValue<string>("FacebookId"));
        }
        catch (Exception ex)
        {
            //ignore
        }
        return null;
    }
    public bool UpdateTenantResolutionKeys(string tenant, List<string> pageIds)
    {
        try
        {
            var conn = new NpgsqlConnection(_dbSettings.ConnectionString);
            try
            {
                conn.Open();
                using (NpgsqlCommand command = new NpgsqlCommand("CALL \"LeadratBlack\".\"UpdateTenantResolutionKeys\"(@tenant_id, @v_ids)", conn))
                {
                    command.Parameters.AddWithValue("tenant_id", tenant);
                    command.Parameters.AddWithValue("v_ids", pageIds);
                    command.ExecuteNonQuery();
                }
                return true;
            }
            catch (Exception ex)
            {
                //ignore
            }
            finally { conn.Close(); }
        }
        catch (Exception ex)
        {
            //ignore
        }
        return false;
    }
    private List<Guid> GetTenantResolutionKeys(string tenant, Guid id)
    {
        try
        {
            if (!string.IsNullOrEmpty(TenantInfo?.ConnectionString))
            {
                var conn = new NpgsqlConnection(TenantInfo?.ConnectionString);
                try
                {
                    conn.Open();
                    var query = $"select * from  \"LeadratBlack\".\"GetTenantResolutionKeys\"('{tenant}', '{id}')";
                    var result = conn.QueryFirstOrDefault<Guid[]>(query);
                    return result?.ToList() ?? new();
                }
                catch (Exception ex)
                {
                    //ignore
                }
                finally { conn.Close(); }
            }
        }
        catch (Exception ex)
        {
            //ignore
        }
        return new();
    }
    public bool ProcessTenantResolutionKeys((Guid?, string?)? value)
    {
        try
        {
            var tenant = TenantInfo?.Id;
            if (!string.IsNullOrEmpty(tenant) && value != null && value.Value.Item1 != null && value.Value.Item1 != Guid.Empty)
            {
                var ids = GetTenantResolutionKeys(tenant, value.Value.Item1 ?? Guid.Empty);
                if (ids?.Any() ?? false)
                {
                    UpdateTenantResolutionKeys(tenant, ids.ConvertAll(i => i.ToString()));
                }
            }
            else if (value != null && !string.IsNullOrEmpty(value.Value.Item2) && !string.IsNullOrEmpty(tenant))
            {
                UpdateTenantResolutionKeys(tenant, new List<string>() { value.Value.Item2 });
            }
        }
        catch (Exception ex)
        {
            //ignore
        }
        return true;
    }
    #endregion

    public ModifiedDate GetModifiedDateData(EntityType entityType, Guid userId)
    {
        var entity = new ModifiedDate();
        entity.Id = Guid.NewGuid();
        entity.LastModifiedOn = DateTime.UtcNow;
        entity.CreatedOn = DateTime.UtcNow;
        entity.EntityType = entityType;
        entity.CreatedBy = userId;
        entity.LastModifiedBy = userId;
        return entity;
    }
    private Task HandleAuditingAfterSaveChangesAsync(List<AuditTrail> trailEntries, CancellationToken cancellationToken = new())
    {
        if (trailEntries == null || trailEntries.Count == 0)
        {
            return Task.CompletedTask;
        }
        var trails = new List<CosmosTrail>();
        foreach (var entry in trailEntries)
        {
            foreach (var prop in entry.TemporaryProperties)
            {
                if (prop.Metadata.IsPrimaryKey())
                {
                    entry.KeyValues[prop.Metadata.Name] = prop.CurrentValue;
                }
                else
                {
                    entry.NewValues[prop.Metadata.Name] = prop.CurrentValue;
                }
            }

            trails.Add(entry.ToCosmosTrailTrail(TenantInfo?.Id ?? string.Empty));
        }
        ProcessAuditTrails(trails);
        return SaveChangesAsync(cancellationToken);
    }

    private async Task SendDomainEventsAsync()
    {
        var entitiesWithEvents = ChangeTracker.Entries<IEntity>()
            .Select(e => e.Entity)
            .Where(e => e.DomainEvents.Count > 0)
            .ToArray();

        foreach (var entity in entitiesWithEvents)
        {
            var domainEvents = entity.DomainEvents.ToArray();
            entity.DomainEvents.Clear();
            foreach (var domainEvent in domainEvents)
            {
                await _events.PublishAsync(domainEvent);
            }
        }
    }

    private async Task AddLeadDisplayIndex()
    {
        var serials = new List<long>();
        var addedEntities = ChangeTracker.Entries<Lead>()
        .Where(e => e.State == EntityState.Added)
        .ToList();
        foreach (var entry in addedEntities)
        {
            entry.Entity.SerialNumber = await GenerateDisplayIndexPrefixAsync(TenantInfo.Id, serials);
        }

    }
    public string GetDisplayIndexPrefixByTenantId(string tenantId)
    {
        return _tenanatDbContext.TenantInfo.FirstOrDefault(i => i.Identifier == tenantId)?.DisplayPrefix;
    }
    public async Task<string> GenerateDisplayIndexPrefixAsync(string tenantId, List<long> serials)
    {
        string prefix = GetDisplayIndexPrefixByTenantId(tenantId);
        try
        {
            var maxSerial = await this.Leads
                .Where(e => e.SerialNumber.StartsWith(prefix))
                .Select(e => e.SerialNumber.Substring(2))
                .MaxAsync(s => (long)Convert.ToDouble(s));
            long nextSerial = maxSerial + 1;
            serials.Add(nextSerial);
            var serial = serials.LastOrDefault();
            if (serial == nextSerial && serials.Count > 1)
            {
                nextSerial = serials.Max() + 1;
                serials.Add(nextSerial);
            }
            return $"{prefix}{nextSerial:0000000000}";
        }
        catch (Exception e)
        {
            //var error = new LrbError()
            //{
            //    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
            //    ErrorSource = e?.Source,
            //    StackTrace = e?.StackTrace,
            //    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
            //    ErrorModule = "ApplicationDbContext -> GenerateDisplayIndexPrefixAsync()"
            //};
            //Errors.Add(error);
            long nextSerial = long.Parse("0000000001");
            return $"{prefix}{nextSerial:0000000000}";
        }
    }

    private async Task AddPropertyDisplayIndex()
    {
        var serials = new List<long>();
        var addedEntities = ChangeTracker.Entries<Property>()
            .Where(e => e.State == EntityState.Added)
            .ToList();
        foreach (var entity in addedEntities)
        {
            entity.Entity.SerialNo = await GeneratePropertyDisplayIndexPrefixAsync(TenantInfo.Id, serials);
        }
    }

    public async Task<string> GeneratePropertyDisplayIndexPrefixAsync(string tenantId, List<long> serials)
    {
        string prefix = GetDisplayIndexPrefixByTenantId(tenantId);
        try
        {
            var maxSerial = await this.Properties
                .Where(e => e.SerialNo.StartsWith(prefix))
                .Select(e => e.SerialNo.Substring(2))
                .MaxAsync(s => (long)Convert.ToDouble(s));
            long nextSerial = maxSerial + 1;
            serials.Add(nextSerial);
            var serial = serials.LastOrDefault();
            if (serial == nextSerial && serials.Count > 1)
            {
                nextSerial = serials.Max() + 1;
                serials.Add(nextSerial);
            }
            return $"{prefix}{nextSerial:0000000000}";
        }
        catch (Exception e)
        {
            //var error = new LrbError()
            //{
            //    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
            //    ErrorSource = e?.Source,
            //    StackTrace = e?.StackTrace,
            //    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
            //    ErrorModule = "ApplicationDbContext -> GeneratePropertyDisplayIndexPrefixAsync()"
            //};
            //Errors.Add(error);
            long nextSerial = long.Parse("0000000001");
            return $"{prefix}{nextSerial:0000000000}";
        }
    }

    private async Task AddProjectDisplayIndex()
    {
        var serials = new List<long>();
        var addedEntities = ChangeTracker.Entries<Project>()
            .Where(e => e.State == EntityState.Added)
            .ToList();
        foreach (var entity in addedEntities)
        {
            entity.Entity.SerialNo = await GenerateProjectDisplayIndexPrefixAsync(TenantInfo.Id, serials);
        }
    }

    public async Task<string> GenerateProjectDisplayIndexPrefixAsync(string tenantId, List<long> serials)
    {
        string prefix = GetDisplayIndexPrefixByTenantId(tenantId);
        try
        {
            var maxSerial = await this.Projects
                .Where(p => p.SerialNo.StartsWith(prefix))
                .Select(e => e.SerialNo.Substring(2))
                .MaxAsync(e => (long)Convert.ToDouble(e));
            long nextSerial = maxSerial + 1;
            serials.Add(nextSerial);
            var serial = serials.LastOrDefault();
            if (serial == nextSerial && serials.Count > 1)
            {
                nextSerial = serials.Max() + 1;
                serials.Add(nextSerial);
            }
            return $"{prefix}{nextSerial:0000000000}";
        }
        catch (Exception e)
        {
            //var error = new LrbError()
            //{
            //    ErrorMessage = e?.Message ?? e?.InnerException?.Message,
            //    ErrorSource = e?.Source,
            //    StackTrace = e?.StackTrace,
            //    InnerException = JsonConvert.SerializeObject(e?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
            //    ErrorModule = "ApplicationDbContext -> GenerateProjectDisplayIndexPrefixAsync()"
            //};
            //Errors.Add(error);
            long nextSerial = long.Parse("0000000001");
            return $"{prefix}{nextSerial:0000000000}";
        }
    }
}
