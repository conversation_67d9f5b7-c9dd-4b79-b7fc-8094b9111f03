﻿namespace Lrb.Application.Lead.Web.Requests
{
    public class GetLeadIdByContactNoRequest : IRequest<Response<LeadContactDto>>
    {
        public string ContactNo { get; set; }
        public string CountryCode {  get; set; }
        public GetLeadIdByContactNoRequest(string contactno,string countryCode)
        {
            ContactNo = contactno;
            CountryCode = countryCode;
        }
    }
    public class GetLeadByContactNoRequestHandler : IRequestHandler<GetLeadIdByContactNoRequest, Response<LeadContactDto>>
    {
        private readonly ICurrentUser _currentUser;
        private readonly IDapperRepository _dapperRepository;
        private readonly IReadRepository<Domain.Entities.Lead> _leadRepo;
        public GetLeadByContactNoRequestHandler(IReadRepository<Domain.Entities.Lead> leadRepo,
            ICurrentUser currentUser,
            IDapperRepository dapperRepository)
        {
            _leadRepo = leadRepo;
            _currentUser = currentUser;
            _dapperRepository = dapperRepository;
        }

        public async Task<Response<LeadContactDto>> Handle(GetLeadIdByContactNoRequest request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrEmpty(request.ContactNo)) { throw new Exception("Contact No field can not be empty."); }
            if (string.IsNullOrEmpty(request.CountryCode)) { throw new Exception("Country Code field can not be empty."); }
            var userId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsWithAdminAsync(userId, tenantId ?? string.Empty,viewAllLeads:null));
            string rawContactNo = request.ContactNo.Trim();
            var lead = (await _leadRepo.FirstOrDefaultAsync(new LeadLookupByContactNumberSpec(request.ContactNo,request.CountryCode), cancellationToken));
            if (lead != null)
            {
                if (subIds.Value.Contains(lead.AssignTo) || subIds.Key)
                {
                    return new(new LeadContactDto() { Id = lead.Id, CanNavigate = true });
                }
                else
                {
                    return new(new LeadContactDto() { Id = lead.Id, CanNavigate = false });
                }
            }
            return new(null);
        }
    }
}
