namespace Lrb.Application.Common.ServiceBus
{
    public interface IServiceBus : ITransientService
    {
        Task SendMessageAsync(object dto);
        Task RunExcelUploadJobAsync(object dto);
        Task ScheduleJobAsync(object dto, DateTimeOffset shiftStartTime);
        Task SendLeadRotationMessageAsync(object dto);
        Task RunBulkOpeartionJobAsync(object dto);
        Task RunUpdateLeadStatusAsync(object dto);
        Task RunAssignLeadsbasedOnScenariosAsync(object dto);
        Task RunLeadHistoryJobAsync(object dto);
        Task RunNotificationJobAsync(object dto);
        Task RunLeadRotationHttpTriggerJobAsync(object dto);
        Task RunExcelExportJobAsync(object dto);
        Task RunBulkStatusUpdateJobAsync(object dto);
        Task RunBulkOpeartionJobToSyncPropertyAsync(object dto);
        Task RunBulkProspectAssignmentJobAsync(object dto);
        Task RunBulkSecondaryLeadAssignmentJobAsync(object dto);
        Task RunBulkLeadSourceUpdateJobAsync(object dto);
        Task RunBulkProspectStatusUpdateJobAsync(object dto);
        Task RunBulkProjectUpdateJobAsync(object dto);
        Task RunFbConversionApiEventAsync(object dto);
        Task RunPFPropertyAssignementJobAsync(object dto);
        Task RunStatusChangeNotificationJobAsync(object dto);
        Task RunXMLFeedListingUrlSyncJobAsync(object dto);
        Task<string> ScheduleReportsAutomationAsync(object dto);
        Task DeleteScheduledReportAutomationAsync(List<string> jobIds);
        Task DeleteTenantAllReportAutomationAsync(string tenantId);
        Task DeleteAllReportAutomationAsync();
        Task RunLeadRotationTriggerJobAsync(object payload);
        Task RunBulkProspectSourceUpdateJobAsync(object dto);
        Task RunBulkSourceUpdateToDirectJobAsync(object dto);
        Task RunLeadRetentionTriggerJobAsync(object payload);
        Task RunBulkAgencyUpdateJobAsync(object dto);
        Task RunBulkChannelPartenerUpdateJobAsync(object dto);
        Task RunBulkCampaignUpdateJobAsync(object dto);
        Task RunBulkProspectAgencyUpdateJobAsync(object dto);
        Task RunBulkProspectChannelPartenerUpdateJobAsync(object dto);
        Task RunBulkProspectCampaignUpdateJobAsync(object dto);
        Task RunAutoDialerTriggerJobAsync(object payload);
        Task RunAutoDialerBulkAddJobAsync(object dto);

        #region Listing Jobs
        Task RunPublishListingJobAsync(object dto);
        Task RunUpdateListingJobAsync(object dto);
        Task RunDeleteListingJobAsync(object dto);
        Task RunPermanentDeleteListingJobAsync(object dto);
        Task RunListingToLeadMappingJobAsync(object dto);
        #endregion

    }
}
