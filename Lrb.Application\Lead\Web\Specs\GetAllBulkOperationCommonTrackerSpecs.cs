﻿namespace Lrb.Application.Lead.Web.Specs
{
    public class GetAllBulkOperationCommonTrackerSpecs : EntitiesByPaginationFilterSpec<BulkCommonTracker>
    {
        public GetAllBulkOperationCommonTrackerSpecs(GetAllBulkOperationCommonTrackerRequest request, List<Guid> subIds) :  base(request) 
        {
            Query.Where(i => !i.IsDeleted && request.Type != null &&  i.Module == request.Type.ToLower().Trim() && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }

    public class GetAllBulkOperationCommonTrackerCountSpecs :  Specification<BulkCommonTracker>
    {
        public GetAllBulkOperationCommonTrackerCountSpecs(string? type, List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && type != null &&  i.Module == type.ToLower().Trim() && subIds.Contains(i.CreatedBy));
        }
    }
}
