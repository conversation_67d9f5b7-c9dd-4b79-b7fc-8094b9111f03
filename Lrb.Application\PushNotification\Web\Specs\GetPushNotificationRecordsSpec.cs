﻿
using Lrb.Application.PushNotification.Web.Dtos;
using Lrb.Application.PushNotification.Web.Requests;

namespace Lrb.Application.PushNotification.Web.Specs
{
    public class GetPushNotificationRecordsSpec : EntitiesByPaginationFilterSpec<PushNotificationRecords>
    {
        public GetPushNotificationRecordsSpec(GetAllPushNotificationsRequest filter, Guid userId, string deviceUDID)
            : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId && i.DeviceUDID == deviceUDID)
                .OrderByDescending(i => i.CreatedOn);
        }
        public GetPushNotificationRecordsSpec(GetAllPushNotificationsRequest filter, Guid userId)
            : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId)
                .OrderByDescending(i => i.CreatedOn);
        }
    }
    public class GetPushNotificationRecordsCountSpec : Specification<PushNotificationRecords>
    {
        public GetPushNotificationRecordsCountSpec(GetAllPushNotificationsRequest filter, Guid userId, string deviceUDID)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId && i.DeviceUDID == deviceUDID)
                .OrderByDescending(i => i.CreatedOn);
        }
        public GetPushNotificationRecordsCountSpec(GetAllPushNotificationsRequest filter, Guid userId)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId)
                .OrderByDescending(i => i.CreatedOn);
        }
    }
    public class GetNotOpenedRecordsCountSpec : Specification<PushNotificationRecords>
    {
        public GetNotOpenedRecordsCountSpec(GetAllPushNotificationsRequest filter, Guid userId, string deviceUDID)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId && i.DeviceUDID == deviceUDID && !i.IsOpened)
                .OrderByDescending(i => i.CreatedOn);
        }
        public GetNotOpenedRecordsCountSpec(GetAllPushNotificationsRequest filter, Guid userId)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId && !i.IsOpened)
                .OrderByDescending(i => i.CreatedOn);
        }
    }
    public class GetNotificationsByUserIdSpec : EntitiesByPaginationFilterSpec<Domain.Entities.Notification>
    {
        public GetNotificationsByUserIdSpec(GetAllPushNotificationsByUserRequest filter, Guid userId)
            : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId)
                .OrderByDescending(i => i.CreatedOn);
        }
        //public GetNotificationsByUserIdSpec(GetAllPushNotificationsByUserWithoutCountRequest filter, Guid userId)
        //    : base(filter)
        //{
        //    Query.Where(i => !i.IsDeleted && i.UserId == userId)
        //        .OrderByDescending(i => i.CreatedOn);
        //}
    }
    public class GetNotificationsCountByUserIdSpec : Specification<Domain.Entities.Notification>
    {
        public GetNotificationsCountByUserIdSpec(Guid userId)
        {
            Query.Where(i => !i.IsDeleted &&
                              i.UserId == userId);
        }
    }
    public class GetPushNotificationRecordsWithoutPaginationSpec : Specification<PushNotificationRecords>
    {
        public GetPushNotificationRecordsWithoutPaginationSpec(List<Guid> uniqueNotificationIds, Guid currentUserId)
        {
            Query.Where(i => !i.IsDeleted && uniqueNotificationIds.Contains(i.NotificationUniqueId ?? Guid.Empty) && i.UserId == currentUserId)
                .OrderByDescending(i => i.CreatedOn);
        }
    }
    public class GetNotOpenedRecordsSpec : Specification<PushNotificationRecords>
    {
        public GetNotOpenedRecordsSpec(List<Guid> notificationUniqueIds, Guid userId)
        {
            Query.Where(i => !i.IsOpened
                                && !i.IsDeleted
                                && i.UserId == userId
                                && notificationUniqueIds.Contains(i.NotificationUniqueId ?? Guid.Empty));
        }
    }
    public class GetPushNotificationRecordsWithoutPaginationSpecV1 : Specification<PushNotificationRecords, PushNotificationRecordsDto>
    {
        public GetPushNotificationRecordsWithoutPaginationSpecV1(List<Guid> uniqueNotificationIds, Guid currentUserId)
        {
            Query.Where(i => !i.IsDeleted && uniqueNotificationIds.Contains(i.NotificationUniqueId ?? Guid.Empty) && i.UserId == currentUserId)
                .OrderByDescending(i => i.CreatedOn);
        }
    }
    public class GetNotificationsByUserIdSpecV1 : EntitiesByPaginationFilterSpec<Notification, NotificationDto>
    {
        public GetNotificationsByUserIdSpecV1(GetAllPushNotificationsByUserRequest filter, Guid userId)
            : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId)
                .OrderByDescending(i => i.CreatedOn);
        }

    }
    public class GetPushNotificationRecordsSpecV1 : EntitiesByPaginationFilterSpec<PushNotificationRecords, PushNotificationRecordsDto>
    {
        public GetPushNotificationRecordsSpecV1(GetAllPushNotificationsRequest filter, Guid userId, string deviceUDID)
            : base(filter)
        {
            Query.Where(i => !i.IsDeleted && i.UserId == userId && i.DeviceUDID == deviceUDID)
                .OrderByDescending(i => i.CreatedOn);

        }

    }
}
