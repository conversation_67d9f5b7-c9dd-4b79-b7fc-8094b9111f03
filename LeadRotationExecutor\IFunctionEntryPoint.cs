﻿using LeadRotationExecutor.Dtos;
using Lrb.Application.LeadRotation.Web.Dtos;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace LeadRotationExecutor
{
    public interface IFunctionEntryPoint
    {
        Task<(bool, LeadsAssignRotationInfoDto)> ScheduleLeadRotation(Guid leadId, LeadsAssignRotationInfoDto dto);
        Task<(bool, LeadsAssignRotationInfoDto)> ScheduleRetentionLeadRotation(Guid leadId, LeadsAssignRotationInfoDto dto);
        Task<bool> ScheduleBufferNotification(Guid leadId, LeadsAssignRotationInfoDto dto);
        Task<bool> UpdateRotationTracker(string msg, Guid leadId);
        Task CallAutoDailerAsync(List<Guid> userId);
    }
}
