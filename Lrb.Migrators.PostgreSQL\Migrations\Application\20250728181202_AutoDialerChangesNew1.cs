﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Lrb.Migrators.PostgreSQL.Migrations.Application
{
    public partial class AutoDialerChangesNew1 : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AutoDialerAudits_IVRCommonCallLogs_CallLogId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits");

            migrationBuilder.RenameColumn(
                name: "CallLogId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits",
                newName: "IVRCommonCallLogId");

            migrationBuilder.RenameIndex(
                name: "IX_AutoDialerAudits_CallLogId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits",
                newName: "IX_AutoDialerAudits_IVRCommonCallLogId");

            migrationBuilder.AddForeignKey(
                name: "FK_AutoDialerAudits_IVRCommonCallLogs_IVRCommonCallLogId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits",
                column: "IVRCommonCallLogId",
                principalSchema: "LeadratBlack",
                principalTable: "IVRCommonCallLogs",
                principalColumn: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AutoDialerAudits_IVRCommonCallLogs_IVRCommonCallLogId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits");

            migrationBuilder.RenameColumn(
                name: "IVRCommonCallLogId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits",
                newName: "CallLogId");

            migrationBuilder.RenameIndex(
                name: "IX_AutoDialerAudits_IVRCommonCallLogId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits",
                newName: "IX_AutoDialerAudits_CallLogId");

            migrationBuilder.AddForeignKey(
                name: "FK_AutoDialerAudits_IVRCommonCallLogs_CallLogId",
                schema: "LeadratBlack",
                table: "AutoDialerAudits",
                column: "CallLogId",
                principalSchema: "LeadratBlack",
                principalTable: "IVRCommonCallLogs",
                principalColumn: "Id");
        }
    }
}
