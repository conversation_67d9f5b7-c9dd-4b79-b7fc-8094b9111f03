﻿using Lrb.Application.UserDetails.Web.Request;
using Lrb.Domain.Entities.User;

namespace Lrb.Application.UserDetails.Web.Specs
{
    public class BulkUserUploadTrackerspec : EntitiesByPaginationFilterSpec<BulkUserUploadTracker>
    {
        public BulkUserUploadTrackerspec(GetImportUsersTrackerRequest filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy)).OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class BulkUserUploadTrackerspecCount : Specification<BulkUserUploadTracker>
    {
        public BulkUserUploadTrackerspecCount( List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
