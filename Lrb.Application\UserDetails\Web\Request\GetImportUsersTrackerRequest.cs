﻿using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Entities.User;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetImportUsersTrackerRequest : PaginationFilter, IRequest<PagedResponse<BulkUserUploadTrackerDto, string>>
    {
    }
    public class GetImportUsersTrackerRequestHandler : IRequestHandler<GetImportUsersTrackerRequest, PagedResponse<BulkUserUploadTrackerDto, string>>
    {
        private readonly IReadRepository<BulkUserUploadTracker> _trackerRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetImportUsersTrackerRequestHandler(IReadRepository<BulkUserUploadTracker> trackerRepo, IDapperRepository dapperRepository, ICurrentUser currentUser)

        {
            _trackerRepo = trackerRepo;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<BulkUserUploadTrackerDto, string>> Handle(GetImportUsersTrackerRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var userTracker = await _trackerRepo.ListAsync(new BulkUserUploadTrackerspec(request, subIds), cancellationToken);
            var totalCount = await _trackerRepo.CountAsync(new BulkUserUploadTrackerspecCount(subIds), cancellationToken);
            var userTrackers = userTracker.Adapt<List<BulkUserUploadTrackerDto>>(); 
            return new PagedResponse<BulkUserUploadTrackerDto, string>(userTrackers, totalCount);
        }

    }
}
