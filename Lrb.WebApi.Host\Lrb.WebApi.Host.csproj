﻿ <Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net6.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<UserSecretsId>54027374-199c-4ccd-9870-0e82e5d27e01</UserSecretsId>
		<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
		<ApplicationInsightsResourceId>/subscriptions/2b9dd759-624e-4279-8a84-89bd47a31332/resourceGroups/az-lrb-prd/providers/microsoft.insights/components/web-api-prd</ApplicationInsightsResourceId>
	</PropertyGroup>

	<ItemGroup>
	  <_ContentIncludedByDefault Remove="Files\Integration\FacebookWebhookPayload.json" />
	  <_ContentIncludedByDefault Remove="Files\Integration\ServetelIntegrationPayload.json" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AWSXRayRecorder" Version="2.13.0" />
		<PackageReference Include="Azure.Identity" Version="1.13.0" />
		<PackageReference Include="FluentValidation.AspNetCore" Version="11.2.2" />
		<PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
		<PackageReference Include="Hangfire.Console.Extensions.Serilog" Version="1.0.2" />
		<PackageReference Include="ISO3166" Version="1.0.4" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />

		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="6.0.36" />
		<PackageReference Include="Microsoft.AspNetCore.SignalR.Client.Core" Version="6.0.36" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.10">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
		<PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="6.0.0" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.15.1" />
		<PackageReference Include="Nager.Country" Version="4.0.0" />
		<PackageReference Include="NodaTime" Version="3.1.11" />
		<PackageReference Include="SixLabors.ImageSharp" Version="3.1.4" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.2.3" />
		<PackageReference Include="TimeZoneConverter" Version="6.1.0" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Lrb.Application\Lrb.Application.csproj" />
		<ProjectReference Include="..\Lrb.Infrastructure\Lrb.Infrastructure.csproj" />
		<ProjectReference Include="..\Lrb.Migrators.PostgreSQL\Lrb.Migrators.PostgreSQL.csproj" />
	</ItemGroup>
	<ItemGroup>
		<Content Update="Configurations\cache.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\cors.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\database.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\facebook.dev.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\facebook.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\facebook.prd.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\facebook.qa.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\hangfire.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\localization.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\logger.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\mail.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\middleware.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\openapi.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\security.dev.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\security.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\security.prd.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\security.qa.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\securityheaders.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Configurations\signalr.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Database\LeadSourceSeeds.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Database\MasterAreaUnitSeeds.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Database\MasterBuilderInfoSeeds.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Database\MasterLeadStatusSeeds.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Database\MasterPropertyAmenitySeeds.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Database\MasterPropertyAttributeSeeds.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Database\MasterPropertyTypeSeeds.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Database\MasterUserServiceSeeds.json">
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Integration\CommonWebhookPayload.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Integration\CronberryIntegrationPayload.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Integration\FacebookWebhookPayload.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Integration\IVRIntegrationPayload.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Integration\ListingSitesIntegrationPayload.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Integration\MyOperatorIntegrationPayload.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Integration\TataTeleBusinessIntegrationPayload.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Integration\ServetelIntegrationPayload.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Update="Files\Integration\WebsiteIntegrationPayload.json">
		  <CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>
	<ItemGroup>
	  <None Update="Files\Database\DBViews\VWFullUserInfo.txt">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="Files\Database\DBViews\VWUserInfo.txt">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>

</Project>
