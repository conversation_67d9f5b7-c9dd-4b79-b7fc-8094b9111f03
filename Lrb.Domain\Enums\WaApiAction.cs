﻿using System.ComponentModel;

namespace Lrb.Domain.Enums
{
    public enum WAApiAction
    {
        [Description("None")]
        None = 0,
        [Description("SendTextMessage")]
        SendTextMessage,
        [Description("SendTextWithMediaMessage")]
        SendTextWithMediaMessage,
        [Description("SendTextTemplate")]
        SendTextTemplate,
        [Description("SendTextWithMediaTemplate")]
        SendTextWithMediaTemplate,
        [Description("GetTemplates")]
        GetTemplates,
        [Description("CreateTemplate")]
        CreateTemplate,
        [Description("UpdateTemplate")]
        UpdateTemplate,
        [Description("DeleteTemplate")]
        DeleteTemplate,
        [Description("SendLeadToEngageto")]
        SendLeadToEngageto
    }
}
