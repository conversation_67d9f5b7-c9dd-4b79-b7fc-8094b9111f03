﻿namespace Lrb.Application.PushNotification.Web.Requests
{
    public class GetAllUnOpenedPushNotificationsCountByUserRequest : IRequest<Response<long>>
    {

    }
    public class GetAllUnOpenedPushNotificationsCountByUserRequestHandler : IRequestHandler<GetAllUnOpenedPushNotificationsCountByUserRequest, Response<long>>
    {
        private readonly ICurrentUser _currentUserRepo;
        private readonly IDapperRepository _dapperRepository;
        private readonly IRepositoryWithEvents<LocalNotification> _localNotificationRepo;
        public GetAllUnOpenedPushNotificationsCountByUserRequestHandler(
                ICurrentUser currentUserRepo,
                IDapperRepository dapperRepository,
                IRepositoryWithEvents<LocalNotification> localNotificationRepo)
        {
            _currentUserRepo = currentUserRepo;
            _dapperRepository = dapperRepository;
            _localNotificationRepo = localNotificationRepo;
        }
        public async Task<Response<long>> Handle(GetAllUnOpenedPushNotificationsCountByUserRequest request, CancellationToken cancellationToken)
        {
            Guid currentUserId = _currentUserRepo.GetUserId();
            var notificationsCount = await _dapperRepository.GetTotalUnOpenedNotificationsCountAsync(currentUserId, _currentUserRepo.GetTenant());
            return new()
            {
                Data = notificationsCount,
                Succeeded = true
            };
        }
    }
}
