﻿namespace Lrb.Domain.Enums
{
    public enum IVRCallStatus
    {
        None = 0,                // Default or unknown status

        // Call lifecycle: initiation
        InQueue = 1,             // Call is queued or about to be initiated
        Initiated = 2,           // Call has been triggered but not yet ringing
        Ringing = 3,             // Call is currently ringing
        Answered = 4,            // Call was answered by user or system
        Connected = 5,           // Both parties are connected
        InProgress = 6,          // Call is ongoing and IVR is active
        Transferred = 7,         // Call transferred to another agent or number

        // Call end states
        Completed = 8,           // Call ended normally
        Dropped = 9,             // Call was dropped unexpectedly
        Aborted = 10,            // Call was manually aborted before completion

        // Call failure states
        Failed = 11,             // Call failed (invalid number, system error)
        Busy = 12,               // Recipient was busy
        NoAnswer = 13,           // Call was not answered
        Timeout = 14,            // Call timed out waiting for response
        Rejected = 15,           // Call was explicitly rejected
        Voicemail = 16,          // Call routed to voicemail
        Blacklisted = 17         // Call blocked due to blacklisting
    }
}