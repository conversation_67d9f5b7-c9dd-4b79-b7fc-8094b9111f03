﻿using Lrb.Application.PushNotification.Web.Dtos;

namespace Lrb.Application.Common.PushNotification
{
    public interface ITemplateNotificationService
    {
        Task<bool> ProcessTemplateNotificationAsync(NotificationInfoDto templateConfig, CancellationToken cancellationToken, string? phoneNumber = null, string? testHeaderValue = null, List<string>? bodyValues = null, string? campaignName = null, int? noOfEntities = null);
    }
}   
