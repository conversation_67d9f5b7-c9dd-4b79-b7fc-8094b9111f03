﻿using Lrb.Application.DataManagement.Web.Export.Dtos;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.ExportTemplate;
using Lrb.Application.Identity.Users;
using Lrb.Application.Team.Web;
using Newtonsoft.Json;

namespace Lrb.Application.DataManagement.Web.Export.Requests
{
    public class GetExportTrackerRequest : PaginationFilter, IRequest<PagedResponse<ExportDataTrackerDto, string>>
    {
    }

    public class GetExportTrackerRequestHandler : IRequestHandler<GetExportTrackerRequest, PagedResponse<ExportDataTrackerDto, string>>
    {
        private readonly IRepositoryWithEvents<Lrb.Domain.Entities.ExportProspectTracker> _exportProspectTrackerRepo;
        private readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetExportTrackerRequestHandler(IRepositoryWithEvents<ExportProspectTracker> exportProspectTrackerRepo, IUserService userService, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _exportProspectTrackerRepo = exportProspectTrackerRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }

        public async Task<PagedResponse<ExportDataTrackerDto, string>> Handle(GetExportTrackerRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var prospectTrackers = await _exportProspectTrackerRepo.ListAsync(new GetExportProspectTrackerRequestSpecs(request, subIds), cancellationToken);
            var totalCount = await _exportProspectTrackerRepo.CountAsync(new GetExportProspectTrackerCountSpecs(subIds), cancellationToken);
            var prospectTrackerDto = prospectTrackers.Adapt<List<ExportDataTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(prospectTrackerDto.Select(i => i.CreatedBy.ToString()).Distinct().ToList(), cancellationToken);
            foreach (var tracker in prospectTrackerDto)
            {
                tracker.ExportTemplate = (JsonConvert.DeserializeObject<ViewExportTemplateDto>(tracker?.Template ?? string.Empty) ?? null);
                tracker.ExportedUser = users?.FirstOrDefault(i => tracker.CreatedBy == i.Id)?.Adapt<UserDto>();
            }
            return new(prospectTrackerDto, totalCount);
        }
    }
}
