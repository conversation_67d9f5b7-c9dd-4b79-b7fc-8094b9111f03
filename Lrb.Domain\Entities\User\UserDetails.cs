﻿namespace Lrb.Domain.Entities
{
    public class UserDetails : AuditableEntity, IAggregateRoot
    {
        public Guid UserId { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? CurrentAddress { get; set; }
        public string Email { get; set; }
        public string? AltEmail { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string PhoneNumber { get; set; }
        public string? PermanentAddress { get; set; }
        #region JobDetails
        public string? EmpNo { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }
        public Guid ReportsTo { get; set; }
        public Guid? GeneralManager { get; set; }
        public Department? Department { get; set; }
        public Designation? Designation { get; set; }
        #endregion
        public string? Description { get; set; }
        public IList<UserDocument>? UserDocuments { get; set; }
        public bool IsAutomationEnabled { get; set; }
        public string? TimeZoneInfo { get; set; }
        public bool? ShouldShowTimeZone { get; set; }
        public WhatsappThrough? WhatsappThrough { get; set; }
        public CallThrough? CallThrough { get; set; }
        public bool? IsGeoFenceActive { get; set; }
        public bool? IsAutoDialerEnabled { get; set; }
        public bool? IsAvilableForCall { get; set; }
    }
}
