﻿using Lrb.Application.Identity.Users;
using Lrb.Application.UserDetails.Web.Dtos;
using Lrb.Application.UserDetails.Web.Specs;
using Lrb.Domain.Entities.User;

namespace Lrb.Application.UserDetails.Web.Request
{
    public class GetDeleteUsersTrackerRequest : PaginationFilter, IRequest<PagedResponse<UserDeletedTrackerDto, string>>
    {
    }
    public class GetDeleteUserTrackerRequestHandler : IRequestHandler<GetDeleteUsersTrackerRequest, PagedResponse<UserDeletedTrackerDto, string>>
    {
        private readonly IReadRepository<UserDeletedTracker> _userTrackerRepo;
        public readonly IUserService _userService;
        private readonly IDapperRepository _dapperRepository;
        private readonly ICurrentUser _currentUser;
        public GetDeleteUserTrackerRequestHandler(IReadRepository<UserDeletedTracker> userTrackerRepo,
            IUserService userService, IDapperRepository dapperRepository, ICurrentUser currentUser)
        {
            _userTrackerRepo = userTrackerRepo;
            _userService = userService;
            _dapperRepository = dapperRepository;
            _currentUser = currentUser;
        }
        public async Task<PagedResponse<UserDeletedTrackerDto, string>> Handle(GetDeleteUsersTrackerRequest request, CancellationToken cancellationToken)
        {
            var currentUserId = _currentUser.GetUserId();
            var tenantId = _currentUser.GetTenant();
            var subIds = (await _dapperRepository.GetSubordinateIdsAsync(new List<Guid>() { currentUserId }, tenantId ?? string.Empty))?.ToList() ?? new();
            var userTracker = await _userTrackerRepo.ListAsync(new UserDeleteTrackerSpec(request, subIds), cancellationToken);
            var totalCount = await _userTrackerRepo.CountAsync(new GetUserDeleteTrackersCountSpec(subIds), cancellationToken);
            var UserTrackerDto = userTracker.Adapt<List<UserDeletedTrackerDto>>();
            var users = await _userService.GetListOfUsersByIdsAsync(UserTrackerDto.Select(i => i.CreatedBy.ToString()).ToList(), cancellationToken);
            var deleteduser = await _userService.GetListOfUsersByIdsAsync(UserTrackerDto.Select(i => i.UserId.ToString()).ToList(), cancellationToken);

            foreach (var userTrackers in UserTrackerDto)
            {
                userTrackers.DeletedBy = users?.FirstOrDefault(i => userTrackers.CreatedBy == i.Id)?.Adapt<Team.Web.UserDto>();
            }

            return new(UserTrackerDto, totalCount);
        }
    }
}