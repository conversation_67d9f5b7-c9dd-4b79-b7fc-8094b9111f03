﻿using Lrb.Application.AutoDialer.Web.Specs;
using Lrb.Application.Automation.Helpers;
using Lrb.Application.Common.PushNotification;
using Lrb.Application.Common.Services;
using Lrb.Application.CustomStatus.Web;
using Lrb.Application.DataManagement.Web.Dtos;
using Lrb.Application.DataManagement.Web.Mapping;
using Lrb.Application.DataManagement.Web.Specs;
using Lrb.Application.GlobalSettings.Web;
using Lrb.Application.Identity.Users;
using Lrb.Application.Integration.Web.Helpers;
using Lrb.Application.Integration.Web.Specs;
using Lrb.Application.Lead.Web;
using Lrb.Application.Lead.Web.Mappings;
using Lrb.Application.Lead.Web.Specs;
using Lrb.Application.Utils;
using Lrb.Domain.Entities.ErrorModule;
using Lrb.Domain.Entities.MasterData;
using Lrb.Shared.Extensions;
using Microsoft.AspNetCore.SignalR;
using Newtonsoft.Json;
using Serilog;
using System.Text.RegularExpressions;
using static Lrb.Application.CustomMasterLeadSubStatus.Web.Request.MasterLeadSubStatusByLevelAndBaseIdSpec;

namespace Lrb.Application.Integration.Web
{
    public class CommonIVRWebhookRequest : IRequest<Response<bool>>
    {
        public object? RequestBody { get; set; }
        public Guid AccountId { get; set; }
        public string ApiKey { get; set; } = default!;
        public string? ServiceProvider { get; set; }
        public bool? IsRedirectedRequest { get; set; }
        public bool? IsPayloadInFormData { get; set; }
    }
    public class CommonIVRWebhookRequestHandler : IRequestHandler<CommonIVRWebhookRequest, Response<bool>>
    {
        private readonly IRepositoryWithEvents<IntegrationAccountInfo> _integrationAccountInfoRepo;
        private readonly ILogger _logger;
        private readonly IRepository<Domain.Entities.IVRCommonCallLog> _ivrCommonCallLogRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Prospect> _prospectRepo;
        private readonly IRepositoryWithEvents<DuplicateLeadFeatureInfo> _duplicateInfoRepo;
        private IUserService _userService;
        private readonly IRepositoryWithEvents<MasterLeadStatus> _leadStatusRepo;
        private readonly IRepositoryWithEvents<CustomMasterLeadStatus> _customLeadStatusRepo;
        private readonly IRepositoryWithEvents<CustomProspectStatus> _customProspectStatusRepo;
        private readonly IRepositoryWithEvents<MasterProspectSource> _masterProspectSourceRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.GlobalSettings> _globalSettingsRepo;
        private readonly ILeadRepositoryAsync _leadRepositoryAsync;
        private readonly IRepositoryWithEvents<Address> _addressRepo;
        private readonly IRepositoryWithEvents<LeadHistory> _leadHistoryRepo;
        private readonly INpgsqlRepository _npgsqlRepo;
        private readonly INotificationSenderService _notificationSenderService;
        private readonly IDapperRepository _dapperRepo;
        private readonly IRepositoryWithEvents<ProspectHistory> _prospcetHistoryRepo;
        private readonly IRepositoryWithEvents<MasterPropertyType> _propertyTypeRepo;
        private readonly IHubContext<DialerHub,IDialerClient> _dialerClient;
        private readonly IRepositoryWithEvents<AutoDialerAudit> _autoDialerAuditRepo;

        public CommonIVRWebhookRequestHandler(IRepositoryWithEvents<IntegrationAccountInfo> integrationAccountInfoRepo,
            ILogger logger,
            IRepository<Domain.Entities.IVRCommonCallLog> ivrCommonCallLogRepo,
            IRepositoryWithEvents<Domain.Entities.Lead> leadRepo,
            IRepositoryWithEvents<Domain.Entities.Prospect> prospectRepo,
            IRepositoryWithEvents<DuplicateLeadFeatureInfo> duplicateInfoRepo,
            IUserService userService,
            IRepositoryWithEvents<MasterLeadStatus> leadStatusRepo,
            IRepositoryWithEvents<CustomMasterLeadStatus> customLeadStatusRepo,
            IRepositoryWithEvents<CustomProspectStatus> customProspectStatusRepo,
            IRepositoryWithEvents<MasterProspectSource> masterProspectSourceRepo,
            IRepositoryWithEvents<Domain.Entities.GlobalSettings> globalSettingsRepo,
            ILeadRepositoryAsync leadRepositoryAsync,
            IRepositoryWithEvents<Address> addressRepo,
            IRepositoryWithEvents<LeadHistory> leadHistoryRepo,
            INpgsqlRepository npgsqlRepo,
            INotificationSenderService notificationSenderService,
            IDapperRepository dapperRepo,
            IRepositoryWithEvents<ProspectHistory> prospcetHistoryRepo,
            IRepositoryWithEvents<MasterPropertyType> propertyTypeRepo,
            IHubContext<DialerHub, IDialerClient> dialerClient,
            IRepositoryWithEvents<AutoDialerAudit> autoDialerAuditRepo
            )
        {
            _integrationAccountInfoRepo = integrationAccountInfoRepo;
            _logger = logger;
            _ivrCommonCallLogRepo = ivrCommonCallLogRepo;
            _leadRepo = leadRepo;
            _duplicateInfoRepo = duplicateInfoRepo;
            _userService = userService;
            _leadStatusRepo = leadStatusRepo;
            _customLeadStatusRepo = customLeadStatusRepo;
            _globalSettingsRepo = globalSettingsRepo;
            _leadRepositoryAsync = leadRepositoryAsync;
            _addressRepo = addressRepo;
            _leadHistoryRepo = leadHistoryRepo;
            _npgsqlRepo = npgsqlRepo;
            _notificationSenderService = notificationSenderService;
            _dapperRepo = dapperRepo;
            _prospectRepo = prospectRepo;
            _customProspectStatusRepo = customProspectStatusRepo;
            _masterProspectSourceRepo = masterProspectSourceRepo;
            _prospcetHistoryRepo = prospcetHistoryRepo;
            _propertyTypeRepo = propertyTypeRepo;
            _dialerClient = dialerClient;
            _autoDialerAuditRepo = autoDialerAuditRepo;
        }

        public async Task<Response<bool>> Handle(CommonIVRWebhookRequest request, CancellationToken cancellationToken)
        {
            if (string.IsNullOrWhiteSpace(request.ApiKey))
            {
                _logger.Information($"CommonIVRWebhookRequestHandler -> ApiKey is empty");
                return new(false)
                {
                    Data = false,
                    Message = "ApiKey cannot be null",
                    Succeeded = false,
                };
            }
            if (string.IsNullOrWhiteSpace(request.ServiceProvider))
            {
                _logger.Information($"CommonIVRWebhookRequestHandler -> ServiceProvider is empty");
                return new(false)
                {
                    Data = false,
                    Message = "ServiceProvider cannot be null",
                    Succeeded = false,
                };
            }
            try
            {
                IVRCallStatus matchedEnum = IVRCallStatus.None; //Used for any mapping on Callstatus Avialable
                string? matchedStatus = "";//Used for any mapping on Callstatus Avialable as string to store in existing callStatus column

                request.AccountId = AccountIdHelper.GetAccountId(request.ApiKey);
                IntegrationAccountInfo? integrationAccountInfo = await _integrationAccountInfoRepo.FirstOrDefaultAsync(new GetIntegrationAccountInfoWithIVRConfigSpec(request.AccountId), cancellationToken);
                if (integrationAccountInfo == null)
                {
                    _logger.Information($"CommonIVRWebhookRequestHandler -> IntegrationAccountInfo is null");
                    return new(false)
                    {
                        Data = false,
                        Message = "IVR account Not Found",
                        Succeeded = false,
                    };
                }

                IVRCommonCallLogDto? callLogDto = null;
                if (request.IsRedirectedRequest ?? false)
                {
                    callLogDto = request.RequestBody?.Adapt<IVRCommonCallLogDto>();
                }
                else
                {
                    #region Checking if PayloadMapping exists
                    IVRPayloadMapping? ivrPayloadMapping = integrationAccountInfo.IVRPayloadMapping;
                    if (ivrPayloadMapping == null)
                    {
                        _logger.Information($"CommonIVRWebhookRequestHandler -> No Payload Mapping was found");
                        return new(false)
                        {
                            Data = false,
                            Message = "No Payload Mapping was found",
                            Succeeded = false,
                        };
                    }
                    //Getting mapped data of the call log
                    callLogDto = await GetCallLogDataAsync(request.RequestBody, ivrPayloadMapping, request.IsPayloadInFormData);
                    if (!string.IsNullOrEmpty(callLogDto.CallStatus) &&
                        ivrPayloadMapping.CallStatusMappings != null)
                    {
                        var match = ivrPayloadMapping.CallStatusMappings
                            .FirstOrDefault(m => string.Equals(
                                m.Value,
                                callLogDto.CallStatus,
                                StringComparison.OrdinalIgnoreCase
                            ));

                        if (!match.Equals(default(KeyValuePair<IVRCallStatus, string>)))
                        {
                            matchedEnum = match.Key; // Enum key from the matching mapping
                            matchedStatus = match.Value;
                        }
                        if (string.IsNullOrWhiteSpace(matchedStatus))
                        {
                            callLogDto.CallStatus = matchedStatus;
                        }
                    }
                    #endregion
                }

                //Fetch existing call log
                IVRCommonCallLog? existingCallLog = null;
                IVRCommonCallLog? commonCallLog = null;
                var tenantId = await _npgsqlRepo.GetTenantId(request.AccountId);
                Domain.Entities.GlobalSettings? globalSettings = await _globalSettingsRepo.FirstOrDefaultAsync(new GetGlobalSettingsSpec(), cancellationToken);
                if (!string.IsNullOrWhiteSpace(callLogDto?.CallId))
                {
                    existingCallLog = await _ivrCommonCallLogRepo.FirstOrDefaultAsync(new IVRCommonCallLogByCallIdSpec(callLogDto.CallId), cancellationToken);
                    #region AutoDialer setUp
                    if (!string.IsNullOrWhiteSpace(globalSettings?.CallSettings) )
                    {
                        var callSettings = JsonConvert.DeserializeObject<CallSettings>(globalSettings?.CallSettings);
                        if (callSettings?.IsAutoDialerEnabled ?? false && existingCallLog != null)
                        {
                            object returnObject = new object(); // default placeholder

                            var leadDetails = await _autoDialerAuditRepo
                                .FirstOrDefaultAsync(new GetAuditByCallLogIdSpec(existingCallLog?.Id));

                            if (leadDetails != null)
                            {
                                leadDetails.CallStatus = matchedEnum;

                                // Create anonymous object to send
                                returnObject = new
                                {
                                    leadDetails.Id,
                                    leadDetails.CallStatus
                                };
                            }
                            await _dialerClient.Clients.Group(tenantId).SendCallEndedDetails(returnObject);
                        }
                    }
                    #endregion
                }
                if (existingCallLog != null)
                {
                    try
                    {
                        //Updating existing call log
                        existingCallLog = GetUpdatedCallLog(existingCallLog, callLogDto);
                        existingCallLog.ServiceProviderName = request.ServiceProvider.Replace(" ", "").ToLower();
                        await _ivrCommonCallLogRepo.UpdateAsync(existingCallLog, cancellationToken);

                        //Updating existing lead
                        var leadInfo = await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(existingCallLog.LeadId ?? Guid.Empty, false));
                        if (!string.IsNullOrWhiteSpace(existingCallLog.CallRecordingURL) && leadInfo != null)
                        {
                            if (!leadInfo.CallRecordingUrls?.Any(i => i.Value.Equals(existingCallLog.CallRecordingURL)) ?? false)
                            {
                                if (leadInfo.CallRecordingUrls?.Any() ?? false)
                                {
                                    leadInfo.CallRecordingUrls.Add(DateTime.UtcNow, existingCallLog.CallRecordingURL);
                                }
                                else
                                {
                                    leadInfo.CallRecordingUrls ??= new() { { DateTime.UtcNow, existingCallLog.CallRecordingURL } };
                                }
                            }
                            leadInfo.CallRecordingUrls ??= new() { { DateTime.UtcNow, existingCallLog.CallRecordingURL } };
                            await _leadRepo.UpdateAsync(leadInfo, cancellationToken);
                        }
                        var prospectInfo = await _prospectRepo.FirstOrDefaultAsync(new GetProspectByIdSpecs(existingCallLog.ProspectId ?? Guid.Empty, false));
                        if (!string.IsNullOrWhiteSpace(existingCallLog.CallRecordingURL) && prospectInfo != null)
                        {
                            if (!prospectInfo.CallRecordingUrls?.Any(i => i.Value.Equals(existingCallLog.CallRecordingURL)) ?? false)
                            {
                                if (prospectInfo.CallRecordingUrls?.Any() ?? false)
                                {
                                    prospectInfo.CallRecordingUrls.Add(DateTime.UtcNow, existingCallLog.CallRecordingURL);
                                }
                                else
                                {
                                    prospectInfo.CallRecordingUrls ??= new() { { DateTime.UtcNow, existingCallLog.CallRecordingURL } };
                                }
                            }
                            prospectInfo.CallRecordingUrls ??= new() { { DateTime.UtcNow, existingCallLog.CallRecordingURL } };
                            await _prospectRepo.UpdateAsync(prospectInfo, cancellationToken);
                        }
                        return new()
                        {
                            Data = true,
                            Succeeded = true
                        };
                    }
                    catch(Exception ex)
                    {
                        _logger.Information($"CommonIVRWebhookRequestHandler -> Exception : " + ex.Message);
                        return new(false);
                    }
                }
                else
                {
                    commonCallLog = callLogDto?.Adapt<IVRCommonCallLog>();
                    commonCallLog.ServiceProviderName = request.ServiceProvider.Replace(" ", "").ToLower();
                    commonCallLog.AccountId = request.AccountId;
                }
                if (string.IsNullOrWhiteSpace(commonCallLog.CustomerNumber))
                {
                    _logger.Information($"CommonIVRWebhookRequestHandler -> Mobile number cannot be null or empty");
                    return new(false);
                   // throw new Exception("Mobile number cannot be null or empty");
                }

                var mobile = ListingSitesHelper.ConcatenatePhoneNumberV2(callLogDto?.CountryCode ?? globalSettings?.Countries?.FirstOrDefault()?.DefaultCallingCode, commonCallLog.CustomerNumber, globalSettings, integrationAccountInfo.CountryCode ?? string.Empty);
                List<Domain.Entities.Lead>? existingLeads = new();
                List<Domain.Entities.Prospect>? existingProspects = new();

                UserDetailsDto? answeredAgentInfo = null;
                commonCallLog.AgentNumber = Regex.Replace(commonCallLog.AgentNumber ?? string.Empty, "[^0-9.+-]", "");
                if (!string.IsNullOrWhiteSpace(commonCallLog.AgentNumber))
                {
                    try
                    {
                        answeredAgentInfo = await _userService.GetByPhoneAsync(commonCallLog.AgentNumber, cancellationToken);
                    }
                    catch (NotFoundException ex)
                    {
                        _logger.Information($"CommonIVRWebhookRequestHandler -> UserNotFound Exception : {JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Culture = System.Globalization.CultureInfo.CurrentCulture, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                    }
                }
                var isAdmin = _dapperRepo.IsAdminAsync(answeredAgentInfo?.Id ?? Guid.Empty, tenantId ?? string.Empty).Result;
                bool shouldCreateLead = false;
                bool shouldCreateData = false;
                Guid? responseAfterUpdatingExistingLeads = null;
                Guid? responseAfterUpdatingExistingProspects = null;
                IVRCommonCallLog? callLogInfo = commonCallLog;
                callLogInfo.ServiceProviderName = request.ServiceProvider;
                if (!string.IsNullOrWhiteSpace(commonCallLog.CustomerNumber))
                {
                    if (callLogInfo != null && (callLogInfo.Direction?.Contains("out", StringComparison.InvariantCultureIgnoreCase) ?? false))
                    {
                        if (callLogInfo.ProspectId != null && callLogInfo.ProspectId != Guid.Empty)
                        {
                            existingProspects.Add(await _prospectRepo.FirstOrDefaultAsync(new GetProspectByIdSpecs(callLogInfo.ProspectId ?? Guid.Empty, false)) ?? new());

                            if (existingProspects.Count() > 0)
                            {
                                try
                                {
                                    await UpdateExistingProspectAsync(existingProspects, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateData, isAdmin, tenantId);
                                    return new(true);
                                }
                                catch(Exception ex)
                                {
                                    _logger.Information($"CommonIVRWebhookRequestHandler -> Exception : " + ex.Message);
                                    return new(false);
                                }
                            }
                        }
                        if (callLogInfo.LeadId != null && callLogInfo.LeadId != Guid.Empty)
                        {
                            existingLeads.Add(await _leadRepo.FirstOrDefaultAsync(new LeadByIdSpec(callLogInfo.LeadId ?? Guid.Empty, false)) ?? new());
                            if (existingLeads.Count() > 0)
                            {
                                try
                                {
                                    await UpdateExistingLeadAsync(existingLeads, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateLead, isAdmin, tenantId);
                                    return new(true);
                                }
                                catch(Exception ex)
                                {
                                    _logger.Information($"CommonIVRWebhookRequestHandler -> Exception : " + ex.Message);
                                    return new(false);
                                }
                            }
                        }
                    }
                }
                if (!string.IsNullOrWhiteSpace(commonCallLog.CustomerNumber))
                {
                    existingLeads = (await _leadRepo.ListAsync(new GetLeadByContactNoSpec(mobile.Length > 10 ? mobile[^10..] : mobile), cancellationToken))?.ToList();
                    existingProspects = (await _prospectRepo.ListAsync(new GetProspectByContactNoSpecs(mobile.Length > 10 ? mobile[^10..] : mobile, Guid.Empty), cancellationToken))?.ToList();
                }
                if (callLogInfo.Direction?.Contains("out", StringComparison.InvariantCultureIgnoreCase) ?? false)
                {
                    if (globalSettings?.DirectionOfLeadCreation == DirectionOfLeadCreation.Data)
                    {
                        (shouldCreateData, responseAfterUpdatingExistingProspects) = await UpdateExistingProspectAsync(existingProspects, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateData, isAdmin, tenantId);
                        if (shouldCreateData)
                        {
                            (shouldCreateLead, responseAfterUpdatingExistingLeads) = await UpdateExistingLeadAsync(existingLeads, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateLead, isAdmin, tenantId);
                            if (shouldCreateLead)
                            {
                                if (globalSettings?.DirectionOfLeadCreation == DirectionOfLeadCreation.Data)
                                {
                                    await CreateProspectAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo);
                                }
                                else
                                {
                                    await CreateLeadAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo, globalSettings);
                                }
                            }
                        }
                    }
                    else
                    {
                        (shouldCreateLead, responseAfterUpdatingExistingLeads) = await UpdateExistingLeadAsync(existingLeads, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateLead, isAdmin, tenantId);
                        if (shouldCreateLead)
                        {
                            (shouldCreateData, responseAfterUpdatingExistingProspects) = await UpdateExistingProspectAsync(existingProspects, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateData, isAdmin, tenantId);
                            if (shouldCreateData)
                            {
                                if (globalSettings?.DirectionOfLeadCreation == DirectionOfLeadCreation.Data)
                                {
                                    await CreateProspectAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo);
                                }
                                else
                                {
                                    await CreateLeadAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo, globalSettings);
                                }
                            }
                        }
                    }
                }
                else
                {
                    if (globalSettings?.DirectionOfLeadCreation == DirectionOfLeadCreation.Data)
                    {
                        (shouldCreateData, responseAfterUpdatingExistingProspects) = await UpdateExistingProspectAsync(existingProspects, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateData, isAdmin, tenantId);
                        if (shouldCreateData)
                        {
                            await CreateProspectAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo);
                        }
                    }
                    else if (globalSettings?.DirectionOfLeadCreation == DirectionOfLeadCreation.Lead)
                    {
                        (shouldCreateLead, responseAfterUpdatingExistingLeads) = await UpdateExistingLeadAsync(existingLeads, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateLead, isAdmin, tenantId);
                        if (shouldCreateLead)
                        {
                            await CreateLeadAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo, globalSettings);
                        }
                    }
                    else
                    {
                        (shouldCreateLead, responseAfterUpdatingExistingLeads) = await UpdateExistingLeadAsync(existingLeads, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateLead, isAdmin, tenantId);
                        if (shouldCreateLead)
                        {
                            (shouldCreateData, responseAfterUpdatingExistingProspects) = await UpdateExistingProspectAsync(existingProspects, callLogInfo, answeredAgentInfo, request, existingCallLog, callLogDto, cancellationToken, shouldCreateData, isAdmin, tenantId);
                            if (shouldCreateData)
                            {
                                if (globalSettings?.DirectionOfLeadCreation == DirectionOfLeadCreation.Data)
                                {
                                    await CreateProspectAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo);
                                }
                                else if (globalSettings?.DirectionOfLeadCreation == DirectionOfLeadCreation.Lead)
                                {
                                    await CreateLeadAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo, globalSettings);
                                }
                                else
                                {
                                    await CreateLeadAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo, globalSettings);
                                    await CreateProspectAsync(callLogInfo, cancellationToken, mobile, integrationAccountInfo, true);
                                }
                            }
                        }
                    }
                }
                return new(true);
            }
            catch(Exception ex) {
                _logger.Information($"CommonIVRWebhookRequestHandler -> Exception : " + ex.Message);
                return new(false);
            }
        }

        public async Task CreateProspectAsync(IVRCommonCallLog callLogInfo,
                                                        CancellationToken cancellationToken,
                                                        string? mobile,
                                                        IntegrationAccountInfo integrationAccountInfo,
                                                        bool? shouldUpdateCallLog = null)

        {
            var prospect = callLogInfo.Adapt<Domain.Entities.Prospect>();
            var customnewStatus = (await _customProspectStatusRepo.ListAsync(cancellationToken)).Where(i => i.Status == "new");
            prospect.Name = "IVR " + ((callLogInfo?.StartStamp?.ToString("dd-MM-yyyy HH:mm:ss")) ?? (DateTime.UtcNow.ToString("dd-MM-yyyy HH:mm:ss") + " UTC"));
            prospect.ContactNo = mobile ?? string.Empty;
            prospect.CreatedBy = integrationAccountInfo.CreatedBy;
            prospect.LastModifiedBy = integrationAccountInfo.LastModifiedBy;
            string name = prospect.Name.Trim();
            prospect.Status = customnewStatus?.FirstOrDefault();
            prospect.AccountId = integrationAccountInfo.Id;
            prospect.AgencyName = integrationAccountInfo?.AgencyName;
            var subSource = integrationAccountInfo?.AccountName?.ToLower();
            if (!string.IsNullOrEmpty(callLogInfo.VirtualNumber))
            {
                subSource = subSource + " - " + callLogInfo.VirtualNumber?.ToLower();
            }
            var allSource = await _masterProspectSourceRepo.ListAsync();
            var ivrSource = (allSource).FirstOrDefault(i => i.DisplayName == "IVR");
            var directSource = (allSource).FirstOrDefault(i => i.DisplayName == "Direct");
            //var enquiry = new LeadEnquiry() { LeadSource = LeadSource.IVR, EnquiredFor = EnquiryType.Buy, IsPrimary = true, SubSource = subSource };
            var enquiry = new ProspectEnquiry() { Source = (ivrSource ?? directSource), EnquiryTypes = new List<EnquiryType> { EnquiryType.Buy }, IsPrimary = true, SubSource = subSource };
            if (!string.IsNullOrWhiteSpace(callLogInfo.CallRecordingURL))
            {
                if (prospect.CallRecordingUrls?.All(i => !i.Value.Equals(callLogInfo.CallRecordingURL)) ?? false)
                {
                    prospect.CallRecordingUrls.Add(DateTime.UtcNow, callLogInfo.CallRecordingURL);
                }
                else
                {
                    prospect.CallRecordingUrls = new Dictionary<DateTime, string>() { { DateTime.UtcNow, callLogInfo.CallRecordingURL } };
                }
            }
            prospect.Notes ??= string.Empty;
            prospect.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.StartStamp.ToString())) ? "Start Stamp - " + callLogInfo.StartStamp + ",\n" : string.Empty;
            prospect.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.EndStamp.ToString())) ? "End Stamp - " + callLogInfo.EndStamp + ",\n" : string.Empty;
            prospect.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.HangupCause)) ? "Hang Up Cause - " + callLogInfo.HangupCause + ",\n" : string.Empty;
            prospect.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.AgentName)) ? "Answered Agent Name - " + callLogInfo.AgentName + ",\n" : string.Empty;
            prospect.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.CallStatus)) ? "Call Status - " + callLogInfo.CallStatus + ",\n" : string.Empty;


            #region Lead Assignment
            IVRAssignment ivrAssignment = new();
            try
            {
                if (integrationAccountInfo != null)
                {
                    ivrAssignment = await IntegrationAssignmentHelper.GetAssignmentDetailsForIVRAccountAsync(_integrationAccountInfoRepo, LeadSource.IVR, integrationAccountInfo.Id, callLogInfo.VirtualNumber);
                }
                if (!string.IsNullOrWhiteSpace(ivrAssignment?.AgencyName))
                {
                    prospect.AgencyName = ivrAssignment.AgencyName;
                }
                if (ivrAssignment?.Assignment?.Project != null)
                {
                    #region Automation

                    if (prospect.Projects != null && ivrAssignment?.Assignment?.Project != null && ivrAssignment?.Assignment?.Project?.IsDeleted == false && ivrAssignment?.Assignment?.Project?.IsArchived == false)
                    {
                        prospect.Projects.Add(ivrAssignment.Assignment.Project);
                    }
                    else if (ivrAssignment?.Assignment?.Project != null && ivrAssignment?.Assignment?.Project?.IsDeleted == false && ivrAssignment?.Assignment?.Project?.IsArchived == false)
                    {
                        prospect.Projects ??= new List<Domain.Entities.Project>() { ivrAssignment.Assignment.Project };
                    }
                    #endregion
                }
                Address? address = null;
                var assignedLocation = ivrAssignment?.Assignment?.Location;
                if (assignedLocation != null)
                {
                    var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                    if (existingAddress != null)
                    {
                        address = existingAddress;
                    }
                    else
                    {
                        address = assignedLocation.MapToAddress();
                        address.Location = assignedLocation;
                        await _addressRepo.AddAsync(address);
                    }
                }
                if (address != null)
                {
                    //enquiry.Address = address;
                    enquiry.Addresses = new List<Address> { address };
                }
                prospect.Enquiries = new List<ProspectEnquiry>() { enquiry };
            }
            catch (Exception ex)
            {
                throw;
            }

            if (!string.IsNullOrEmpty(callLogInfo.AgentNumber))
            {
                var agentContactNumber = callLogInfo.AgentNumber;
                UserDetailsDto? userDetails = null;
                try
                {
                    userDetails = await _userService.GetByPhoneAsync(agentContactNumber, cancellationToken);
                }
                catch (Exception ex)
                {
                    var error = new LrbError()
                    {
                        ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                        ErrorSource = ex?.Source,
                        StackTrace = ex?.StackTrace,
                        InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                        ErrorModule = "CommonIVRWebhookRequestHandler -> Handle() -> GetByPhoneAsync()"
                    };
                    await _leadRepositoryAsync.AddErrorAsync(error);
                }
                if (userDetails != null)
                {
                    prospect.AssignTo = userDetails.Id;
                }
            }
            #endregion

            prospect.AgencyName = integrationAccountInfo != null ? integrationAccountInfo?.AgencyName : string.Empty;
            prospect = await _prospectRepo.AddAsync(prospect);
            #region History
            var statuses = await _customProspectStatusRepo.ListAsync();
            var propertyTypes = await _propertyTypeRepo.ListAsync();
            var prospectVM = prospect.Adapt<ViewProspectDto>();
            var users = new List<string?>
            {
                prospectVM.AssignTo.ToString(),
                prospectVM.LastModifiedBy.ToString(),
                prospectVM.AssignedFrom.ToString(),
                prospectVM.SourcingManager.ToString(),
                prospectVM.LastModifiedBy.ToString()

            };
            var userDetails1 = await _userService.GetListOfUsersByIdsAsync(users, cancellationToken);

            prospectVM = await ProspectHistoryHelper.SetUserViewForProspectV1(prospectVM, userDetails1, cancellationToken);
            var histories = await ProspectHistoryHelper.CreateProspectHistoryForVM(prospectVM, null, Guid.Empty, 1, statuses, propertyTypes, allSource, _userService, cancellationToken);
            await _prospcetHistoryRepo.AddRangeAsync(histories);
            #endregion
            try
            {
                callLogInfo.ProspectId = prospect.Id;
                callLogInfo.UserId = prospect.AssignTo;
                callLogInfo = await UpdateDateTimeKind(callLogInfo);
                if (shouldUpdateCallLog ?? false)
                {
                    await _ivrCommonCallLogRepo.UpdateAsync(callLogInfo, cancellationToken);
                }
                else
                {
                    await _ivrCommonCallLogRepo.AddAsync(callLogInfo, cancellationToken);
                }
            }
            catch (Exception ex)
            {
                _logger.Information($"CommonIVRWebhookRequestHandler -> Exception : {JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Culture = System.Globalization.CultureInfo.CurrentCulture, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
            }
            if (integrationAccountInfo != null)
            {
                integrationAccountInfo.LeadCount++;
                await _integrationAccountInfoRepo.UpdateAsync(integrationAccountInfo);
            }
        }

        public async Task CreateLeadAsync(IVRCommonCallLog callLogInfo,
                                                        CancellationToken cancellationToken,
                                                        string? mobile,
                                                        IntegrationAccountInfo integrationAccountInfo,
                                                        Domain.Entities.GlobalSettings? globalSettings)
        {
            var lead = callLogInfo.Adapt<Domain.Entities.Lead>();
            var customStatus = await _customLeadStatusRepo.FirstOrDefaultAsync(new GetDefaultStatusSpec(), cancellationToken);
            lead.Name = "IVR " + ((callLogInfo?.StartStamp?.ToString("dd-MM-yyyy HH:mm:ss")) ?? (DateTime.UtcNow.ToString("dd-MM-yyyy HH:mm:ss") + " UTC"));
            lead.ContactNo = mobile ?? string.Empty;
            lead.CreatedBy = integrationAccountInfo.CreatedBy;
            //lead.LastModifiedBy = integrationAccountInfo.LastModifiedBy;
            string name = lead.Name.Trim();
            lead.LeadNumber = name[0].ToString().ToUpper() + new Random().Next(1000, 9999).ToString() + DateTime.UtcNow.ToString("FFFFFFF");
            lead.CustomLeadStatus = customStatus ?? (await _customLeadStatusRepo.FirstOrDefaultAsync(new MasterLeadSubStatusByNameSpec(new List<string>() { "new" }), cancellationToken));
            lead.AccountId = integrationAccountInfo.Id;
            lead.TagInfo = new();
            lead.AgencyName = integrationAccountInfo?.AgencyName;
            lead.Agencies = integrationAccountInfo?.Agency != null ? new List<Domain.Entities.Agency>() { integrationAccountInfo.Agency } : lead.Agencies;
            var subSource = integrationAccountInfo?.AccountName?.ToLower();
            if (!string.IsNullOrEmpty(callLogInfo.VirtualNumber))
            {
                subSource = subSource + " - " + callLogInfo.VirtualNumber?.ToLower();
            }
            //var enquiry = new LeadEnquiry() { LeadSource = LeadSource.IVR, EnquiredFor = EnquiryType.Buy, IsPrimary = true, SubSource = subSource };
            var enquiry = new LeadEnquiry() { LeadSource = LeadSource.IVR, EnquiryTypes = new List<EnquiryType> { EnquiryType.Buy }, IsPrimary = true, SubSource = subSource };
            if (!string.IsNullOrWhiteSpace(callLogInfo.CallRecordingURL))
            {
                if (lead.CallRecordingUrls?.All(i => !i.Value.Equals(callLogInfo.CallRecordingURL)) ?? false)
                {
                    lead.CallRecordingUrls.Add(DateTime.UtcNow, callLogInfo.CallRecordingURL);
                }
                else
                {
                    lead.CallRecordingUrls = new Dictionary<DateTime, string>() { { DateTime.UtcNow, callLogInfo.CallRecordingURL } };
                }
            }
            lead.Notes ??= string.Empty;
            lead.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.StartStamp.ToString())) ? "Start Stamp - " + callLogInfo.StartStamp + ",\n" : string.Empty;
            lead.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.EndStamp.ToString())) ? "End Stamp - " + callLogInfo.EndStamp + ",\n" : string.Empty;
            lead.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.HangupCause)) ? "Hang Up Cause - " + callLogInfo.HangupCause + ",\n" : string.Empty;
            lead.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.AgentName)) ? "Answered Agent Name - " + callLogInfo.AgentName + ",\n" : string.Empty;
            lead.Notes += (!string.IsNullOrWhiteSpace(callLogInfo.CallStatus)) ? "Call Status - " + callLogInfo.CallStatus + ",\n" : string.Empty;


            #region Lead Assignment
            IVRAssignment ivrAssignment = new();
            try
            {
                if (integrationAccountInfo != null)
                {
                    ivrAssignment = await IntegrationAssignmentHelper.GetAssignmentDetailsForIVRAccountAsync(_integrationAccountInfoRepo, LeadSource.IVR, integrationAccountInfo.Id, callLogInfo.VirtualNumber);
                }
                if (!string.IsNullOrWhiteSpace(ivrAssignment?.AgencyName))
                {
                    lead.AgencyName = ivrAssignment.AgencyName;
                }
                if (ivrAssignment?.Assignment?.Project != null)
                {
                    #region Automation

                    if (lead.Projects != null && ivrAssignment?.Assignment?.Project != null && ivrAssignment?.Assignment?.Project?.IsArchived == false && ivrAssignment?.Assignment?.Project?.IsDeleted == false)
                    {
                        lead.Projects.Add(ivrAssignment.Assignment.Project);
                    }
                    else if (ivrAssignment?.Assignment?.Project != null && ivrAssignment?.Assignment?.Project?.IsArchived == false && ivrAssignment?.Assignment?.Project?.IsDeleted == false)
                    {
                        lead.Projects ??= new List<Domain.Entities.Project>() { ivrAssignment.Assignment.Project };
                    }
                    #endregion
                }
                Address? address = null;
                var assignedLocation = ivrAssignment?.Assignment?.Location;
                if (assignedLocation != null)
                {
                    var existingAddress = await _addressRepo.FirstOrDefaultAsync(new GetAddressByLocaitonIdSpec(assignedLocation.Id), cancellationToken);
                    if (existingAddress != null)
                    {
                        address = existingAddress;
                    }
                    else
                    {
                        address = assignedLocation.MapToAddress();
                        address.Location = assignedLocation;
                        await _addressRepo.AddAsync(address);
                    }
                }
                if (address != null)
                {
                    //enquiry.Address = address;
                    enquiry.Addresses = new List<Address> { address };
                }
                lead.Enquiries = new List<LeadEnquiry>() { enquiry };
            }
            catch (Exception ex)
            {
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CommonIVRWebhookRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }

            var existingLead = await _leadRepo.FirstOrDefaultAsync(new GetRootLeadSpec(lead.ContactNo, lead.AlternateContactNo), cancellationToken);
            UserDetailsDto? assignedUser = null;
            if (existingLead != null && existingLead.AssignTo != Guid.Empty)
            {
                try
                {
                    assignedUser = await _userService.GetAsync(existingLead?.AssignTo.ToString() ?? Guid.Empty.ToString(), cancellationToken);
                }
                catch (Exception ex)
                {
                }
            }

            if ((globalSettings?.IsStickyAgentEnabled ?? false) && existingLead != null && existingLead.AssignTo != default && assignedUser?.IsActive == true)
            {
                lead.AssignTo = existingLead.AssignTo;
            }
            else
            {
                if (!string.IsNullOrEmpty(callLogInfo.AgentNumber))
                {
                    var agentContactNumber = callLogInfo.AgentNumber;
                    UserDetailsDto? userDetails = null;
                    try
                    {
                        userDetails = await _userService.GetByPhoneAsync(agentContactNumber, cancellationToken);
                    }
                    catch (Exception ex)
                    {
                        var error = new LrbError()
                        {
                            ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                            ErrorSource = ex?.Source,
                            StackTrace = ex?.StackTrace,
                            InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                            ErrorModule = "CommonIVRWebhookRequestHandler -> Handle() -> GetByPhoneAsync()"
                        };
                        await _leadRepositoryAsync.AddErrorAsync(error);
                    }
                    if (userDetails != null)
                    {
                        lead.AssignTo = userDetails.Id;
                    }
                }
            }
            #endregion
            #region Duplicate Tag
            if (existingLead != null)
            {
                lead = lead.AddDuplicateDetail(existingLead.ChildLeadsCount, existingLead.Id);
                existingLead.ChildLeadsCount += 1;
                try
                {
                    await _leadRepo.UpdateAsync(existingLead);
                }
                catch (Exception ex)
                {
                }
            }
            #endregion
            lead.AgencyName = integrationAccountInfo != null ? integrationAccountInfo?.AgencyName : string.Empty;
            if ((lead.OriginalOwner == null || lead.OriginalOwner == Guid.Empty) && lead.AssignTo != Guid.Empty)
            {
                lead.OriginalOwner = lead.AssignTo;
            }
            lead = await _leadRepo.AddAsync(lead);
            var leadDto = lead.Adapt<ViewLeadDto>();
            try
            {
                await leadDto.SetUsersInViewLeadDtoAsync(_userService, cancellationToken, source: leadDto?.Enquiry?.LeadSource);
            }
            catch (Exception ex) { }
            var leadHistory = LeadHistoryHelper.LeadHistoryMapper(leadDto);
            await _leadHistoryRepo.AddAsync(leadHistory);
            try
            {
                callLogInfo.LeadId = lead.Id;
                callLogInfo.UserId = lead.AssignTo;
                if (lead?.AssignTo != null && lead.AssignTo != Guid.Empty)
                {
                    callLogInfo.LastModifiedBy = lead.AssignTo; 
                }             

                callLogInfo = await UpdateDateTimeKind(callLogInfo);
                await _ivrCommonCallLogRepo.AddAsync(callLogInfo, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.Information($"CommonIVRWebhookRequestHandler -> Exception : {JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Culture = System.Globalization.CultureInfo.CurrentCulture, ReferenceLoopHandling = ReferenceLoopHandling.Ignore })}");
                throw;
            }
            if (integrationAccountInfo != null)
            {
                integrationAccountInfo.LeadCount++;
                await _integrationAccountInfoRepo.UpdateAsync(integrationAccountInfo);
            }
            #region Push Notification
            try
            {
                NotificationSettings? notificationSettings = JsonConvert.DeserializeObject<NotificationSettings>(globalSettings?.NotificationSettings ?? string.Empty);
                List<string> notificationResponses = new();
                string? tenantId = await _npgsqlRepo.GetTenantId(integrationAccountInfo?.Id ?? Guid.Empty);
                List<Guid> adminIds = await _npgsqlRepo.GetAdminIdsAsync(tenantId ?? string.Empty);
                if (lead.AssignTo == default || lead.AssignTo == Guid.Empty)
                {
                    _logger.Information($"CommonIVRWebhookRequestHandler -> tenantId : {tenantId} , adminIds : " + JsonConvert.SerializeObject(adminIds));
                    if (adminIds.Any())
                    {
                        List<string> notificationSchduleResponse = new();
                        notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, userIds: adminIds, noOfEntities: 1, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                        notificationResponses.AddRange(notificationSchduleResponse);
                    }
                }
                else if (lead.AssignTo != Guid.Empty)
                {
                    var user = await _userService.GetAsync(lead.AssignTo.ToString(), cancellationToken);
                    if (user != null)
                    {
                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Event.LeadFromIntegration, lead, lead.AssignTo, user.FirstName + " " + user.LastName, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() });
                        notificationResponses.AddRange(notificationSchduleResponse);
                    }
                    List<Guid> userWithManagerIds = new();
                    if (notificationSettings?.IsManagerEnabled ?? false)
                    {
                        List<Guid> managerIds = await _npgsqlRepo.GetReportingManagerUserIdsAsync(new List<Guid> { lead.AssignTo });
                        userWithManagerIds.AddRange(managerIds);
                    }
                    if (notificationSettings?.IsAdminEnabled ?? false)
                    {
                        userWithManagerIds.AddRange(adminIds);
                    }
                    if (user != null && userWithManagerIds.Any())
                    {
                        userWithManagerIds = userWithManagerIds.Distinct().ToList();
                        userWithManagerIds.Remove(lead.AssignTo);
                        List<string> notificationSchduleResponse = await _notificationSenderService.ScheduleNotificationsAsync(Domain.Enums.Event.NotifiyManagerAndAdmins, lead, null, null, topics: new List<string> { lead.CreatedBy.ToString(), lead.LastModifiedBy.ToString() }, userIds: userWithManagerIds);
                        notificationResponses.AddRange(notificationSchduleResponse);
                    }
                }
                _logger.Information($"CommonIVRWebhookRequestHandler -> NotificationSchedulingResponses JobIds : " + JsonConvert.SerializeObject(notificationResponses));
            }
            catch (Exception ex)
            {
                _logger.Information($"CommonIVRWebhookRequestHandler -> Exception -> PushNotification : " + JsonConvert.SerializeObject(ex, new JsonSerializerSettings() { Formatting = Formatting.Indented, ReferenceLoopHandling = ReferenceLoopHandling.Ignore }));
                var error = new LrbError()
                {
                    ErrorMessage = ex?.Message ?? ex?.InnerException?.Message,
                    ErrorSource = ex?.Source,
                    StackTrace = ex?.StackTrace,
                    InnerException = JsonConvert.SerializeObject(ex?.InnerException, new JsonSerializerSettings { ReferenceLoopHandling = ReferenceLoopHandling.Ignore, Formatting = Formatting.Indented }),
                    ErrorModule = "CommonIVRWebhookRequestHandler -> Handle()"
                };
                await _leadRepositoryAsync.AddErrorAsync(error);
            }
            #endregion
        }

        public async Task<(bool, Guid?)> UpdateExistingProspectAsync(List<Domain.Entities.Prospect>? existingProspects,
                                                        IVRCommonCallLog callLogInfo,
                                                        UserDetailsDto? answeredAgentInfo,
                                                        CommonIVRWebhookRequest request,
                                                        IVRCommonCallLog? existingCallLog,
                                                        IVRCommonCallLogDto? callLogDto,
                                                        CancellationToken cancellationToken,
                                                        bool shouldCreateData,
                                                        bool? isAdmin,
                                                        string? tenantId)
        {
            Guid? responseAfterUpdatingExistingProspects = null;
            if (existingProspects?.Any() ?? false)
            {
                if (callLogInfo.Direction?.Contains("out", StringComparison.InvariantCultureIgnoreCase) ?? false)
                {
                    if ((existingProspects?.Any(i => i.AssignTo == Guid.Empty) ?? false) && answeredAgentInfo == null)
                    {
                        responseAfterUpdatingExistingProspects = await UpdateUnAssignedCallLogAsync(existingProspects, answeredAgentInfo, callLogInfo);
                    }
                    else if (existingProspects?.Any(i => (i.AssignTo != Guid.Empty) && (i.AssignTo == answeredAgentInfo?.Id)) ?? false)
                    {
                        responseAfterUpdatingExistingProspects = await UpdateAssignedCallLogAsync(existingProspects, answeredAgentInfo, callLogInfo);
                    }
                    else if (answeredAgentInfo != null)
                    {
                        var subordinateIds = await _dapperRepo.GetSubordinateIdsAsync(answeredAgentInfo.Id, tenantId, viewAllLeads: null, isAdmin);
                        if (existingProspects?.Any(i => subordinateIds.Contains(i.AssignTo)) ?? false)
                        {
                            var initialProspect = existingProspects?.Where(i => subordinateIds.Any(j => j == i.AssignTo)).OrderBy(i => i.CreatedOn).FirstOrDefault();
                            if (initialProspect != null)
                            {
                                responseAfterUpdatingExistingProspects = await UpdateProspectAndCallLogAsync(initialProspect, answeredAgentInfo, callLogInfo);
                            }
                        }
                    }
                }
                else
                {
                    if ((existingProspects?.Any(i => i.AssignTo == Guid.Empty) ?? false) && answeredAgentInfo == null)
                    {
                        responseAfterUpdatingExistingProspects = await UpdateUnAssignedCallLogAsync(existingProspects, answeredAgentInfo, callLogInfo);
                    }
                    else if (existingProspects?.Any(i => (i.AssignTo != Guid.Empty) && (i.AssignTo == answeredAgentInfo?.Id)) ?? false)
                    {
                        responseAfterUpdatingExistingProspects = await UpdateAssignedCallLogAsync(existingProspects, answeredAgentInfo, callLogInfo);
                    }
                }
                if (existingProspects?.Any(i => i.Id == responseAfterUpdatingExistingProspects) ?? false)
                {
                    var prospectInfo = existingProspects?.FirstOrDefault(i => i.Id == responseAfterUpdatingExistingProspects);
                    existingCallLog = await _ivrCommonCallLogRepo.FirstOrDefaultAsync(new IVRCommonCallLogByCallIdSpec(callLogDto?.CallId ?? string.Empty), cancellationToken);
                    if (!string.IsNullOrWhiteSpace(existingCallLog?.CallRecordingURL) && prospectInfo != null)
                    {
                        if (prospectInfo.CallRecordingUrls?.All(i => !i.Value.Equals(existingCallLog.CallRecordingURL)) ?? false)
                        {
                            prospectInfo.CallRecordingUrls.Add(DateTime.UtcNow, existingCallLog.CallRecordingURL);
                        }
                        else prospectInfo.CallRecordingUrls ??= new() { { DateTime.UtcNow, existingCallLog.CallRecordingURL } };
                        await _prospectRepo.UpdateAsync(prospectInfo, cancellationToken);
                    }
                    else if (prospectInfo != null)
                    {
                        await _prospectRepo.UpdateAsync(prospectInfo, cancellationToken);
                    }
                }
                else
                {
                    shouldCreateData = true;
                }
            }
            else
            {
                shouldCreateData = true;
            }
            return (shouldCreateData, responseAfterUpdatingExistingProspects);
        }

        public async Task<(bool, Guid?)> UpdateExistingLeadAsync(List<Domain.Entities.Lead>? existingLeads,
                                                        IVRCommonCallLog callLogInfo,
                                                        UserDetailsDto? answeredAgentInfo,
                                                        CommonIVRWebhookRequest request,
                                                        IVRCommonCallLog? existingCallLog,
                                                        IVRCommonCallLogDto? callLogDto,
                                                        CancellationToken cancellationToken,
                                                        bool shouldCreateLead,
                                                        bool? isAdmin,
                                                        string? tenantId)
        {
            Guid? responseAfterUpdatingExistingLeads = null;
            if (existingLeads?.Any() ?? false)
            {
                if (callLogInfo.Direction?.Contains("out", StringComparison.InvariantCultureIgnoreCase) ?? false)
                {
                    if ((existingLeads?.Any(i => i.AssignTo == Guid.Empty) ?? false) && answeredAgentInfo == null)
                    {
                        responseAfterUpdatingExistingLeads = await UpdateUnAssignedCallLogAsync(existingLeads, answeredAgentInfo, callLogInfo);
                    }
                    else if ((existingLeads?.Any(i => ((i.AssignTo != Guid.Empty && (i.AssignTo == answeredAgentInfo?.Id)) || ((i.SecondaryUserId == answeredAgentInfo?.Id) && (i.SecondaryUserId != Guid.Empty)))) ?? false))
                    {
                        responseAfterUpdatingExistingLeads = await UpdateAssignedCallLogAsync(existingLeads, answeredAgentInfo, callLogInfo);
                    }
                    else if (answeredAgentInfo != null)
                    {
                        var subordinateIds = await _dapperRepo.GetSubordinateIdsAsync(answeredAgentInfo.Id, tenantId, viewAllLeads: null, isAdmin);
                        if (existingLeads?.Any(i => subordinateIds.Contains(i.AssignTo)) ?? false)
                        {
                            var initialLead = existingLeads?.Where(i => subordinateIds.Any(j => j == i.AssignTo)).OrderBy(i => i.CreatedOn).FirstOrDefault();
                            if (initialLead != null)
                            {
                                responseAfterUpdatingExistingLeads = await UpdateLeadAndCallLogAsync(initialLead, answeredAgentInfo, callLogInfo);
                            }
                        }
                    }
                }
                else
                {
                    if ((existingLeads?.Any(i => i.AssignTo == Guid.Empty) ?? false) && answeredAgentInfo == null)
                    {
                        responseAfterUpdatingExistingLeads = await UpdateUnAssignedCallLogAsync(existingLeads, answeredAgentInfo, callLogInfo);
                    }
                    else if ((existingLeads?.Any(i => ((i.AssignTo != Guid.Empty && (i.AssignTo == answeredAgentInfo?.Id)) || ((i.SecondaryUserId == answeredAgentInfo?.Id) && (i.SecondaryUserId != Guid.Empty)))) ?? false))
                    {
                        responseAfterUpdatingExistingLeads = await UpdateAssignedCallLogAsync(existingLeads, answeredAgentInfo, callLogInfo);
                    }
                }
                if (existingLeads?.Any(i => i.Id == responseAfterUpdatingExistingLeads) ?? false)
                {
                    var leadInfo = existingLeads?.FirstOrDefault(i => i.Id == responseAfterUpdatingExistingLeads);
                    existingCallLog = await _ivrCommonCallLogRepo.FirstOrDefaultAsync(new IVRCommonCallLogByCallIdSpec(callLogDto.CallId), cancellationToken);
                    if (!string.IsNullOrWhiteSpace(existingCallLog?.CallRecordingURL) && leadInfo != null)
                    {
                        if (leadInfo.CallRecordingUrls?.All(i => !i.Value.Equals(existingCallLog.CallRecordingURL)) ?? false)
                        {
                            leadInfo.CallRecordingUrls.Add(DateTime.UtcNow, existingCallLog.CallRecordingURL);
                        }
                        else leadInfo.CallRecordingUrls ??= new() { { DateTime.UtcNow, existingCallLog.CallRecordingURL } };
                        await _leadRepo.UpdateAsync(leadInfo, cancellationToken);
                    }
                    else if (leadInfo != null)
                    {
                        await _leadRepo.UpdateAsync(leadInfo, cancellationToken);
                    }
                }
                else
                {
                    shouldCreateLead = true;
                }
            }
            else
            {
                shouldCreateLead = true;
            }
            return (shouldCreateLead, responseAfterUpdatingExistingLeads);
        }
        public static IVRCommonCallLog GetUpdatedCallLog(IVRCommonCallLog callLog, IVRCommonCallLogDto callLogDto)
        {
            callLog.CallId = !string.IsNullOrWhiteSpace(callLog.CallId) ? callLog.CallId : callLogDto.CallId;
            callLog.CustomerNumber = !string.IsNullOrWhiteSpace(callLog.CustomerNumber) ? callLog.CustomerNumber : callLogDto.CustomerNumber;
            callLog.VirtualNumber = !string.IsNullOrWhiteSpace(callLog.VirtualNumber) ? callLog.VirtualNumber : callLogDto.VirtualNumber;
            callLog.AgentNumber = !string.IsNullOrWhiteSpace(callLog.AgentNumber) ? callLog.AgentNumber : callLogDto.AgentNumber;
            callLog.HangupCause = !string.IsNullOrWhiteSpace(callLog.HangupCause) ? callLog.HangupCause : callLogDto.HangupCause;
            callLog.Billsec = !string.IsNullOrWhiteSpace(callLog.Billsec) ? callLog.Billsec : callLogDto.Billsec;
            callLog.DigitsDialed = !string.IsNullOrWhiteSpace(callLog.DigitsDialed) ? callLog.DigitsDialed : callLogDto.DigitsDialed;
            callLog.Direction = !string.IsNullOrWhiteSpace(callLog.Direction) ? callLog.Direction : callLogDto.Direction;
            callLog.AgentName = !string.IsNullOrWhiteSpace(callLog.AgentName) ? callLog.AgentName : callLogDto.AgentName;
            callLog.CallRecordingURL = !string.IsNullOrWhiteSpace(callLog.CallRecordingURL) ? callLog.CallRecordingURL : callLogDto.CallRecordingURL;
            callLog.CallStatus = !string.IsNullOrWhiteSpace(callLog.CallStatus) ? callLog.CallStatus : callLogDto.CallStatus;
            callLog.StartStamp = callLog.StartStamp != null ? callLog.StartStamp?.ConvertAndSetKindAsUtc() : callLogDto.StartStamp?.ConvertAndSetKindAsUtc();
            callLog.AnswerStamp = callLog.AnswerStamp != null ? callLog.AnswerStamp?.ConvertAndSetKindAsUtc() : callLogDto.AnswerStamp?.ConvertAndSetKindAsUtc();
            callLog.EndStamp = callLog.EndStamp != null ? callLog.EndStamp?.ConvertAndSetKindAsUtc() : callLogDto.EndStamp?.ConvertAndSetKindAsUtc();
            callLog.Duration = callLog.Duration != null ? callLog.Duration : callLogDto.Duration;
            callLog.IVRDuration = callLog.IVRDuration != null ? callLog.IVRDuration : callLogDto.IVRDuration;
            callLog.RawData = callLog.RawData != null ? callLog.RawData : callLogDto.RawData;
            return callLog;
        }
        public async Task<IVRCommonCallLogDto> GetCallLogDataAsync(object payloadObject, IVRPayloadMapping payloadMapping, bool? IsPayloadInFormData = null)
        {
            if (payloadObject == null)
            {
                throw new Exception("Payload is null!");
            }
            IVRCommonCallLogDto callLog = new();
            try
            {
                var mappings = payloadMapping.PushEndPointMappings ?? throw new Exception("No Mapping Found!");
                dynamic? payload = null;
                try
                {
                    if (IsPayloadInFormData ?? false)
                    {
                        //payload = payloadObject;
                        string serializedPayloadObject = JsonConvert.SerializeObject(payloadObject);
                        payload = JsonConvert.DeserializeObject<Dictionary<string, object>>(serializedPayloadObject) ?? throw new Exception("Payload is null!");
                    }
                    else
                    {
                        payload = JsonConvert.DeserializeObject<Dictionary<string, object>>(payloadObject?.ToString() ?? string.Empty) ?? throw new Exception("Payload is null!");
                    }
                }
                catch (Exception ex)
                {
                    payload = payloadObject;
                }
                object value = string.Empty;
                try
                {
                    callLog.CustomerNumber = payload.TryGetValue(mappings.GetValueOrDefault("#CustomerNumber#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.VirtualNumber = payload.TryGetValue(mappings.GetValueOrDefault("#VirtualNumber#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.HangupCause = payload.TryGetValue(mappings.GetValueOrDefault("#HangupCause#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.AgentNumber = payload.TryGetValue(mappings.GetValueOrDefault("#AgentNumber#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.Billsec = payload.TryGetValue(mappings.GetValueOrDefault("#BillSec#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.DigitsDialed = payload.TryGetValue(mappings.GetValueOrDefault("#DigitsDialed#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.Direction = payload.TryGetValue(mappings.GetValueOrDefault("#Direction#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.AgentName = payload.TryGetValue(mappings.GetValueOrDefault("#AgentName#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.CallRecordingURL = payload.TryGetValue(mappings.GetValueOrDefault("#CallRecordingURL#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.CallStatus = payload.TryGetValue(mappings.GetValueOrDefault("#CallStatus#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.CallId = payload.TryGetValue(mappings.GetValueOrDefault("#CallId#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.StartStamp = await GetDateTimeFromStringAsync((payload.TryGetValue(mappings.GetValueOrDefault("#StartStamp#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString()));
                    callLog.AnswerStamp = await GetDateTimeFromStringAsync((payload.TryGetValue(mappings.GetValueOrDefault("#AnswerStamp#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString()));
                    callLog.EndStamp = await GetDateTimeFromStringAsync((payload.TryGetValue(mappings.GetValueOrDefault("#EndStamp#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString()));
                    callLog.Duration = await GetLongValueFromStringAsync((payload.TryGetValue(mappings.GetValueOrDefault("#Duration#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString()));
                    callLog.IVRDuration = await GetLongValueFromStringAsync((payload.TryGetValue(mappings.GetValueOrDefault("#IvrDuration#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString()));
                    callLog.LeadId = Guid.TryParse(payload.TryGetValue(mappings.GetValueOrDefault("#LeadId#") ?? string.Empty, out value) ? value?.ToString() : null, out var leadId) ? leadId : Guid.Empty;
                    callLog.ProspectId = Guid.TryParse(payload.TryGetValue(mappings.GetValueOrDefault("#ProspectId#") ?? string.Empty, out value) ? value?.ToString() : null, out var prospectId) ? prospectId : Guid.Empty;
                    callLog.CountryCode = payload.TryGetValue(mappings.GetValueOrDefault("#CountryCode#") ?? string.Empty, out value) ? value?.ToString() : value?.ToString();
                    callLog.RawData = JsonConvert.SerializeObject(payload, Formatting.Indented);
                    if (callLog.Duration == null && callLog.EndStamp != null && callLog.StartStamp != null)
                    {
                        callLog.Duration = (long)((callLog.EndStamp - callLog.StartStamp).Value.TotalSeconds);
                    }
                    if (!string.IsNullOrWhiteSpace(callLog.Direction))
                    {
                        switch (callLog.Direction)
                        {
                            case "clicktocall":
                                callLog.Direction = "outbound";
                                break;
                            default:
                                break;
                        }
                    }
                }
                catch (Exception ex)
                {

                }
                //callLog.Billsec = payload[mappings.GetValueOrDefault("#billSec#") ?? string.Empty];
                //callLog.StartStamp = await GetDateTimeFromStringAsync((string)payload[mappings.GetValueOrDefault("#startStamp#") ?? string.Empty]);
            }
            catch (Exception ex)
            {
                throw;
            }
            return callLog;
        }
        public async Task<DateTime?> GetDateTimeFromStringAsync(string? dateTimeString)
        {
            DateTime? formattedDate = DateTime.TryParse(dateTimeString, out var date) ? date : null;
            return formattedDate;
        }
        public async Task<long?> GetLongValueFromStringAsync(string? longString)
        {
            long? longValue = long.TryParse(longString, out var l) ? l : null;
            return longValue;
        }
        public async Task<Guid> UpdateUnAssignedCallLogAsync(List<Domain.Entities.Lead>? existingLeads, UserDetailsDto? answeredAgentInfo, IVRCommonCallLog callLogInfo)
        {
            Domain.Entities.Lead? unassignedLead = existingLeads?.FirstOrDefault(i => i.AssignTo == Guid.Empty);
            if (unassignedLead != null)
            {
                callLogInfo.LeadId = unassignedLead.Id;
                callLogInfo.UserId = Guid.Empty;
                callLogInfo = await UpdateDateTimeKind(callLogInfo);
                await _ivrCommonCallLogRepo.AddAsync(callLogInfo);
                return unassignedLead.Id;
            }
            return Guid.Empty;
        }
        public async Task<Guid> UpdateAssignedCallLogAsync(List<Domain.Entities.Lead>? existingLeads, UserDetailsDto? answeredAgentInfo, IVRCommonCallLog callLogInfo)
        {
            Domain.Entities.Lead? assignedLeadToCurrentUser = existingLeads?.FirstOrDefault(i => ((i.AssignTo != default) && i.AssignTo == answeredAgentInfo?.Id) || ((i.SecondaryUserId != default) && i.SecondaryUserId == answeredAgentInfo?.Id));
            if (assignedLeadToCurrentUser != null)
            {
                callLogInfo.LeadId = assignedLeadToCurrentUser.Id;
                callLogInfo.UserId = assignedLeadToCurrentUser.AssignTo == answeredAgentInfo?.Id ? assignedLeadToCurrentUser.AssignTo : assignedLeadToCurrentUser.SecondaryUserId;
                callLogInfo = await UpdateDateTimeKind(callLogInfo);
                await _ivrCommonCallLogRepo.AddAsync(callLogInfo);
                return assignedLeadToCurrentUser.Id;
            }
            return Guid.Empty;
        }
        public async Task<Guid> UpdateLeadAndCallLogAsync(Domain.Entities.Lead? initialLead, UserDetailsDto? answeredAgentInfo, IVRCommonCallLog callLogInfo)
        {
            if (initialLead != null && answeredAgentInfo != null)
            {
                callLogInfo.LeadId = initialLead.Id;
                callLogInfo.UserId = answeredAgentInfo.Id;
                callLogInfo = await UpdateDateTimeKind(callLogInfo);
                await _ivrCommonCallLogRepo.AddAsync(callLogInfo);
                return initialLead.Id;
            }
            return Guid.Empty;
        }
        public async Task<Guid> UpdateUnAssignedCallLogAsync(List<Prospect>? existingProspects, UserDetailsDto? answeredAgentInfo, IVRCommonCallLog callLogInfo)
        {
            Prospect? unassignedProspect = existingProspects?.FirstOrDefault(i => i.AssignTo == Guid.Empty);
            if (unassignedProspect != null)
            {
                callLogInfo.ProspectId = unassignedProspect.Id;
                callLogInfo.UserId = Guid.Empty;
                callLogInfo = await UpdateDateTimeKind(callLogInfo);
                await _ivrCommonCallLogRepo.AddAsync(callLogInfo); 
                return unassignedProspect.Id;
            }
            return Guid.Empty;
        }
        public async Task<Guid> UpdateAssignedCallLogAsync(List<Prospect>? existingProspects, UserDetailsDto? answeredAgentInfo, IVRCommonCallLog callLogInfo)
        {
            Prospect? assignedProspectToCurrentUser = existingProspects?.FirstOrDefault(i => ((i.AssignTo != default) && i.AssignTo == answeredAgentInfo?.Id));
            if (assignedProspectToCurrentUser != null)
            {
                callLogInfo.ProspectId = assignedProspectToCurrentUser.Id;
                callLogInfo.UserId = assignedProspectToCurrentUser.AssignTo;
                callLogInfo = await UpdateDateTimeKind(callLogInfo);
                await _ivrCommonCallLogRepo.AddAsync(callLogInfo);
                return assignedProspectToCurrentUser.Id;
            }
            return Guid.Empty;
        }
        public async Task<Guid> UpdateProspectAndCallLogAsync(Prospect? initialProspect, UserDetailsDto? answeredAgentInfo, IVRCommonCallLog callLogInfo)
        {
            if (initialProspect != null && answeredAgentInfo != null)
            {
                callLogInfo.ProspectId = initialProspect.Id;
                callLogInfo.UserId = answeredAgentInfo.Id;
                callLogInfo = await UpdateDateTimeKind(callLogInfo);
                await _ivrCommonCallLogRepo.AddAsync(callLogInfo);
                return initialProspect.Id;
            }
            return Guid.Empty;
        }
        public async Task<IVRCommonCallLog> UpdateDateTimeKind(IVRCommonCallLog ivrCommonCallLog)
        {
            ivrCommonCallLog.StartStamp = ivrCommonCallLog.StartStamp?.ConvertAndSetKindAsUtc() ?? ivrCommonCallLog.StartStamp;
            ivrCommonCallLog.EndStamp = ivrCommonCallLog.EndStamp?.ConvertAndSetKindAsUtc() ?? ivrCommonCallLog.EndStamp;
            ivrCommonCallLog.AnswerStamp = ivrCommonCallLog.AnswerStamp?.ConvertAndSetKindAsUtc() ?? ivrCommonCallLog.AnswerStamp;
            return ivrCommonCallLog;
        }
    }
}
