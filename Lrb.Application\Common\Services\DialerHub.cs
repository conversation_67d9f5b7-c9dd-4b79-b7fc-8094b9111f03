﻿using Lrb.Application.AutoDialer.Web.Dtos;
using Microsoft.AspNetCore.SignalR;

namespace Lrb.Application.Common.Services
{
    public class DialerHub : Hub<IDialerClient>
    {
        public Guid? UserId { get; set; }
        public string? TenantId { get; set; }
        private readonly IRepositoryWithEvents<Domain.Entities.AutoDialerAudit> _autoDailerRepo;
        private readonly IRepositoryWithEvents<Domain.Entities.Lead> _leadRepo;
        private readonly IDapperRepository _dapperRepository;
        public DialerHub(IRepositoryWithEvents<Domain.Entities.Lead> leadRepo, IRepositoryWithEvents<Domain.Entities.AutoDialerAudit> autoDailerRepo, IDapperRepository dapperRepository)
        {
            _leadRepo = leadRepo;
            _autoDailerRepo = autoDailerRepo;
            _dapperRepository = dapperRepository;
        }
        Task JoinGroup(string tenant) => Groups.AddToGroupAsync(Context.ConnectionId, tenant);
        Task LeaveGroup(string tenant) => Groups.RemoveFromGroupAsync(Context.ConnectionId, tenant);

        public override Task OnConnectedAsync()
        {
            var httpContext = Context.GetHttpContext();
            var tenantId = httpContext?.Request.Query["tenantId"].ToString();

            if (!string.IsNullOrEmpty(tenantId))
            {
                // Join a group like tenant_user for scoped push
                Groups.AddToGroupAsync(Context.ConnectionId, $"{tenantId}");
            }
            return base.OnConnectedAsync();
        }

        public async Task RequestForGroupJoinAsync(string tenant)
        {
            tenant = ValidateAndGetTenant(tenant);
            await JoinGroup(tenant);
            //await _whatsAppService.StoreConnectionDetailsAsync(Context.ConnectionId, false, tenant, string.Empty, true);
            await Clients.Group(tenant).ReceiveMessageAsync($"{Context.ConnectionId} joined in {tenant}");
        }

        public async void RequestForDisconnectAsync(string tenant)
        {

            tenant = ValidateAndGetTenant(tenant);
            await LeaveGroup(tenant);
            //await _whatsAppService.StoreConnectionDetailsAsync(Context.ConnectionId, true, tenant, "disconnected", false);
            await Clients.Group(tenant).ReceiveMessageAsync($"{Context.ConnectionId} disconnected {tenant}");
            Context.Abort();
        }

        private string ValidateAndGetTenant(string tenant)
        {
            if (string.IsNullOrEmpty(tenant)) { throw new Exception("Tenant id is required."); }
            return tenant;
        }

        private string ValidateAndGetUser(string user)
        {
            if (string.IsNullOrEmpty(user)) { throw new Exception("User id is required."); }
            return user;
        }
        public async Task SendDialerEventToGroupAsync(string tenant, string userId)
        {
            tenant = ValidateAndGetTenant(tenant);
            userId = ValidateAndGetUser(userId);
            try
            {
                if (!string.IsNullOrWhiteSpace(tenant) && !string.IsNullOrWhiteSpace(userId))
                {
                    var response = await GetLatestLeadDetails(tenant, userId);
                    await Clients.Group(tenant).ReceiveDialerEvent(response);
                }
                else
                {
                    await Clients.Group(tenant).ReceiveDialerEvent(null);
                }
            }
            catch (Exception ex)
            {
                //await Clients.Group(tenant).ReceiveMessageAsync(new Response<object>(false, ex.Message ?? ex.InnerException?.Message ?? string.Empty));
            }
        }

        public async Task<AutoDialerLeadDetailsDto> GetLatestLeadDetails(string tenant, string? userId)
        {
            if (string.IsNullOrEmpty(userId) || !Guid.TryParse(userId, out var parsedUserId))
            {
                throw new Exception("User id is required.");
            }
            var lead = await _dapperRepository.GetOngoingCallLeadDetails(tenant, parsedUserId);
            return lead;
        }
    }

}
