﻿using Lrb.Application.Identity.Roles;
using Lrb.Application.Team.Web;
using Lrb.Application.TimeZone.Dto;
using Npgsql.EntityFrameworkCore.PostgreSQL.Query.Expressions.Internal;

namespace Lrb.Application.UserDetails.Web
{
    public class UpdateUserDto : IDto
    {
        public Guid UserId { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public string? PhoneNumber { get; set; }
        public string? Email { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? AltEmail { get; set; }
        public string? Address { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? ImageUrl { get; set; }
        public string? PermanentAddress { get; set; }
        #region JobDetails
        public string? EmpNo { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }
        public Guid? ReportsTo { get; set; }
        public Guid? GeneralManager { get; set; }
        public Guid? DepartmentId { get; set; }
        public Guid? DesignationId { get; set; }
        #endregion
        public string? Description { get; set; }
        public List<Identity.Users.UserRoleDto>? UserRoles { get; set; }
        public bool IsAutomationEnabled { get; set; }
        public string? TimeZoneId { get; set; } = default!;
        public bool? ShouldShowTimeZone { get; set; }
        public string? TimeZone { get; set; }
        public TimeSpan? BaseUTcOffset { get; set; }
        public string? LicenseNo { get; set; }
        public bool? IsGeoFenceActive { get; set; }
    }

    public class UserDetailsDto : IDto
    {
        public Guid UserId { get; set; }
        public string? UserName { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public bool IsActive { get; set; } = true;
        public bool EmailConfirmed { get; set; }
        public string? ImageUrl { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? Address { get; set; }
        public string? Email { get; set; }
        public string? AltEmail { get; set; }
        public BloodGroupType? BloodGroup { get; set; }
        public Gender? Gender { get; set; }
        public string? PermanentAddress { get; set; }
        public string? PhoneNumber { get; set; }
        public double ProfileCompletion { get; set; }
        #region JobDetails
        public string? EmpNo { get; set; }
        public string? OfficeName { get; set; }
        public string? OfficeAddress { get; set; }
        public UserDto? ReportsTo { get; set; }
        public UserDto? GeneralManager { get; set; }
        public DepartmantDto? Department { get; set; }
        public DesignationDto? Designation { get; set; }
        #endregion
        public string? Description { get; set; }
        public Dictionary<DocumentType, List<ViewUserDocumentDto>>? Documents { get; set; } = new();
        public int LeadCount { get; set; }
        #region RolesAndPermission
        public List<UserRoleDto>? UserRoles { get; set; }
        public List<RolePermissionDto> RolePermission { get; set; } = new();
        #endregion
        public bool IsDeleted { get; set; }
        public DateTime? LastModifiedOn { get; set; }
        public DateTime? CreatedOn { get; set; }
        public Guid CreatedBy { get; set; }
        public Guid LastModifiedBy { get; set; }
        public bool IsAutomationEnabled { get; set; } = default!;
        public  CreateTimeZoneDto? TimeZoneInfo { get; set; }
        public bool? ShouldShowTimeZone { get; set; }
        public string? LicenseNo { get; set; }
        public bool? IsGeoFenceActive { get; set; }
        #region AutoDialer
        public bool? IsAutoDialerEnabled { get; set; }
        public bool? IsAvilableForCall { get; set; }
        #endregion
    }
    public class UserRoleDto : IDto
    {
        public string RoleId { get; set; } = default!;
        public string Name { get; set; } = default!;
    }
    public class UsersDto : IDto
    {
        public string? UserName { get; set; }
        public string? ReportsTo { get; set; }
        public string? Departments { get; set; }
        public string? Designations { get; set; }
        public List<UserRoleDto>? UserRoles { get; set; }
        public int LeadCount { get; set; }

    }
    public class ExportUserFormattedDto : IDto
    {
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? PhoneNumbers { get; set; }
        public string? AltPhoneNumber { get; set; }
        public string? Location { get; set; }
        public string? ReportsTo { get; set; }
        public string? GeneralManager {  get; set; }
        public string? Departments { get; set; }
        public string? Designations { get; set; }
        public string? UserRoles { get; set; }
        public int LeadCount { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime? CreatedOn { get; set; }
        public DateTime? LastModifiedOn { get; set; }
    }
    public class UserFilterDto : IDto
    {
        public List<Guid>? UserIds { get; set; }
        public List<Guid>? ReportsToIds { get; set; }
        public List<Guid>? GeneralManagerIds { get; set; }
        public List<string>? Departments { get; set; }
        public List<string>? Designations { get; set; }
    }
    public class UserFormattedFilterDto : IDto
    {
        public string? UserNames { get; set; }
        public string? ReportsTo { get; set; }
        public string? Departments { get; set; }
        public string? Designations { get; set; }
        public string? GeneralManagerIds { get; set; }
    }
    public class UserBasicInfoDto
    {
        public Guid Id { get; set; }  
        public string? Email { get; set; }
        public UserReportsTo? ReportsTo { get; set; }
        public UserReportsTo? GeneralManager { get; set; }
    }
    public class UserFormattedDto : IDto
    {
        public Guid Id { get; set; }
        public string? Name { get; set; }
        public string? Email { get; set; }
        public string? ReportsTo { get; set; }
        public string? GeneralManager { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime? CreatedOn { get; set; }
        public DateTime? LastModifiedOn { get; set; }
    }
    public class UserDetailsWithRoleDto : IDto
    {
        public Guid Id { get; set; }
        public string? UserName { get; set; }
        public string? FirstName { get; set; }
        public string? LastName { get; set; }
        public bool IsActive { get; set; } = true;
        public List<UserRoleDto>? UserRoles { get; set; }
    }
}

