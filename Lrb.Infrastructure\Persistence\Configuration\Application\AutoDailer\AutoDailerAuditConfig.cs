﻿using Finbuckle.MultiTenant.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace Lrb.Infrastructure.Persistence.Configuration.Application.AutoDialer
{
    public class AutoDialerAuditConfig : IEntityTypeConfiguration<Domain.Entities.AutoDialerAudit>
    {
        public void Configure(EntityTypeBuilder<Domain.Entities.AutoDialerAudit> builder)
        {
            builder.IsMultiTenant();
        }
    }
}
