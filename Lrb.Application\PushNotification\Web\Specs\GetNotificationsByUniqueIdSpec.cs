﻿using Lrb.Application.PushNotification.Web.Dtos;

namespace Lrb.Application.PushNotification.Web.Specs
{
    public class GetNotificationsByUniqueIdSpec : Specification<Domain.Entities.Notification>
    {
        public GetNotificationsByUniqueIdSpec(Guid uniqueId)
        {
            Query.Where(i => !i.IsDeleted && i.UniqueId == uniqueId);
        }
        public GetNotificationsByUniqueIdSpec(List<Guid> uniqueIds)
        {
            Query.Where(i => !i.IsDeleted && i.UniqueId != null && uniqueIds.Contains(i.UniqueId.Value));
        }
    }
    public class GetNotificationsByUniqueIdSpecV1 : Specification<Domain.Entities.Notification, NotificationDto>
    {
        public GetNotificationsByUniqueIdSpecV1(List<Guid> uniqueIds)
        {
            Query.Where(i => !i.IsDeleted && i.UniqueId != null && uniqueIds.Contains(i.UniqueId.Value));
        }
    }
}
