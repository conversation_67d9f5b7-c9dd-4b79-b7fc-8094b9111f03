﻿using Lrb.Application.Project.Web.Requests.Bulk_Upload;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Lrb.Application.Project.Web.Specs
{
    public class BulkUnitUploadSpec : EntitiesByPaginationFilterSpec<BulkUnitUploadTracker>
    {
        public BulkUnitUploadSpec(GetAllBulkUnitTrackerRequest filter, List<Guid> subIds) : base(filter)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy))
                .OrderByDescending(i => i.LastModifiedOn);
        }
    }
    public class BulkUnitUploadCountSpec : Specification<BulkUnitUploadTracker>
    {
        public BulkUnitUploadCountSpec(List<Guid> subIds)
        {
            Query.Where(i => !i.IsDeleted && subIds.Contains(i.CreatedBy));
        }
    }
}
