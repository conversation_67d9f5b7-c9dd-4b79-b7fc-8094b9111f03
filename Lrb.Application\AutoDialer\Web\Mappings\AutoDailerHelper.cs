﻿using Lrb.Application.AutoDialer.Web.Specs;
using Lrb.Application.Common.IVR;
using Lrb.Application.Integration.Web;

namespace Lrb.Application.AutoDialer.Web.Mappings
{
    public class AutoDailerHelper
    {
        public static async Task<bool> InitiateAutoCall(Guid userId, IRepositoryWithEvents<Domain.Entities.AutoDialerAudit> autoDialerRepo,
                                         IRepositoryWithEvents<Domain.Entities.IntegrationAccountInfo> integrationAccRepo,
                                         IRepositoryWithEvents<Domain.Entities.IVRCommonCallLog> ivrCommonCallRepo, IIVRService ivrService)
        {
            try
            {
                // If there’s an ongoing call, mark it as completed
                var onGoingCall = await autoDialerRepo.FirstOrDefaultAsync(
                    new GetAutoDialerAuditByStatusSpec(userId, IVRCallStatus.InProgress));

                if (onGoingCall != null)
                {
                    onGoingCall.CallStatus = IVRCallStatus.Completed;
                    await autoDialerRepo.UpdateAsync(onGoingCall);
                }

                // Pick the next queued call for the user
                var audit = await autoDialerRepo.FirstOrDefaultAsync(
                    new GetAutoDialerAuditByStatusSpec(userId, IVRCallStatus.InQueue));

                if (audit == null)
                    return false;

                // Get integration config
                var integrationInfo = await integrationAccRepo.FirstOrDefaultAsync(
                    new GetIntegrationAccountInfoWithOutBoundSpec());

                // Prepare call request
                var c2cDto = new ClickToCallCommonDto
                {
                    UserId = userId,
                    DestinationNumber = audit.CallTo,
                    AgentNumber = audit.CallFrom,
                    LeadId = audit.LeadId
                };

                // Trigger outbound call
                var response = await ivrService.AutoDailerC2CAsync(
                    c2cDto,
                    integrationInfo?.IVROutboundConfiguration ?? new());

                // Update audit record
                audit.CallStatus = IVRCallStatus.InProgress;
                audit.CallStarted = DateTime.UtcNow;

                // Create call log
                var ivrCallLog = new IVRCommonCallLog
                {
                    Id = Guid.NewGuid(),
                    CallId = response?.Data?.CallId ?? string.Empty,
                    AccountId = integrationInfo?.IVROutboundConfiguration?.Id ?? Guid.Empty,
                    LeadId = audit.LeadId,
                    CustomerNumber = audit.CallTo,
                    AgentName = audit.CallFrom
                };

                audit.IVRCommonCallLogId = ivrCallLog.Id;

                // Save call log and update audit
                await ivrCommonCallRepo.AddAsync(ivrCallLog);
                await autoDialerRepo.UpdateAsync(audit);

                // If we successfully initiated at least one call, return true
                return true;
            }
            catch (Exception ex)
            {
                // Optional: log the exception here
                return false;
            }
        }

    }
}