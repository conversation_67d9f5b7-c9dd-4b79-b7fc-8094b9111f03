﻿namespace Lrb.Application.Property.Web
{
    public class PropertyByIdSpec : Specification<Lrb.Domain.Entities.Property>
    {
        public PropertyByIdSpec(Guid id) =>
            Query.Where(p => !p.IsDeleted && p.Id == id)
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.OwnerDetails)
            .Include(i => i.Dimension)
            .Include(i => i.TagInfo)
            .Include(i => i.Attributes)
            .Include(i => i.Amenities)
            .Include(i => i.Project)
            .Include(i => i.PropertyAssignments)
            .Include(i => i.ListingSources)
            .Include(i => i.ListingSourceAddresses)
            .ThenInclude(i => i.ListingSource)
            .Include(i => i.Galleries.Where(j => !j.IsDeleted))
            .Include(i => i.TenantContactInfo)
            .Include(i=>i.PropertyOwnerDetails)
            .Include(i => i.Compliance);
    }

    public class GetActivePropertyByIdSpec : Specification<Lrb.Domain.Entities.Property>
    {
        public GetActivePropertyByIdSpec(Guid id) =>
            Query.Where(p => !p.IsDeleted && p.Status == PropertyStatus.Active && p.Id == id)
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.PropertyOwnerDetails)
            .Include(i => i.Dimension)
            .Include(i => i.TagInfo)
            .Include(i => i.Attributes)
            .Include(i => i.Amenities)
            .Include(i => i.Project)
            .Include(i => i.PropertyAssignments)
            .Include(i => i.ListingSourceAddresses)
            .ThenInclude(i => i.ListingSource)
            .Include(i => i.Galleries.Where(j => !j.IsDeleted))
            .Include(i => i.TenantContactInfo);
    }

    public class PropertyBySerialNoSpec : Specification<Lrb.Domain.Entities.Property>
    {
        public PropertyBySerialNoSpec(string serialNo) =>
            Query.Where(p => !p.IsDeleted && p.SerialNo == serialNo)
            .Include(i => i.Address)
                .ThenInclude(i => i.Location)
                    .ThenInclude(i => i.Zone)
                       .ThenInclude(i => i.City)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.PropertyOwnerDetails)
            .Include(i => i.Dimension)
            .Include(i => i.TagInfo)
            .Include(i => i.Attributes)
            .Include(i => i.Amenities)
            .Include(i => i.Project)
            .Include(i => i.PropertyAssignments.Where(j=>!j.IsDeleted))
            .Include(i => i.TenantContactInfo)
            .Include(i => i.Galleries.Where(j => !j.IsDeleted))
            .Include(i => i.Compliance);
    }
    public class PropertyBySerialNoSpecV2 : Specification<Lrb.Domain.Entities.Property>
    {
        public PropertyBySerialNoSpecV2(string serialNo) =>
            Query.Where(p => !p.IsDeleted && p.SerialNo == serialNo)
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.Dimension);
    }

    public class PropertyByRefrenceNoSpecV2 : Specification<Lrb.Domain.Entities.Property>
    {
        public PropertyByRefrenceNoSpecV2(string serialNo) =>
            Query.Where(p => !p.IsDeleted && p.SerialNo == serialNo)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.Attributes)
            .Include(i => i.Dimension)
            .Include(i => i.Project);
    }
    public class GetPropertyAssignmentSpec : Specification<Domain.Entities.Property>
    {
        public GetPropertyAssignmentSpec(Guid id) =>
           Query.Where(p => !p.IsDeleted && p.Id == id)
           .Include(i => i.PropertyAssignments);          
    }

    public class PropertyByIdSpecV2 : Specification<Lrb.Domain.Entities.Property>
    {
        public PropertyByIdSpecV2(Guid id) =>
            Query.Where(p => !p.IsDeleted && p.Id == id)
            .Include(i => i.Address)
            .Include(i => i.MonetaryInfo)
            .Include(i => i.PropertyType)
            .Include(i => i.Dimension)
            .Include(i => i.Attributes)
            .Include(i => i.Project)
            .Include(i => i.PropertyAssignments.Where(j => j.IsCurrentlyAssigned == true))
            .Include(i => i.ListingSources)
            .Include(i => i.ListingSourceAddresses)
            .ThenInclude(i => i.ListingSource)
            .Include(i => i.Galleries.Where(j => !j.IsDeleted))
            .Include(i => i.PropertyOwnerDetails)
            .Include(i => i.Compliance);
    }

    public class PropertyAmenitiesByIdSpecV2 : Specification<PropertyAmenity>
    {
        public PropertyAmenitiesByIdSpecV2(Guid id) =>
            Query.Where(i => i.PropertyId == id);
    }
}
